<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\GeneratePDF;
use App\Traits\GetEncryptedFile;
use App\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;

class GeneralNote extends Model
{
    use Encryptable;
    use LogsActivity;
    use GetEncryptedFile;
    use GeneratePDF;
    use SoftDeletes;

    protected $encrypted = [
        'title',
        'notes',
        'sign',
        'filename',
        'notes_html',
        'filenames',
        'cancel_note'
    ];

    protected $fillable = [
        'title',
        'notes',
        'notes_html',
        'filename',
        'client_id',
        'sign',
        'signed_at',
        'sign_by_id',
        'important',
        'filenames',
        'created_at',
        'is_cancelled',
        'cancel_note',
        'cancelled_by_id',
        'cancelled_at'
    ];

    protected $casts = [
        'important' => 'boolean',
        'filenames' => 'array',
    ];

    public function client()
    {
        return $this->belongsTo('App\Client');
    }

    public function file()
    {
        return $this->morphOne(File::class, 'fileable')->latest();
    }
    public function files()
    {
        return $this->morphMany(File::class, 'fileable')->latest();
    }

    public function file_batches()
    {
        return $this->morphMany(ClientFileBatch::class, 'signable');
    }

    public function signed_by()
    {
        return $this->belongsTo('App\User', 'sign_by_id')->withTrashed()->without('company')->select(['id', 'first_name', 'last_name', 'email']);
    }
    public function cancelled_by()
    {
        return $this->belongsTo('App\User', 'cancelled_by_id')->withTrashed()->without('company')->select(['id', 'first_name', 'last_name', 'email']);
    }
    protected static $logAttributes = [];

    protected static $logName = 'general_note';

    public function prescriptions()
    {
        return $this->morphToMany(
            ClientPrescription::class,
            'prescriptionable',
            'prescriptionables',
            'prescriptionable_id',
            'prescription_id',
            'id',
        );
    }

    public function getDescriptionForEvent(string $eventName): string
    {
        $client_user = $this->client()->first();

        return $client_user->first_name . ' ' . $client_user->last_name . "'s Genereal Note :subject.title has been {$eventName} by :causer.first_name :causer.last_name";
    }

    public function getSignAttribute($value)
    {
        if ($value) {
            $data = str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl(Crypt::decrypt($value)));
            return Crypt::encrypt($data);
        }
        return $value;
    }

    public static function downloadPDF(GeneralNote $note, $is_twelve_hours)
    {
        return self::downloadFromView('exports.client.general_note', [
            'client' => $note->client,
            'note' => $note,
            'is_twelve_hours' => $is_twelve_hours,
            'company' => $note->client->company,
        ]);
    }
}
