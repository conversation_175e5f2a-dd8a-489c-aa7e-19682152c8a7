<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\GetEncryptedFile;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyCategory extends Model
{
    use HasFactory, GetEncryptedFile, Encryptable;

    protected $encrypted = [
        'name'
    ];
    protected $fillable = [
        'company_id',
        'name',
        'is_active',
        'order',
        'group_booking',
        'is_hidden'
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
    public function services()
    {
        return $this->hasMany(CompanyService::class, 'category_id');
    }

    public function bookings()
    {
        return $this->hasManyThrough(CompanyBooking::class, CompanyService::class, 'category_id', 'service_id', 'id', 'id');
    }
}