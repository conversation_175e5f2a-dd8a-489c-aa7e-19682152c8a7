<?php

namespace App;

use App\Company;
use App\Traits\Encryptable;
use App\CompanyReceiptItem;
use App\CompanyProductCategory;
use App\Traits\POS\HasUniqueCode;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CompanyProduct extends Model
{
    use HasFactory, SoftDeletes, Encryptable, HasUniqueCode;

    //TABLE
    public $table = 'company_products';

    //FILLABLES
    protected $fillable = [
        'company_id',
        'category_id',

        'product_code',
        'barcode',

        'name',
        'description',

        'selling_price',
        'base_price',
        'tax_information',
        'stock',

        'deleted_at',
    ];

    protected $encrypted = [
        'barcode',

        'name',
        'description',

        'selling_price',
        'base_price',
        'tax_information',
        'stock',
    ];

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        // auto-sets values on creation
        static::creating(function ($query) {
            $value = self::getIncrementalValueModel($query->company_id);

            $query->product_code = $value->item_sequence_number;

            $value->item_sequence_number += 1;
            $value->save();
        });
    }

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function category()
    {
        return $this->belongsTo(CompanyProductCategory::class, 'category_id', 'id');
    }

    public function receipt_items(): MorphMany
    {
        return $this->morphMany(CompanyReceiptItem::class, 'receiptable');
    }

    // public function refunds(): MorphMany
    // {
    //     return $this->morphMany(CompanyReceiptRefundItem::class, 'refundable');
    // }


    //ATTRIBUTES
    //public function getExampleAttribute()
    //{
    //    return $data;
    //}
}