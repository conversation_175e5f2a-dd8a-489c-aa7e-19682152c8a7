<?php

namespace App;

use App\Traits\Encryptable;
use App\Traits\GetEncryptedFile;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;

class ClientFileBatch extends Model
{
    use HasFactory, Encryptable, GetEncryptedFile;

    //TABLE
    public $table = 'client_files_batches';


    protected $encrypted = [
        'sign_path',
        'filenames',
        'description'
    ];
    //FILLABLES
    protected $fillable = [
        'client_id',
        'signed_by_id',
        'sign_path',
        'signed_at',
        'signable_type',
        'signable_id',
        'filenames',
        'description'
    ];

    //HIDDEN
    protected $hidden = [];

    //APPENDS
    protected $appends = [];

    //WITH
    protected $with = [];

    //CASTS
    protected $casts = [
        'filenames' => 'array'
    ];

    //RULES
    public static $getListRules = [];

    //RELATIONSHIPS
    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    public function signed_by()
    {
        return $this->belongsTo(User::class, 'signed_by_id')->withTrashed()->without('company')->select(['id', 'first_name', 'last_name', 'email']);
    }

    public function files()
    {
        return $this->morphMany('App\File', 'fileable');
    }

    public function signable()
    {
        return $this->morphTo();
    }

    //ATTRIBUTES
    public function getSignPathAttribute($value)
    {
        if ($value) {
            $data = str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl(Crypt::decrypt($value)));
            return Crypt::encrypt($data);
        }
        return $value;
    }
}
