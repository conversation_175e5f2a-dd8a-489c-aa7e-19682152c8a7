<?php

namespace App;

use Throwable;
use App\Company;
use App\Setting;
use Carbon\Carbon;
use App\UserDevice;
use App\UserService;
use App\UserTimeSlot;
use App\CompanyBooking;
use App\CompanyService;
use App\Traits\useDevice;
use App\Traits\Encryptable;
use App\Traits\LogsActivity;
use App\Traits\BookingManager;
use App\CompanyProductCategory;
use App\Models\UserCalendar;
use App\Models\UserOauthToken;
use App\Traits\TimeZoneManager;
use App\Traits\GetEncryptedFile;
use Illuminate\Support\Facades\Hash;
use App\Notifications\VerifyApiEmail;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Support\Facades\Cache;
use Tymon\JWTAuth\Contracts\JWTSubject;

class User extends Authenticatable implements MustVerifyEmail, JWTSubject
{
    use Notifiable, HasApiTokens, Encryptable, SoftDeletes, LogsActivity, useDevice, GetEncryptedFile;

    protected const EMAIL_PENDING = 'pending',
        EMAIL_VERIFIED = 'verified',
        EMAIL_REJECTED = 'rejected';

    protected static $logAttributes = [];

    protected static $logName = 'user';

    public function getDescriptionForEvent(string $eventName): string
    {
        return ":subject.first_name :subject.last_name user has been {$eventName} by :causer.first_name :causer.last_name";
    }

    const ADMIN = 'admin', USER = 'user', MASTER_ADMIN = 'master_admin';

    protected $encrypted = [
        'title',
        'first_name',
        'last_name',
        'user_role',
        'profile_photo',
        'mobile_number',
        'street_address',
        'zip_code',
        'city',
        'state',
        'country',
        'booking_description',
        'country_code',
        'personal_id'
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'title',
        'first_name',
        'last_name',
        'user_role',
        'is_active',
        'profile_photo',
        'company_id',
        'email',
        'mobile_number',
        'street_address',
        'zip_code',
        'city',
        'state',
        'country',
        'email_verified_at',
        'password',
        'last_login_at',
        'is_for_booking',
        'booking_description',
        'country_code',
        'is_booking_on',
        'is_record_on',
        'invalid_password_tries',
        'is_account_lock',
        'account_unlock_at',
        'password_last_change_at',
        'personal_id',
        'firebase_tokens',
        'is_pos_on',
        'is_management_on',
        'is_bankid_verified'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
        'firebase_tokens'
    ];

    protected $with = [
        'company'
    ];

    protected $casts = [
        'is_record_on' => 'boolean',
        'is_booking_on' => 'boolean',
        'is_pos_on' => 'boolean',
        'is_management_on' => 'boolean',
        'is_account_lock' => 'boolean',
        'is_active' => 'boolean',

        'email_verified_at' => 'datetime',
        'firebase_tokens' => 'array',
    ];

    protected $appends = [
        'email_verification',
    ];

    public function isUser()
    {
        return $this->user_role == self::USER;
    }

    public function isAdmin()
    {
        return $this->user_role == self::ADMIN;
    }

    public function isSuperAdmin()
    {
        return $this->email == $this->company->email;
    }

    public function isMasterAdmin()
    {
        return $this->user_role == self::MASTER_ADMIN;
    }

    public function fullName()
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    public function supports()
    {
        return $this->hasMany('App\Support');
    }

    public function file()
    {
        return $this->morphOne('App\File', 'fileable')->latest();
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
    public function companies()
    {
        return $this->belongsToMany(Company::class, UserCompany::class)->withPivot('id', 'price', 'invite_status');
    }

    public function company_connections()
    {
        return $this->hasMany(UserCompany::class, 'user_id', 'id');
    }
    public function accepted_companies()
    {
        return $this->belongsToMany(Company::class, UserCompany::class)->withPivot('id', 'price', 'invite_status')->wherePivot('invite_status', UserCompany::ACCEPTED);
    }

    public function bills()
    {
        return $this->hasMany(UserBilling::class);
    }

    public function accesses()
    {
        return $this->belongsToMany('App\Client', 'user_client');
    }

    public function client_treatments()
    {
        return $this->hasMany('App\ClientTreatment');
    }

    public function scopeForBooking($query)
    {
        return $query->where('is_booking_on', true);
    }

    public function scopeForRecord($query)
    {
        return $query->where('is_record_on', true);
    }

    public function scopeForPos($query)
    {
        return $query->where('is_pos_on', true);
    }

    public function scopeVerifiedEmail($query)
    {
        return $query->whereNotNull('email_verified_at');
    }

    public function scopeVerifiedCompany($query)
    {
        return $query->whereHas('company', fn($q) => $q->where('verification', Company::COMPANY_VERIFIED));
    }

    public function company_categories()
    {
        return $this->hasManyThrough(CompanyProductCategory::class, Company::class);
    }

    /**
     * Find the user instance for the given username.
     *
     * @param  string  $username
     * @return \App\User
     */
    public function findForPassport($username)
    {
        return $this->where('email', $username)->first();
    }

    /**
     * Validate the password of the user for the Passport password grant.
     *
     * @param  string  $password
     * @return bool
     */
    public function validateForPassportPasswordGrant($password)
    {
        return Hash::check($password, $this->password);
    }

    public function getEmailVerificationAttribute()
    {
        if (!$this->email_verified_at) {
            return self::EMAIL_PENDING;
        }

        if ($this->email_verified_at == now()->createFromTimestamp(1)) {
            return self::EMAIL_REJECTED;
        }

        return self::EMAIL_VERIFIED;
    }

    public function hasRejectedEmail()
    {
        return $this->email_verification == self::EMAIL_REJECTED;
    }

    public function hasVerifiedEmail()
    {
        return $this->email_verification == self::EMAIL_VERIFIED;
    }

    public function rejectEmail()
    {
        $this->email_verified_at = now()->createFromTimestamp(1);
    }

    public function getProfilePhotoAttribute($value)
    {
        if ($value) {
            $data = str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl(Crypt::decrypt($value)));
            return Crypt::encrypt($data);
        }
        return $value;
    }

    public function services()
    {
        return $this->hasManyThrough(CompanyService::class, UserService::class, 'user_id', 'id', 'id', 'service_id');
    }

    public function assigned_prescriptions()
    {
        return $this->hasMany(ClientPrescription::class, 'assign_to_id');
    }

    public function bookings()
    {
        return $this->hasMany(CompanyBooking::class, 'user_id', 'id');
    }

    public function user_time_slots()
    {
        return $this->hasMany(UserTimeSlot::class, 'user_id', 'id');
    }

    public function oauth_tokens()
    {
        return $this->hasMany(UserOauthToken::class, 'user_id', 'id');
    }

    public function calendars()
    {
        return $this->hasMany(UserCalendar::class, 'user_id', 'id');
    }

    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [
            'email' => $this->email,
        ];
    }

    static function getUnAvailableDays(Company $company, Carbon $start_date, Carbon $end_date, CompanyService $service = null, $user_ids = null)
    {

        //getting temp data from available slots method
        $temp_data = User::getAvailableSlots($company, $start_date, $end_date, $service, $user_ids);


        //default array to store unavailable days
        $unavailable_days = [];
        foreach ($temp_data as $t_data) {
            if (count($t_data['slots']) <= 0) {
                array_push($unavailable_days, $t_data['date']);
            }
        }

        return $unavailable_days;
    }

    static function getAvailablePractitioners(Company $company, Carbon $start_date, Carbon $end_date, CompanyService $service = null)
    {
        //getting practitioner for particular company
        $practitioners = User::where('company_id', $company->id);

        //applying filter for practitioner that's available for booking and also active
        $practitioners = $practitioners->where('is_active', 1);
        $practitioners = $practitioners->where('is_booking_on', 1);
        $practitioners = $practitioners->verifiedEmail();

        //NEW LOGIC WITHOUT BUSINESS HOURS
        $practitioners = $practitioners->whereHas('user_time_slots', function ($query) use ($start_date, $end_date, $service) {
            $query = $query
                ->where('type', UserTimeSlot::AVAILABLE)
                ->where(function ($q) use ($start_date, $end_date) {
                    $q = $q->where(function ($q) use ($start_date, $end_date) {
                        $q = $q->where('start_at', '>=', $start_date)->where('start_at', '<=', $end_date);
                    })->orWhere(function ($q) use ($start_date, $end_date) {
                        $q = $q->where('end_at', '>=', $start_date)->where('end_at', '<=', $end_date);
                    })->orWhere(function ($q) use ($start_date, $end_date) {
                        $q = $q->where('start_at', '<=', $start_date)->where('end_at', '>=', $end_date);
                    });
                });
            if ($service && $service->category &&  $service->category->group_booking && $service->group_quantity > 0) {
                $query->whereHas('services', function ($query) use ($service) {
                    $query = $query->whereIn('company_services.id', [$service->id]);
                });
            } else {
                $query->where(function ($query) use ($service) {
                    $query = $query->whereHas('services', function ($query) use ($service) {
                        $query = $query->whereIn('company_services.id', [$service->id]);
                    })->orWhere('is_for_all_services', 1);
                });
            }
        });

        if ($service && $service->category &&  $service->category->group_booking && $service->group_quantity > 0) {
            //getting only those practitioners who doesn't have any booking for given time
            $practitioners  = $practitioners->whereDoesntHave('bookings', function ($query) use ($start_date, $end_date, $service) {
                //checking for booking that's verified
                $query = $query->where('is_verified', 1)
                    //checking for booking that not cancelled
                    ->where('is_cancelled', 0)
                    //checking for booking that's between given time period
                    ->where(function ($q) use ($start_date, $end_date) {
                        $q = $q->where(function ($q) use ($start_date, $end_date) {
                            $q = $q->where('start_at', '>=', $start_date)->where('start_at', '<', $end_date);
                        })->orWhere(function ($q) use ($start_date, $end_date) {
                            $q = $q->where('end_at', '>', $start_date)->where('end_at', '<=', $end_date);
                        })->orWhere(function ($q) use ($start_date, $end_date) {
                            $q = $q->where('start_at', '<', $start_date)->where('end_at', '>=', $end_date);
                        });
                    })
                    ->withCount('active_clients')
                    ->having('active_clients_count', '>=', $service->group_quantity);
            });
        } else {
            //getting only those practitioners who doesn't have any booking for given time
            $practitioners  = $practitioners->whereDoesntHave('bookings', function ($query) use ($start_date, $end_date) {
                //checking for booking that's verified
                $query = $query->where('is_verified', 1)
                    //checking for booking that not cancelled
                    ->where('is_cancelled', 0)
                    //checking for booking that's between given time period
                    ->where(function ($q) use ($start_date, $end_date) {
                        $q = $q->where(function ($q) use ($start_date, $end_date) {
                            $q = $q->where('start_at', '>=', $start_date)->where('start_at', '<', $end_date);
                        })->orWhere(function ($q) use ($start_date, $end_date) {
                            $q = $q->where('end_at', '>', $start_date)->where('end_at', '<=', $end_date);
                        })->orWhere(function ($q) use ($start_date, $end_date) {
                            $q = $q->where('start_at', '<', $start_date)->where('end_at', '>=', $end_date);
                        });
                    });
            });
        }

        //returning the practitioners that's available for given time period
        return $practitioners;
    }

    static function getNewActiveDays(Company $company, Carbon $start_date, Carbon $end_date, CompanyService $service = null, $user_ids = null)
    {
        //default array to store unavailable days
        $unavailable_days = [];

        //getting the practitioners that's available for booking section
        $practitioners = User::where('company_id', $company->id)->where('is_booking_on', 1);

        // filter practitioners by verified email
        $practitioners = $practitioners->verifiedEmail();

        //applying filter for service
        if ($service) {
            $practitioners = $practitioners->whereHas('services', function ($query) use ($service) {
                $query = $query->where('company_services.id', $service->id);
            });
        }

        //applying filter for users_ids(if any)
        if ($user_ids && is_array($user_ids)) {
            $practitioners = $practitioners->whereIn('id', $user_ids);
        }

        //getting the final list of practitioners
        $practitioners =  $practitioners->get();

        //default data array to store the slots in
        $data = [];
        if ($start_date && $end_date) {

            //temp date to go throw each dates
            $temp_date = $start_date;
            $practitioner_ids = $practitioners->pluck('id')->toArray();
            //loop that will go throw each dates and check slots in it
            while ($temp_date->lessThanOrEqualTo($end_date)) {

                //decide which WEEKDAY it is
                $week_day = BookingManager::getWeekDay($temp_date);
                //checking is practitioner ids is empty then directly making slots empty
                if (count($practitioner_ids) <= 0) {
                    array_push($unavailable_days, [
                        "date" => $temp_date->format('Y-m-d'),
                        "note" => null,
                    ]);
                    $temp_date = $temp_date->addDay();
                    continue;
                }
                //NEW LOGIC WITHOUT BUSINESS HOURS
                $user_time_slot = UserTimeSlot::whereIn('user_id', $practitioner_ids)
                    ->where('type', UserTimeSlot::AVAILABLE);
                if ($service) {
                    if ($service->category && $service->category->group_booking) {
                        $user_time_slot = $user_time_slot->where(function ($query) use ($service) {
                            $query = $query
                                ->whereHas('services', function ($query) use ($service) {
                                    $query = $query->where('company_services.id', $service->id);
                                });
                        });
                    } else {
                        $user_time_slot = $user_time_slot->where(function ($query) use ($service) {
                            $query = $query
                                ->where('is_for_all_services', 1)
                                ->orWhereHas('services', function ($query) use ($service) {
                                    $query = $query->where('company_services.id', $service->id);
                                });
                        });
                    }
                }
                $user_time_slot = $user_time_slot->whereDate('start_at', '<=', $temp_date)
                    ->whereDate('end_at', '>=', $temp_date)
                    ->first();


                if (!$user_time_slot) {
                    array_push($unavailable_days, [
                        "date" => $temp_date->format('Y-m-d'),
                        "note" => null,
                    ]);
                }

                //adding 1 day into temp_date to go throw next date
                $temp_date = $temp_date->addDay();
            }
        }

        return $unavailable_days;
    }

    static function getAvailableSlots(
        Company $company,
        Carbon $start_date,
        Carbon $end_date,
        CompanyService $service = null,
        $user_ids = null,
        $time_zone = null
    ) {
        //getting the default time increments, in case of service doesn't have duration
        $time_increments = Setting::getSetting($company, Setting::TIME_INCREMENTS);

        //getting the practitioners that's available for booking section
        $practitioners = User::where('company_id', $company->id)->where('is_booking_on', 1);

        // filter practitioners by verified email
        $practitioners = $practitioners->verifiedEmail();

        //applying filter for service
        if ($service) {
            $practitioners = $practitioners->whereHas('services', function ($query) use ($service) {
                $query = $query->where('company_services.id', $service->id);
            });
        }

        $is_group_service = false;
        $max_booking_per_slot = 0;
        if ($service->category && $service->category->group_booking) {
            $is_group_service = true;
            $max_booking_per_slot = $service->group_quantity;
        }

        //applying filter for users_ids(if any)
        if ($user_ids && is_array($user_ids)) {
            $practitioners = $practitioners->whereIn('id', $user_ids);
        }

        //getting the final list of practitioners
        $practitioners =  $practitioners->get();
        $all_practitioner_ids =  $practitioners->pluck('id')->toArray();

        $now = TimeZoneManager::getTimeZoneCarbonNow($time_zone);
        $minimum_lead_time_setting = Setting::getSetting($company, Setting::MINIMUM_LEAD_TIME);
        $maximum_lead_time_setting = Setting::getSetting($company, Setting::MAXIMUM_LEAD_TIME);
        $minimum_lead_time = $minimum_lead_time_setting->value;
        $maximum_lead_time = $maximum_lead_time_setting->value;

        //default data array to store the slots in
        $data = [];
        if ($start_date && $end_date) {

            //temp date to go throw each dates
            $temp_date = $start_date;
            $practitioner_ids = $practitioners->pluck('id')->toArray();

            //loop that will go throw each dates and check slots in it
            while ($temp_date->lessThanOrEqualTo($end_date)) {




                //decide which WEEKDAY it is
                $week_day = BookingManager::getWeekDay($temp_date);

                //deciding duration of each slot based on either service or time increment
                $duration = $time_increments->value;
                if ($service) {
                    $duration = $service->duration;
                    if ($service->time_margin > 0) {
                        $duration = $duration + $service->time_margin;
                    }
                }

                //checking is practitioner ids is empty then directly making slots empty
                if (count($practitioner_ids) <= 0) {
                    array_push($data, [
                        'date' => $temp_date->format('Y-m-d'),
                        'slots' => [],
                    ]);
                    $temp_date = $temp_date->addDay();
                    continue;
                }

                //checking if this date is before the current time, if yes then ignoring it and continue
                $td = Carbon::parse($temp_date);
                if ($now->greaterThan($td->addDay())) {
                    array_push($data, [
                        'date' => $temp_date->format('Y-m-d'),
                        'slots' => [],
                    ]);
                    $temp_date = $temp_date->addDay();
                    continue;
                }

                //checking if this date is after the maximum lead time, if yes then ignoring it and continue
                $td1 = Carbon::parse($temp_date);
                $td2 = Carbon::parse($now);
                if ($td2->addMinutes($maximum_lead_time)->lessThan($td1)) {
                    array_push($data, [
                        'date' => $temp_date->format('Y-m-d'),
                        'slots' => [],
                    ]);
                    $temp_date = $temp_date->addDay();
                    continue;
                }

                //NEW LOGIC WITHOUT BUSINESS HOURS
                //default slot array to store slots data
                $slots = [];

                //exact date-time between which we have check for available slots
                $start_date_to_check = Carbon::parse($temp_date);
                $end_date_to_check = Carbon::parse($temp_date)->endOfDay();
                $user_available_time_slots = UserTimeSlot::where('type', UserTimeSlot::AVAILABLE)
                    ->whereIn('user_id', $practitioner_ids);

                if ($service) {
                    if ($is_group_service) {
                        $user_available_time_slots = $user_available_time_slots->where(function ($query) use ($service) {
                            $query = $query
                                ->whereHas('services', function ($query) use ($service) {
                                    $query = $query->where('company_services.id', $service->id);
                                });
                        });
                    } else {
                        $user_available_time_slots = $user_available_time_slots->where(function ($query) use ($service) {
                            $query = $query
                                ->where('is_for_all_services', 1)
                                ->orWhereHas('services', function ($query) use ($service) {
                                    $query = $query->where('company_services.id', $service->id);
                                });
                        });
                    }
                }


                $user_available_time_slots = $user_available_time_slots->where('start_at', '>=', $start_date_to_check)
                    ->where('end_at', '<=', $end_date_to_check)
                    ->orderBy('start_at', 'asc')
                    ->get();

                $practitioner_ids = $user_available_time_slots->pluck('user_id')->unique()->toArray();

                if (count($user_available_time_slots) <= 0) {
                    //if available time slots is empty then adding empty slots
                    array_push($data, [
                        'date' => $temp_date->format('Y-m-d'),
                        'slots' => [],
                    ]);
                } else {

                    $all_booked_bookings = Cache::remember('company_' . $company->id . '_booking_' . $start_date_to_check->format('Y-m-d') .  '_data', now()->addDay(), function () use ($company, $start_date_to_check, $end_date_to_check, $practitioner_ids) {
                        $all_booked_bookings = CompanyBooking::where('company_id', $company->id)
                            ->where('is_verified', 1)
                            ->where('is_cancelled', 0)
                            ->with('clients')
                            ->where('start_at', '>=', Carbon::parse($start_date_to_check))
                            ->where('end_at', '<=', Carbon::parse($end_date_to_check))
                            // ->whereIn('user_id', $practitioner_ids)
                            ->orderBy('start_at', 'asc')
                            ->get();
                        return $all_booked_bookings;
                    });


                    //default time array to store times
                    $times = [];
                    foreach ($user_available_time_slots as $user_available_time_slot) {

                        /*
                            first checking on the bookings and removing all the durations thats already booked for given practitioner_ids
                        */
                        //getting already booked Booking between the date-times
                        // $already_booked_bookings = CompanyBooking::where('company_id', $company->id)
                        //     ->where('is_verified', 1)
                        //     ->where('is_cancelled', 0)
                        //     ->with('clients')
                        //     // ->where('start_at', '>=', Carbon::parse($user_available_time_slot->start_at))
                        //     // ->where('end_at', '<=', Carbon::parse($user_available_time_slot->end_at))
                        //     ->where(function ($q) use ($user_available_time_slot) {
                        //         $q = $q->where(function ($q) use ($user_available_time_slot) {
                        //             $q = $q->where('start_at', '>=', Carbon::parse($user_available_time_slot->start_at))->where('start_at', '<', Carbon::parse($user_available_time_slot->end_at));
                        //         })->orWhere(function ($q) use ($user_available_time_slot) {
                        //             $q = $q->where('end_at', '>', Carbon::parse($user_available_time_slot->start_at))->where('end_at', '<=', Carbon::parse($user_available_time_slot->end_at));
                        //         })->orWhere(function ($q) use ($user_available_time_slot) {
                        //             $q = $q->where('start_at', '<', Carbon::parse($user_available_time_slot->start_at))->where('end_at', '>=', Carbon::parse($user_available_time_slot->end_at));
                        //         });
                        //     })
                        //     ->whereIn('user_id', $practitioner_ids)
                        //     ->orderBy('start_at', 'asc')
                        //     ->get();


                        $already_booked_bookings = collect($all_booked_bookings)->filter(function ($booking) use ($user_available_time_slot, $practitioner_ids) {

                            if (!in_array($booking->user_id, $practitioner_ids)) {
                                return false;
                            }
                            $startAt = Carbon::parse($user_available_time_slot->start_at);
                            $endAt = Carbon::parse($user_available_time_slot->end_at);

                            $bookingStart = Carbon::parse($booking->start_at);
                            $bookingEnd = Carbon::parse($booking->end_at);

                            // Condition 1: Booking starts within the user available time slot
                            $condition1 = $bookingStart->gte($startAt) && $bookingStart->lt($endAt);

                            // Condition 2: Booking ends within the user available time slot
                            $condition2 = $bookingEnd->gt($startAt) && $bookingEnd->lte($endAt);

                            // Condition 3: Booking fully overlaps the user available time slot
                            $condition3 = $bookingStart->lt($startAt) && $bookingEnd->gte($endAt);

                            // Return true if any of the conditions match
                            return $condition1 || $condition2 || $condition3;
                        });

                        //NEW LOGIC FOR GROUP SERVICE
                        if ($is_group_service && count($practitioner_ids) == 1) { //in group-service, there can be only 1 practitioner
                            //DO SOMETHING IF NEEDED
                            if ($max_booking_per_slot <= 0) {
                                //TODO::should alert use that service doesn't have any slot by default.
                            }
                            //temp start date to start with
                            $temp_start_at = Carbon::parse($user_available_time_slot->start_at);

                            //going throw each booking to see if all slots are booked for that booking
                            foreach ($already_booked_bookings as $booking) {

                                //checking if this booking's all slots are full
                                //if yes then storing our temp start time and booking start time as available and skipping current bookings duration
                                //(thus making it unavailable)
                                if (count($booking->active_clients) >= $max_booking_per_slot) {
                                    if ($temp_start_at->lessThan(Carbon::parse($booking->start_at))) {
                                        array_push($times, [
                                            "start_time" => $temp_start_at,
                                            "end_time" => Carbon::parse($booking->start_at),
                                        ]);
                                    }
                                    // $temp_start_at = Carbon::parse($booking->end_at);
                                    if (Carbon::parse($booking->end_at)->greaterThan(Carbon::parse($user_available_time_slot->end_at))) {
                                        $temp_start_at = Carbon::parse($user_available_time_slot->end_at);
                                    } else {
                                        $temp_start_at = Carbon::parse($booking->end_at);
                                    }
                                }
                            }
                            //after going throw each booking if our temp start time is still not at the end
                            //then making the duration between temp start time and end time is available.
                            if ($temp_start_at != Carbon::parse($user_available_time_slot->end_at)) {
                                array_push($times, [
                                    "start_time" => $temp_start_at,
                                    "end_time" => Carbon::parse($user_available_time_slot->end_at),
                                ]);
                            }
                        } else {
                            //checking if this bookings is for all the practitioners, if not then ignoring it and setting the array to empty
                            //(because if it's not for all practitioners it means some practitioners are still available, so we don't need to look into this booking )
                            $booked_user_ids = array_unique($already_booked_bookings->pluck('user_id')->toArray());
                            if (count($booked_user_ids) != count($practitioner_ids)) {
                                $already_booked_bookings = [];
                            }

                            //if booking is for all the practitioners then going throw each booking to get the available slots
                            //temp start date to start with
                            $temp_start_at = Carbon::parse($user_available_time_slot->start_at);

                            //going throw each booking to see if all the practitioners are booked or not
                            foreach ($already_booked_bookings as $booking) {

                                //getting all the booking thats between current booking's start and end time

                                $temp_already_booked_bookings = $already_booked_bookings->filter(function ($booked_booking) use ($booking) {

                                    if (
                                        Carbon::parse($booked_booking->start_at)->greaterThanOrEqualTo(Carbon::parse($booking->start_at))
                                        &&
                                        Carbon::parse($booked_booking->start_at)->lessThan(Carbon::parse($booking->end_at))
                                    ) {
                                        return true;
                                    }
                                    if (
                                        Carbon::parse($booked_booking->end_at)->greaterThan(Carbon::parse($booking->start_at))
                                        &&
                                        Carbon::parse($booked_booking->end_at)->lessThanOrEqualTo(Carbon::parse($booking->end_at))
                                    ) {
                                        return true;
                                    }
                                    if (
                                        Carbon::parse($booked_booking->start_at)->lessThan(Carbon::parse($booking->start_at))
                                        &&
                                        Carbon::parse($booked_booking->end_at)->greaterThanOrEqualTo(Carbon::parse($booking->end_at))
                                    ) {
                                        return true;
                                    }
                                    if (
                                        Carbon::parse($booked_booking->start_at)->equalTo(Carbon::parse($booking->start_at))
                                        &&
                                        Carbon::parse($booked_booking->end_at)->equalTo(Carbon::parse($booking->end_at))
                                    ) {
                                        return true;
                                    }

                                    return false;
                                });


                                $temp = array_unique($temp_already_booked_bookings
                                    ->pluck('user_id')
                                    ->toArray());



                                //checking if this booking is booked for all the practitioners,
                                //if yes then storing our temp start time and booking start time as available and skipping current bookings duration
                                //(thus making it unavailable)
                                if (count($temp) == count($practitioner_ids)) {

                                    // if ($temp_start_at->lessThan(Carbon::parse($booking->start_at)) && ($duration - 2) < $temp_start_at->diffInMinutes(Carbon::parse($booking->start_at))) {
                                    array_push($times, [
                                        "start_time" => $temp_start_at,
                                        "end_time" => Carbon::parse($booking->start_at),
                                    ]);
                                    // }

                                    // $temp_start_at = Carbon::parse($booking->end_at);
                                    if (Carbon::parse($booking->end_at)->greaterThan(Carbon::parse($user_available_time_slot->end_at))) {
                                        $temp_start_at = Carbon::parse($user_available_time_slot->end_at);
                                    } else {
                                        $temp_start_at = Carbon::parse($booking->end_at);
                                    }
                                }
                            }

                            //after going throw each booking if our temp start time is still not at the end
                            //then making the duration between temp start time and end time is available.
                            if ($temp_start_at != Carbon::parse($user_available_time_slot->end_at)) {
                                array_push($times, [
                                    "start_time" => $temp_start_at,
                                    "end_time" => Carbon::parse($user_available_time_slot->end_at),
                                ]);
                            }
                        }



                        /*
                            now checking on private blocking time and remove
                        */
                        $temp_times = [];
                        $niddle_start_time = null;
                        $niddle_end_time = null;

                        $all_private_slots = Cache::remember('company_' . $company->id . '_private_slots_' . $start_date_to_check->format('Y-m-d') .  '_data', now()->addDay(), function () use ($company, $start_date_to_check, $end_date_to_check, $practitioner_ids) {
                            $all_private_slots = UserTimeSlot::where('type', UserTimeSlot::PRIVATE_SLOT_BLOCKING)
                                ->whereHas('user', function ($query) use ($company) {
                                    $query->where('company_id', $company->id);
                                })
                                ->where('start_at', '>=', Carbon::parse($start_date_to_check))
                                ->where('end_at', '<=', Carbon::parse($end_date_to_check))
                                // ->whereIn('user_id', $practitioner_ids)
                                ->orderBy('start_at', 'asc')
                                ->get();
                            return $all_private_slots;
                        });

                        $all_available_slots = Cache::remember('company_' . $company->id . '_available_slots_' . $start_date_to_check->format('Y-m-d') .  '_data', now()->addDay(), function () use ($company, $start_date_to_check, $end_date_to_check, $practitioner_ids) {
                            $all_private_slots = UserTimeSlot::where('type', UserTimeSlot::AVAILABLE)
                                ->whereHas('user', function ($query) use ($company) {
                                    $query->where('company_id', $company->id);
                                })
                                ->where('start_at', '>=', Carbon::parse($start_date_to_check))
                                ->where('end_at', '<=', Carbon::parse($end_date_to_check))
                                // ->whereIn('user_id', $practitioner_ids)
                                ->orderBy('start_at', 'asc')
                                ->get();
                            return $all_private_slots;
                        });

                        foreach ($times as $time) {
                            // $private_slots = UserTimeSlot::where('type', UserTimeSlot::PRIVATE_SLOT_BLOCKING)
                            //     ->where(function ($q) use ($time) {
                            //         $q = $q->where(function ($q) use ($time) {
                            //             $q = $q->where('start_at', '>=', Carbon::parse($time['start_time']))->where('start_at', '<', Carbon::parse($time['end_time']));
                            //         })->orWhere(function ($q) use ($time) {
                            //             $q = $q->where('end_at', '>', Carbon::parse($time['start_time']))->where('end_at', '<=', Carbon::parse($time['end_time']));
                            //         })->orWhere(function ($q) use ($time) {
                            //             $q = $q->where('start_at', '<', Carbon::parse($time['start_time']))->where('end_at', '>=', Carbon::parse($time['end_time']));
                            //         });
                            //     })
                            //     ->whereIn('user_id', $practitioner_ids)
                            //     ->orderBy('start_at', 'asc')
                            //     ->get();

                            $private_slots = collect($all_private_slots)->filter(function ($private_slot) use ($practitioner_ids, $time) {
                                if (!in_array($private_slot->user_id, $practitioner_ids)) {
                                    return false;
                                }
                                $startAt = Carbon::parse($time['start_time']);
                                $endAt = Carbon::parse($time['end_time']);

                                $private_start = Carbon::parse($private_slot->start_at);
                                $private_end = Carbon::parse($private_slot->end_at);

                                // Condition 1: Booking starts within the user available time slot
                                $condition1 = $private_start->gte($startAt) && $private_start->lt($endAt);

                                // Condition 2: Booking ends within the user available time slot
                                $condition2 = $private_end->gt($startAt) && $private_end->lte($endAt);

                                // Condition 3: Booking fully overlaps the user available time slot
                                $condition3 = $private_start->lt($startAt) && $private_end->gte($endAt);

                                // Return true if any of the conditions match
                                return $condition1 || $condition2 || $condition3;
                            });



                            $niddle_start_time = $time['start_time'];
                            $niddle_end_time = $time['end_time'];
                            foreach ($private_slots as $private_slot) {

                                // getting all the private slots thats between current slot's start and end time
                                $temp = array_unique($private_slots
                                    ->where('start_at', '>=', Carbon::parse($private_slot->start_at))
                                    ->where('end_at', '<=', Carbon::parse($private_slot->end_at))
                                    ->pluck('user_id')
                                    ->toArray());

                                //checking if this private slot is for all practitioners,
                                if (count($temp) == count($practitioner_ids)) {
                                    if ($niddle_start_time->lessThan(Carbon::parse($private_slot->start_at))) {
                                        array_push($temp_times, [
                                            "start_time" => Carbon::parse($niddle_start_time),
                                            "end_time" => Carbon::parse($private_slot->start_at),
                                        ]);
                                    }
                                    $niddle_start_time = Carbon::parse($private_slot->end_at);
                                } else {

                                    // $already_booked_bookings = CompanyBooking::where('company_id', $company->id)
                                    //     ->where('is_verified', 1)
                                    //     ->where('is_cancelled', 0)
                                    //     // ->where('start_at', '>=', Carbon::parse($user_available_time_slot->start_at))
                                    //     // ->where('end_at', '<=', Carbon::parse($user_available_time_slot->end_at))
                                    //     ->where(function ($q) use ($niddle_start_time, $private_slot) {
                                    //         $q = $q->where(function ($q) use ($niddle_start_time, $private_slot) {
                                    //             $q = $q->where('start_at', '>=', Carbon::parse($private_slot->start_at))->where('start_at', '<', Carbon::parse($private_slot->end_at));
                                    //         })->orWhere(function ($q) use ($niddle_start_time, $private_slot) {
                                    //             $q = $q->where('end_at', '>', Carbon::parse($private_slot->start_at))->where('end_at', '<=', Carbon::parse($private_slot->end_at));
                                    //         })->orWhere(function ($q) use ($niddle_start_time, $private_slot) {
                                    //             $q = $q->where('start_at', '<', Carbon::parse($private_slot->start_at))->where('end_at', '>=', Carbon::parse($private_slot->end_at));
                                    //         });
                                    //     })
                                    //     ->whereIn('user_id', $practitioner_ids)
                                    //     ->orderBy('start_at', 'asc')
                                    //     ->get();

                                    $already_booked_bookings = collect($all_booked_bookings)->filter(function ($booking) use ($user_available_time_slot, $practitioner_ids) {
                                        if (!in_array($booking->user_id, $practitioner_ids)) {
                                            return false;
                                        }
                                        $startAt = Carbon::parse($user_available_time_slot->start_at);
                                        $endAt = Carbon::parse($user_available_time_slot->end_at);

                                        $bookingStart = Carbon::parse($booking->start_at);
                                        $bookingEnd = Carbon::parse($booking->end_at);

                                        // Condition 1: Booking starts within the user available time slot
                                        $condition1 = $bookingStart->gte($startAt) && $bookingStart->lt($endAt);

                                        // Condition 2: Booking ends within the user available time slot
                                        $condition2 = $bookingEnd->gt($startAt) && $bookingEnd->lte($endAt);

                                        // Condition 3: Booking fully overlaps the user available time slot
                                        $condition3 = $bookingStart->lt($startAt) && $bookingEnd->gte($endAt);

                                        // Return true if any of the conditions match
                                        return $condition1 || $condition2 || $condition3;
                                    });

                                    $already_booked_booking_ids = array_unique($already_booked_bookings
                                        ->pluck('user_id')
                                        ->toArray());


                                    if (count(array_unique(array_merge($already_booked_booking_ids, $temp))) == count($practitioner_ids)) {

                                        foreach ($already_booked_bookings as $already_booked_booking) {
                                            //getting all the booking thats between current booking's start and end time
                                            $in_temp = array_unique($already_booked_bookings
                                                ->where('start_at', '>=', Carbon::parse($already_booked_booking->start_at))
                                                ->where('end_at', '<=', Carbon::parse($already_booked_booking->end_at))
                                                ->pluck('user_id')
                                                ->toArray());

                                            //checking if this booking is booked for all the practitioners,
                                            //if yes then storing our temp start time and booking start time as available and skipping current bookings duration
                                            //(thus making it unavailable)
                                            if (count(array_unique(array_merge($in_temp, $temp))) == count($practitioner_ids)) {


                                                if ($niddle_start_time->lessThan(Carbon::parse($already_booked_booking->start_at)) && ($duration - 2) < $niddle_start_time->diffInMinutes(Carbon::parse($already_booked_booking->start_at))) {
                                                    array_push($temp_times, [
                                                        "start_time" => $niddle_start_time,
                                                        "end_time" => Carbon::parse($already_booked_booking->start_at),
                                                    ]);
                                                }

                                                // $temp_start_at = Carbon::parse($booking->end_at);
                                                // if (Carbon::parse($booking->end_at)->greaterThan(Carbon::parse($private_slot->end_at))) {
                                                //     $niddle_end_time = Carbon::parse($private_slot->end_at);
                                                // } else {
                                                $niddle_end_time = Carbon::parse($already_booked_booking->end_at);
                                                // }
                                            }
                                        }
                                        if ($niddle_start_time->lessThan(Carbon::parse($private_slot->start_at))) {
                                            array_push($temp_times, [
                                                "start_time" => Carbon::parse($niddle_start_time),
                                                "end_time" => Carbon::parse($private_slot->start_at),
                                            ]);
                                        }
                                        $niddle_start_time = Carbon::parse($private_slot->end_at);
                                    } else {
                                        $temp_practitioner_ids = $practitioner_ids;
                                        unset($temp_practitioner_ids[array_search($private_slot->user_id, $temp_practitioner_ids)]);

                                        // $user_available_time_slots = UserTimeSlot::where('type', UserTimeSlot::AVAILABLE)
                                        // ->where(function ($q) use ($time, $private_slot) {
                                        //     $q = $q->where(function ($q) use ($time, $private_slot) {
                                        //         $q = $q->where('start_at', '>=', Carbon::parse($private_slot->start_at))->where('start_at', '<', Carbon::parse($private_slot->end_at));
                                        //     })->orWhere(function ($q) use ($time, $private_slot) {
                                        //         $q = $q->where('end_at', '>', Carbon::parse($private_slot->start_at))->where('end_at', '<=', Carbon::parse($private_slot->end_at));
                                        //     })->orWhere(function ($q) use ($time, $private_slot) {
                                        //         $q = $q->where('start_at', '<', Carbon::parse($private_slot->start_at))->where('end_at', '>=', Carbon::parse($private_slot->end_at));
                                        //     });
                                        // })
                                        // ->whereIn('user_id', $temp_practitioner_ids)
                                        // ->orderBy('start_at', 'asc')
                                        // ->get();

                                        $user_available_time_slots = collect($all_available_slots)->filter(function ($available_slot) use ($private_slot, $time, $temp_practitioner_ids) {
                                            if (!in_array($available_slot->user_id, $temp_practitioner_ids)) {
                                                return false;
                                            }
                                            $startAt = Carbon::parse($private_slot->start_at);
                                            $endAt = Carbon::parse($private_slot->end_at);

                                            $slot_start = Carbon::parse($available_slot->start_at);
                                            $slot_end = Carbon::parse($available_slot->end_at);

                                            // Condition 1: Booking starts within the user available time slot
                                            $condition1 = $slot_start->gte($startAt) && $slot_start->lt($endAt);

                                            // Condition 2: Booking ends within the user available time slot
                                            $condition2 = $slot_end->gt($startAt) && $slot_end->lte($endAt);

                                            // Condition 3: Booking fully overlaps the user available time slot
                                            $condition3 = $slot_start->lt($startAt) && $slot_end->gte($endAt);

                                            // Return true if any of the conditions match
                                            return $condition1 || $condition2 || $condition3;
                                        });



                                        if (count($user_available_time_slots) <= 0) {
                                            $niddle_start_time = Carbon::parse($private_slot->end_at);
                                        }
                                    }
                                }
                            }

                            if ($niddle_start_time != $niddle_end_time) {
                                array_push($temp_times, [
                                    "start_time" => Carbon::parse($niddle_start_time),
                                    "end_time" => Carbon::parse($niddle_end_time),
                                ]);
                            }
                        }
                        $times = $temp_times;
                    }


                    /*
                        going throw times array to combine all the continuous time slots
                    */

                    $temp_times = []; //temp times to replace it with real time
                    $niddle_start_time = null;
                    $niddle_end_time = null;
                    // return $times;
                    foreach ($times as $time) {
                        if (!$niddle_start_time && !$niddle_end_time) {
                            $niddle_start_time = $time['start_time'];
                            $niddle_end_time = $time['end_time'];
                        } else {
                            if ($niddle_end_time == $time['end_time'] && $niddle_start_time == $time['start_time']) {
                                continue;
                            }
                            if ($niddle_end_time <= $time['start_time']) {
                                if ($niddle_end_time == $time['start_time']) {
                                    $niddle_end_time = $time['end_time'];
                                } else {
                                    array_push($temp_times, [
                                        "start_time" => $niddle_start_time,
                                        "end_time" => $niddle_end_time,
                                    ]);
                                    $niddle_start_time = $time['start_time'];
                                    $niddle_end_time = $time['end_time'];
                                }
                            }
                            if ($niddle_end_time <= $time['end_time']) {
                                $niddle_end_time = $time['end_time'];
                            }
                        }
                    }
                    if (!in_array([
                        "start_time" => $niddle_start_time,
                        "end_time" => $niddle_end_time,
                    ], $temp_times)) {

                        array_push($temp_times, [
                            "start_time" => $niddle_start_time,
                            "end_time" => $niddle_end_time,
                        ]);
                    }
                    $times = $temp_times;



                    /*
                        going throw all time to check for concurrent bookings
                    */
                    $temp_times = [];
                    $niddle_start_time = null;
                    $niddle_end_time = null;
                    if ($service && $service->parallel_booking_count > 0) {
                        foreach ($times as $time) {
                            $niddle_start_time = $time['start_time'];
                            $niddle_end_time = $time['end_time'];
                            // $already_booked_bookings = CompanyBooking::where('company_id', $company->id)
                            //     ->where('is_verified', 1)
                            //     ->where('is_cancelled', 0)
                            //     ->where(function ($q) use ($time) {
                            //         $q = $q->where(function ($q) use ($time) {
                            //             $q = $q->where('start_at', '>=', Carbon::parse($time['start_time']))->where('start_at', '<', Carbon::parse($time['end_time']));
                            //         })->orWhere(function ($q) use ($time) {
                            //             $q = $q->where('end_at', '>', Carbon::parse($time['start_time']))->where('end_at', '<=', Carbon::parse($time['end_time']));
                            //         })->orWhere(function ($q) use ($time) {
                            //             $q = $q->where('start_at', '<', Carbon::parse($time['start_time']))->where('end_at', '>=', Carbon::parse($time['end_time']));
                            //         });
                            //     })
                            //     ->where('service_id', $service->id)
                            //     ->orderBy('start_at', 'asc')
                            //     ->get();


                            $already_booked_bookings = collect($all_booked_bookings)->filter(function ($booking) use ($user_available_time_slot, $time, $practitioner_ids) {
                                if (!in_array($booking->user_id, $practitioner_ids)) {
                                    return false;
                                }
                                $startAt = Carbon::parse($time['start_time']);
                                $endAt = Carbon::parse($time['end_time']);

                                $bookingStart = Carbon::parse($booking->start_at);
                                $bookingEnd = Carbon::parse($booking->end_at);

                                // Condition 1: Booking starts within the user available time slot
                                $condition1 = $bookingStart->gte($startAt) && $bookingStart->lt($endAt);

                                // Condition 2: Booking ends within the user available time slot
                                $condition2 = $bookingEnd->gt($startAt) && $bookingEnd->lte($endAt);

                                // Condition 3: Booking fully overlaps the user available time slot
                                $condition3 = $bookingStart->lt($startAt) && $bookingEnd->gte($endAt);

                                // Return true if any of the conditions match
                                return $condition1 || $condition2 || $condition3;
                            });

                            if ($already_booked_bookings->count() > 0 && $already_booked_bookings->count() >= $service->parallel_booking_count) {
                                foreach ($already_booked_bookings as $booked_booking) {
                                    // $temp_count = CompanyBooking::where('company_id', $company->id)
                                    //     ->where('service_id', $service->id)
                                    //     ->where('is_verified', 1)
                                    //     ->where('is_cancelled', 0)
                                    //     ->where(function ($q) use ($booking) {
                                    //         $q = $q->where(function ($q) use ($booking) {
                                    //             $q = $q->where('start_at', '>=', Carbon::parse($booking->start_at))->where('start_at', '<', Carbon::parse($booking->end_at));
                                    //         })->orWhere(function ($q) use ($booking) {
                                    //             $q = $q->where('end_at', '>', Carbon::parse($booking->start_at))->where('end_at', '<=', Carbon::parse($booking->end_at));
                                    //         })->orWhere(function ($q) use ($booking) {
                                    //             $q = $q->where('start_at', '<', Carbon::parse($booking->start_at))->where('end_at', '>=', Carbon::parse($booking->end_at));
                                    //         });
                                    //     })
                                    //     ->count();


                                    $temp_count = collect($all_booked_bookings)->filter(function ($booking) use ($booked_booking, $practitioner_ids) {
                                        if (!in_array($booking->user_id, $practitioner_ids)) {
                                            return false;
                                        }
                                        $startAt = Carbon::parse($booked_booking->start_at);
                                        $endAt = Carbon::parse($booked_booking->end_at);

                                        $bookingStart = Carbon::parse($booking->start_at);
                                        $bookingEnd = Carbon::parse($booking->end_at);

                                        // Condition 1: Booking starts within the user available time slot
                                        $condition1 = $bookingStart->gte($startAt) && $bookingStart->lt($endAt);

                                        // Condition 2: Booking ends within the user available time slot
                                        $condition2 = $bookingEnd->gt($startAt) && $bookingEnd->lte($endAt);

                                        // Condition 3: Booking fully overlaps the user available time slot
                                        $condition3 = $bookingStart->lt($startAt) && $bookingEnd->gte($endAt);

                                        // Return true if any of the conditions match
                                        return $condition1 || $condition2 || $condition3;
                                    })->count();

                                    if ($temp_count >= $service->parallel_booking_count) {
                                        if ($niddle_start_time->lessThan(Carbon::parse($booked_booking->start_at))) {
                                            array_push($temp_times, [
                                                "start_time" => Carbon::parse($niddle_start_time),
                                                "end_time" => Carbon::parse($booked_booking->start_at),
                                            ]);
                                        }
                                        $niddle_start_time = Carbon::parse($booked_booking->end_at);
                                    }
                                }
                                if ($niddle_start_time != $niddle_end_time) {
                                    array_push($temp_times, [
                                        "start_time" => Carbon::parse($niddle_start_time),
                                        "end_time" => Carbon::parse($niddle_end_time),
                                    ]);
                                }
                            } else {
                                array_push($temp_times, [
                                    "start_time" => Carbon::parse($niddle_start_time),
                                    "end_time" => Carbon::parse($niddle_end_time),
                                ]);
                            }
                        }
                        $times = $temp_times;
                    }






                    /*
                        going throw all the available times and separating it into slots
                    */
                    foreach ($times as $time) {


                        $temp_time = $time['start_time'];
                        if ($temp_time) {
                            while ($temp_time->lessThanOrEqualTo($time['end_time'])) {
                                if (($duration - 2) <= $temp_time->diffInMinutes($time['end_time'])) {
                                    $slot_to_add = Carbon::parse($temp_date->format('Y-m-d') . ' ' . $temp_time->format('H:i:s'));
                                    $td = Carbon::parse($now);
                                    if ($slot_to_add->lessThan($td->addMinutes($minimum_lead_time))) {
                                        $temp_time = $temp_time->addMinutes($duration);
                                        continue;
                                    }
                                    if ($slot_to_add->greaterThan($td->addMinutes($maximum_lead_time))) {
                                        $temp_time = $temp_time->addMinutes($duration);
                                        continue;
                                    }

                                    array_push($slots, $temp_time->format('H:i'));
                                }
                                $temp_time = $temp_time->addMinutes($duration);
                            }
                        }
                    }
                    $slots_to_send = [];
                    foreach (array_unique($slots) as $slot) {
                        array_push($slots_to_send, $slot);
                    }

                    //finally adding all the available slots for particular date in data array
                    array_push($data, [
                        'date' => $temp_date->format('Y-m-d'),
                        'slots' => $slots_to_send,
                    ]);
                }
                //adding 1 day into temp_date to go throw next date
                $practitioner_ids = $all_practitioner_ids;
                $temp_date = $temp_date->addDay();
            }
        }


        //returning data with all the available slots in it
        return $data;
    }


    static function getAvailableSlotsTemp(
        Company $company,
        Carbon $start_date,
        Carbon $end_date,
        CompanyService $service = null,
        $user_ids = null,
        $time_zone = null
    ) {
        //getting the default time increments, in case of service doesn't have duration
        $time_increments = Setting::getSetting($company, Setting::TIME_INCREMENTS);

        //getting the practitioners that's available for booking section
        $practitioners = User::where('company_id', $company->id)->where('is_booking_on', 1);

        // filter practitioners by verified email
        $practitioners = $practitioners->verifiedEmail();

        //applying filter for service
        if ($service) {
            $practitioners = $practitioners->whereHas('services', function ($query) use ($service) {
                $query = $query->where('company_services.id', $service->id);
            });
        }

        $is_group_service = false;
        $max_booking_per_slot = 0;
        if ($service->category && $service->category->group_booking) {
            $is_group_service = true;
            $max_booking_per_slot = $service->group_quantity;
        }

        //applying filter for users_ids(if any)
        if ($user_ids && is_array($user_ids)) {
            $practitioners = $practitioners->whereIn('id', $user_ids);
        }

        //getting the final list of practitioners
        $practitioners =  $practitioners->get();
        $all_practitioner_ids =  $practitioners->pluck('id')->toArray();

        $now = TimeZoneManager::getTimeZoneCarbonNow($time_zone);
        $minimum_lead_time_setting = Setting::getSetting($company, Setting::MINIMUM_LEAD_TIME);
        $maximum_lead_time_setting = Setting::getSetting($company, Setting::MAXIMUM_LEAD_TIME);
        $minimum_lead_time = $minimum_lead_time_setting->value;
        $maximum_lead_time = $maximum_lead_time_setting->value;

        //default data array to store the slots in
        $data = [];
        if ($start_date && $end_date) {

            //temp date to go throw each dates
            $temp_date = $start_date;
            $practitioner_ids = $practitioners->pluck('id')->toArray();

            //loop that will go throw each dates and check slots in it
            while ($temp_date->lessThanOrEqualTo($end_date)) {




                //decide which WEEKDAY it is
                $week_day = BookingManager::getWeekDay($temp_date);

                //deciding duration of each slot based on either service or time increment
                $duration = $time_increments->value;
                if ($service) {
                    $duration = $service->duration;
                    if ($service->time_margin > 0) {
                        $duration = $duration + $service->time_margin;
                    }
                }

                //checking is practitioner ids is empty then directly making slots empty
                if (count($practitioner_ids) <= 0) {
                    array_push($data, [
                        'date' => $temp_date->format('Y-m-d'),
                        'slots' => [],
                    ]);
                    $temp_date = $temp_date->addDay();
                    continue;
                }

                //checking if this date is before the current time, if yes then ignoring it and continue
                $td = Carbon::parse($temp_date);
                if ($now->greaterThan($td->addDay())) {
                    array_push($data, [
                        'date' => $temp_date->format('Y-m-d'),
                        'slots' => [],
                    ]);
                    $temp_date = $temp_date->addDay();
                    continue;
                }

                //checking if this date is after the maximum lead time, if yes then ignoring it and continue
                $td1 = Carbon::parse($temp_date);
                $td2 = Carbon::parse($now);
                if ($td2->addMinutes($maximum_lead_time)->lessThan($td1)) {
                    array_push($data, [
                        'date' => $temp_date->format('Y-m-d'),
                        'slots' => [],
                    ]);
                    $temp_date = $temp_date->addDay();
                    continue;
                }

                //NEW LOGIC WITHOUT BUSINESS HOURS
                //default slot array to store slots data
                $slots = [];

                //exact date-time between which we have check for available slots
                $start_date_to_check = Carbon::parse($temp_date);
                $end_date_to_check = Carbon::parse($temp_date)->endOfDay();
                $user_available_time_slots = UserTimeSlot::where('type', UserTimeSlot::AVAILABLE)
                    ->whereIn('user_id', $practitioner_ids);

                if ($service) {
                    if ($is_group_service) {
                        $user_available_time_slots = $user_available_time_slots->where(function ($query) use ($service) {
                            $query = $query
                                ->whereHas('services', function ($query) use ($service) {
                                    $query = $query->where('company_services.id', $service->id);
                                });
                        });
                    } else {
                        $user_available_time_slots = $user_available_time_slots->where(function ($query) use ($service) {
                            $query = $query
                                ->where('is_for_all_services', 1)
                                ->orWhereHas('services', function ($query) use ($service) {
                                    $query = $query->where('company_services.id', $service->id);
                                });
                        });
                    }
                }


                $user_available_time_slots = $user_available_time_slots->where('start_at', '>=', $start_date_to_check)
                    ->where('end_at', '<=', $end_date_to_check)
                    ->orderBy('start_at', 'asc')
                    ->get();

                $practitioner_ids = $user_available_time_slots->pluck('user_id')->unique()->toArray();

                if (count($user_available_time_slots) <= 0) {
                    //if available time slots is empty then adding empty slots
                    array_push($data, [
                        'date' => $temp_date->format('Y-m-d'),
                        'slots' => [],
                    ]);
                } else {

                    //default time array to store times
                    $times = [];
                    foreach ($user_available_time_slots as $user_available_time_slot) {

                        /*
                            first checking on the bookings and removing all the durations thats already booked for given practitioner_ids
                        */
                        //getting already booked Booking between the date-times
                        $already_booked_bookings = CompanyBooking::where('company_id', $company->id)
                            ->where('is_verified', 1)
                            ->where('is_cancelled', 0)
                            ->with('clients')
                            // ->where('start_at', '>=', Carbon::parse($user_available_time_slot->start_at))
                            // ->where('end_at', '<=', Carbon::parse($user_available_time_slot->end_at))
                            ->where(function ($q) use ($user_available_time_slot) {
                                $q = $q->where(function ($q) use ($user_available_time_slot) {
                                    $q = $q->where('start_at', '>=', Carbon::parse($user_available_time_slot->start_at))->where('start_at', '<', Carbon::parse($user_available_time_slot->end_at));
                                })->orWhere(function ($q) use ($user_available_time_slot) {
                                    $q = $q->where('end_at', '>', Carbon::parse($user_available_time_slot->start_at))->where('end_at', '<=', Carbon::parse($user_available_time_slot->end_at));
                                })->orWhere(function ($q) use ($user_available_time_slot) {
                                    $q = $q->where('start_at', '<', Carbon::parse($user_available_time_slot->start_at))->where('end_at', '>=', Carbon::parse($user_available_time_slot->end_at));
                                });
                            })
                            ->whereIn('user_id', $practitioner_ids)
                            ->orderBy('start_at', 'asc')
                            ->get();



                        //NEW LOGIC FOR GROUP SERVICE
                        if ($is_group_service && count($practitioner_ids) == 1) { //in group-service, there can be only 1 practitioner
                            //DO SOMETHING IF NEEDED
                            if ($max_booking_per_slot <= 0) {
                                //TODO::should alert use that service doesn't have any slot by default.
                            }
                            //temp start date to start with
                            $temp_start_at = Carbon::parse($user_available_time_slot->start_at);

                            //going throw each booking to see if all slots are booked for that booking
                            foreach ($already_booked_bookings as $booking) {

                                //checking if this booking's all slots are full
                                //if yes then storing our temp start time and booking start time as available and skipping current bookings duration
                                //(thus making it unavailable)
                                if (count($booking->active_clients) >= $max_booking_per_slot) {
                                    if ($temp_start_at->lessThan(Carbon::parse($booking->start_at))) {
                                        array_push($times, [
                                            "start_time" => $temp_start_at,
                                            "end_time" => Carbon::parse($booking->start_at),
                                        ]);
                                    }
                                    // $temp_start_at = Carbon::parse($booking->end_at);
                                    if (Carbon::parse($booking->end_at)->greaterThan(Carbon::parse($user_available_time_slot->end_at))) {
                                        $temp_start_at = Carbon::parse($user_available_time_slot->end_at);
                                    } else {
                                        $temp_start_at = Carbon::parse($booking->end_at);
                                    }
                                }
                            }
                            //after going throw each booking if our temp start time is still not at the end
                            //then making the duration between temp start time and end time is available.
                            if ($temp_start_at != Carbon::parse($user_available_time_slot->end_at)) {
                                array_push($times, [
                                    "start_time" => $temp_start_at,
                                    "end_time" => Carbon::parse($user_available_time_slot->end_at),
                                ]);
                            }
                        } else {
                            //checking if this bookings is for all the practitioners, if not then ignoring it and setting the array to empty
                            //(because if it's not for all practitioners it means some practitioners are still available, so we don't need to look into this booking )
                            $booked_user_ids = array_unique($already_booked_bookings->pluck('user_id')->toArray());
                            if (count($booked_user_ids) != count($practitioner_ids)) {
                                $already_booked_bookings = [];
                            }

                            //if booking is for all the practitioners then going throw each booking to get the available slots
                            //temp start date to start with
                            $temp_start_at = Carbon::parse($user_available_time_slot->start_at);

                            //going throw each booking to see if all the practitioners are booked or not
                            foreach ($already_booked_bookings as $booking) {

                                //getting all the booking thats between current booking's start and end time

                                //NEW-LOGIC
                                $temp_already_booked_bookings = $already_booked_bookings->filter(function ($booked_booking) use ($booking) {

                                    if (
                                        Carbon::parse($booked_booking->start_at)->greaterThanOrEqualTo(Carbon::parse($booking->start_at))
                                        &&
                                        Carbon::parse($booked_booking->start_at)->lessThan(Carbon::parse($booking->end_at))
                                    ) {
                                        return true;
                                    }
                                    if (
                                        Carbon::parse($booked_booking->end_at)->greaterThan(Carbon::parse($booking->start_at))
                                        &&
                                        Carbon::parse($booked_booking->end_at)->lessThanOrEqualTo(Carbon::parse($booking->end_at))
                                    ) {
                                        return true;
                                    }
                                    if (
                                        Carbon::parse($booked_booking->start_at)->lessThan(Carbon::parse($booking->start_at))
                                        &&
                                        Carbon::parse($booked_booking->end_at)->greaterThanOrEqualTo(Carbon::parse($booking->end_at))
                                    ) {
                                        return true;
                                    }
                                    if (
                                        Carbon::parse($booked_booking->start_at)->equalTo(Carbon::parse($booking->start_at))
                                        &&
                                        Carbon::parse($booked_booking->end_at)->equalTo(Carbon::parse($booking->end_at))
                                    ) {
                                        return true;
                                    }

                                    return false;
                                });


                                $temp = array_unique($temp_already_booked_bookings
                                    ->pluck('user_id')
                                    ->toArray());

                                //OLD-LOGIC
                                // $temp = array_unique($already_booked_bookings
                                //     ->where('start_at', '>=', Carbon::parse($booking->start_at))
                                //     ->where('end_at', '<=', Carbon::parse($booking->end_at))
                                //     ->pluck('user_id')
                                //     ->toArray());



                                //checking if this booking is booked for all the practitioners,
                                //if yes then storing our temp start time and booking start time as available and skipping current bookings duration
                                //(thus making it unavailable)
                                if (count($temp) == count($practitioner_ids)) {

                                    if ($temp_start_at->lessThan(Carbon::parse($booking->start_at)) && ($duration - 2) < $temp_start_at->diffInMinutes(Carbon::parse($booking->start_at))) {
                                        array_push($times, [
                                            "start_time" => $temp_start_at,
                                            "end_time" => Carbon::parse($booking->start_at),
                                        ]);
                                    }

                                    // $temp_start_at = Carbon::parse($booking->end_at);
                                    if (Carbon::parse($booking->end_at)->greaterThan(Carbon::parse($user_available_time_slot->end_at))) {
                                        $temp_start_at = Carbon::parse($user_available_time_slot->end_at);
                                    } else {
                                        $temp_start_at = Carbon::parse($booking->end_at);
                                    }
                                }
                            }

                            //after going throw each booking if our temp start time is still not at the end
                            //then making the duration between temp start time and end time is available.
                            if ($temp_start_at != Carbon::parse($user_available_time_slot->end_at)) {
                                array_push($times, [
                                    "start_time" => $temp_start_at,
                                    "end_time" => Carbon::parse($user_available_time_slot->end_at),
                                ]);
                            }
                        }



                        /*
                            now checking on private blocking time and remove
                        */
                        $temp_times = [];
                        $niddle_start_time = null;
                        $niddle_end_time = null;
                        foreach ($times as $time) {
                            $private_slots = UserTimeSlot::where('type', UserTimeSlot::PRIVATE_SLOT_BLOCKING)
                                ->where(function ($q) use ($time) {
                                    $q = $q->where(function ($q) use ($time) {
                                        $q = $q->where('start_at', '>=', Carbon::parse($time['start_time']))->where('start_at', '<', Carbon::parse($time['end_time']));
                                    })->orWhere(function ($q) use ($time) {
                                        $q = $q->where('end_at', '>', Carbon::parse($time['start_time']))->where('end_at', '<=', Carbon::parse($time['end_time']));
                                    })->orWhere(function ($q) use ($time) {
                                        $q = $q->where('start_at', '<', Carbon::parse($time['start_time']))->where('end_at', '>=', Carbon::parse($time['end_time']));
                                    });
                                })
                                ->whereIn('user_id', $practitioner_ids)
                                ->orderBy('start_at', 'asc')
                                ->get();


                            $niddle_start_time = $time['start_time'];
                            $niddle_end_time = $time['end_time'];
                            foreach ($private_slots as $private_slot) {

                                // getting all the private slots thats between current slot's start and end time
                                $temp = array_unique($private_slots
                                    ->where('start_at', '>=', Carbon::parse($private_slot->start_at))
                                    ->where('end_at', '<=', Carbon::parse($private_slot->end_at))
                                    ->pluck('user_id')
                                    ->toArray());

                                //checking if this private slot is for all practitioners,
                                if (count($temp) == count($practitioner_ids)) {
                                    if ($niddle_start_time->lessThan(Carbon::parse($private_slot->start_at))) {
                                        array_push($temp_times, [
                                            "start_time" => Carbon::parse($niddle_start_time),
                                            "end_time" => Carbon::parse($private_slot->start_at),
                                        ]);
                                    }
                                    $niddle_start_time = Carbon::parse($private_slot->end_at);
                                } else {

                                    $already_booked_bookings = CompanyBooking::where('company_id', $company->id)
                                        ->where('is_verified', 1)
                                        ->where('is_cancelled', 0)
                                        // ->where('start_at', '>=', Carbon::parse($user_available_time_slot->start_at))
                                        // ->where('end_at', '<=', Carbon::parse($user_available_time_slot->end_at))
                                        ->where(function ($q) use ($niddle_start_time, $private_slot) {
                                            $q = $q->where(function ($q) use ($niddle_start_time, $private_slot) {
                                                $q = $q->where('start_at', '>=', Carbon::parse($private_slot->start_at))->where('start_at', '<', Carbon::parse($private_slot->end_at));
                                            })->orWhere(function ($q) use ($niddle_start_time, $private_slot) {
                                                $q = $q->where('end_at', '>', Carbon::parse($private_slot->start_at))->where('end_at', '<=', Carbon::parse($private_slot->end_at));
                                            })->orWhere(function ($q) use ($niddle_start_time, $private_slot) {
                                                $q = $q->where('start_at', '<', Carbon::parse($private_slot->start_at))->where('end_at', '>=', Carbon::parse($private_slot->end_at));
                                            });
                                        })
                                        ->whereIn('user_id', $practitioner_ids)
                                        ->orderBy('start_at', 'asc')
                                        ->get();

                                    $already_booked_booking_ids = array_unique($already_booked_bookings
                                        ->pluck('user_id')
                                        ->toArray());


                                    if (count(array_unique(array_merge($already_booked_booking_ids, $temp))) == count($practitioner_ids)) {

                                        foreach ($already_booked_bookings as $already_booked_booking) {
                                            //getting all the booking thats between current booking's start and end time
                                            $in_temp = array_unique($already_booked_bookings
                                                ->where('start_at', '>=', Carbon::parse($already_booked_booking->start_at))
                                                ->where('end_at', '<=', Carbon::parse($already_booked_booking->end_at))
                                                ->pluck('user_id')
                                                ->toArray());

                                            //checking if this booking is booked for all the practitioners,
                                            //if yes then storing our temp start time and booking start time as available and skipping current bookings duration
                                            //(thus making it unavailable)
                                            if (count(array_unique(array_merge($in_temp, $temp))) == count($practitioner_ids)) {


                                                if ($niddle_start_time->lessThan(Carbon::parse($already_booked_booking->start_at)) && ($duration - 2) < $niddle_start_time->diffInMinutes(Carbon::parse($already_booked_booking->start_at))) {

                                                    array_push($temp_times, [
                                                        "start_time" => $niddle_start_time,
                                                        "end_time" => Carbon::parse($already_booked_booking->start_at),
                                                    ]);
                                                    // if ($temp_date->format('Y-m-d') == '2023-11-03') {
                                                    //     dd($times);
                                                    // }
                                                }

                                                // $temp_start_at = Carbon::parse($booking->end_at);
                                                // if (Carbon::parse($booking->end_at)->greaterThan(Carbon::parse($private_slot->end_at))) {
                                                //     $niddle_end_time = Carbon::parse($private_slot->end_at);
                                                // } else {
                                                $niddle_end_time = Carbon::parse($already_booked_booking->end_at);
                                                // }
                                            }
                                        }
                                        if ($niddle_start_time->lessThan(Carbon::parse($private_slot->start_at))) {
                                            array_push($temp_times, [
                                                "start_time" => Carbon::parse($niddle_start_time),
                                                "end_time" => Carbon::parse($private_slot->start_at),
                                            ]);
                                        }
                                        $niddle_start_time = Carbon::parse($private_slot->end_at);
                                    } else {
                                        $temp_practitioner_ids = $practitioner_ids;
                                        unset($temp_practitioner_ids[array_search($private_slot->user_id, $temp_practitioner_ids)]);
                                        $user_available_time_slots = UserTimeSlot::where('type', UserTimeSlot::AVAILABLE)
                                            ->where(function ($q) use ($time, $private_slot) {
                                                $q = $q->where(function ($q) use ($time, $private_slot) {
                                                    $q = $q->where('start_at', '>=', Carbon::parse($private_slot->start_at))->where('start_at', '<', Carbon::parse($private_slot->end_at));
                                                })->orWhere(function ($q) use ($time, $private_slot) {
                                                    $q = $q->where('end_at', '>', Carbon::parse($private_slot->start_at))->where('end_at', '<=', Carbon::parse($private_slot->end_at));
                                                })->orWhere(function ($q) use ($time, $private_slot) {
                                                    $q = $q->where('start_at', '<', Carbon::parse($private_slot->start_at))->where('end_at', '>=', Carbon::parse($private_slot->end_at));
                                                });
                                            })
                                            ->whereIn('user_id', $temp_practitioner_ids)
                                            ->orderBy('start_at', 'asc')
                                            ->get();

                                        if (count($user_available_time_slots) <= 0) {
                                            $niddle_start_time = Carbon::parse($private_slot->end_at);
                                        }
                                    }
                                }
                            }

                            if ($niddle_start_time != $niddle_end_time) {
                                array_push($temp_times, [
                                    "start_time" => Carbon::parse($niddle_start_time),
                                    "end_time" => Carbon::parse($niddle_end_time),
                                ]);
                            }
                        }
                        $times = $temp_times;
                    }


                    /*
                        going throw times array to combine all the continuous time slots
                    */

                    $temp_times = []; //temp times to replace it with real time
                    $niddle_start_time = null;
                    $niddle_end_time = null;
                    // return $times;
                    foreach ($times as $time) {
                        if (!$niddle_start_time && !$niddle_end_time) {
                            $niddle_start_time = $time['start_time'];
                            $niddle_end_time = $time['end_time'];
                        } else {
                            if ($niddle_end_time == $time['end_time'] && $niddle_start_time == $time['start_time']) {
                                continue;
                            }
                            if ($niddle_end_time <= $time['start_time']) {
                                if ($niddle_end_time == $time['start_time']) {
                                    $niddle_end_time = $time['end_time'];
                                } else {
                                    array_push($temp_times, [
                                        "start_time" => $niddle_start_time,
                                        "end_time" => $niddle_end_time,
                                    ]);
                                    $niddle_start_time = $time['start_time'];
                                    $niddle_end_time = $time['end_time'];
                                }
                            }
                            if ($niddle_end_time <= $time['end_time']) {
                                $niddle_end_time = $time['end_time'];
                            }
                        }
                    }
                    if (!in_array([
                        "start_time" => $niddle_start_time,
                        "end_time" => $niddle_end_time,
                    ], $temp_times)) {

                        array_push($temp_times, [
                            "start_time" => $niddle_start_time,
                            "end_time" => $niddle_end_time,
                        ]);
                    }
                    $times = $temp_times;



                    /*
                        going throw all time to check for concurrent bookings
                    */
                    $temp_times = [];
                    $niddle_start_time = null;
                    $niddle_end_time = null;
                    if ($service && $service->parallel_booking_count > 0) {
                        foreach ($times as $time) {
                            $niddle_start_time = $time['start_time'];
                            $niddle_end_time = $time['end_time'];
                            $already_booked_bookings = CompanyBooking::where('company_id', $company->id)
                                ->where('is_verified', 1)
                                ->where('is_cancelled', 0)
                                ->where(function ($q) use ($time) {
                                    $q = $q->where(function ($q) use ($time) {
                                        $q = $q->where('start_at', '>=', Carbon::parse($time['start_time']))->where('start_at', '<', Carbon::parse($time['end_time']));
                                    })->orWhere(function ($q) use ($time) {
                                        $q = $q->where('end_at', '>', Carbon::parse($time['start_time']))->where('end_at', '<=', Carbon::parse($time['end_time']));
                                    })->orWhere(function ($q) use ($time) {
                                        $q = $q->where('start_at', '<', Carbon::parse($time['start_time']))->where('end_at', '>=', Carbon::parse($time['end_time']));
                                    });
                                })
                                ->where('service_id', $service->id)
                                ->orderBy('start_at', 'asc')
                                ->get();
                            if ($already_booked_bookings->count() > 0 && $already_booked_bookings->count() >= $service->parallel_booking_count) {
                                foreach ($already_booked_bookings as $booking) {
                                    $temp_count = CompanyBooking::where('company_id', $company->id)
                                        ->where('service_id', $service->id)
                                        ->where('is_verified', 1)
                                        ->where('is_cancelled', 0)
                                        ->where(function ($q) use ($booking) {
                                            $q = $q->where(function ($q) use ($booking) {
                                                $q = $q->where('start_at', '>=', Carbon::parse($booking->start_at))->where('start_at', '<', Carbon::parse($booking->end_at));
                                            })->orWhere(function ($q) use ($booking) {
                                                $q = $q->where('end_at', '>', Carbon::parse($booking->start_at))->where('end_at', '<=', Carbon::parse($booking->end_at));
                                            })->orWhere(function ($q) use ($booking) {
                                                $q = $q->where('start_at', '<', Carbon::parse($booking->start_at))->where('end_at', '>=', Carbon::parse($booking->end_at));
                                            });
                                        })
                                        ->count();
                                    if ($temp_count >= $service->parallel_booking_count) {
                                        if ($niddle_start_time->lessThan(Carbon::parse($booking->start_at))) {
                                            array_push($temp_times, [
                                                "start_time" => Carbon::parse($niddle_start_time),
                                                "end_time" => Carbon::parse($booking->start_at),
                                            ]);
                                        }
                                        $niddle_start_time = Carbon::parse($booking->end_at);
                                    }
                                }
                                if ($niddle_start_time != $niddle_end_time) {
                                    array_push($temp_times, [
                                        "start_time" => Carbon::parse($niddle_start_time),
                                        "end_time" => Carbon::parse($niddle_end_time),
                                    ]);
                                }
                            } else {
                                array_push($temp_times, [
                                    "start_time" => Carbon::parse($niddle_start_time),
                                    "end_time" => Carbon::parse($niddle_end_time),
                                ]);
                            }
                        }
                        $times = $temp_times;
                    }






                    /*
                        going throw all the available times and separating it into slots
                    */
                    foreach ($times as $time) {


                        $temp_time = $time['start_time'];
                        if ($temp_time) {
                            while ($temp_time->lessThanOrEqualTo($time['end_time'])) {
                                if (($duration - 2) <= $temp_time->diffInMinutes($time['end_time'])) {
                                    $slot_to_add = Carbon::parse($temp_date->format('Y-m-d') . ' ' . $temp_time->format('H:i:s'));
                                    $td = Carbon::parse($now);
                                    if ($slot_to_add->lessThan($td->addMinutes($minimum_lead_time))) {
                                        $temp_time = $temp_time->addMinutes($duration);
                                        continue;
                                    }
                                    if ($slot_to_add->greaterThan($td->addMinutes($maximum_lead_time))) {
                                        $temp_time = $temp_time->addMinutes($duration);
                                        continue;
                                    }

                                    array_push($slots, $temp_time->format('H:i'));
                                }
                                $temp_time = $temp_time->addMinutes($duration);
                            }
                        }
                    }
                    $slots_to_send = [];
                    foreach (array_unique($slots) as $slot) {
                        array_push($slots_to_send, $slot);
                    }

                    //finally adding all the available slots for particular date in data array
                    array_push($data, [
                        'date' => $temp_date->format('Y-m-d'),
                        'slots' => $slots_to_send,
                    ]);
                }
                //adding 1 day into temp_date to go throw next date
                $practitioner_ids = $all_practitioner_ids;
                $temp_date = $temp_date->addDay();
            }
        }


        //returning data with all the available slots in it
        return $data;
    }

    static function getActiveDays(Company $company, Carbon $start_date, Carbon $end_date, CompanyService $service = null, $user_ids = null)
    {
        //default array to store unavailable days
        $unavailable_days = [];

        //getting the practitioners that's available for booking section
        $practitioners = User::where('company_id', $company->id)->where('is_booking_on', 1);

        // filter practitioners by verified email
        $practitioners = $practitioners->verifiedEmail();

        //applying filter for service
        if ($service) {
            $practitioners = $practitioners->whereHas('services', function ($query) use ($service) {
                $query = $query->where('company_services.id', $service->id);
            });
        }

        //applying filter for users_ids(if any)
        if ($user_ids && is_array($user_ids)) {
            $practitioners = $practitioners->whereIn('id', $user_ids);
        }

        //getting the final list of practitioners
        $practitioners =  $practitioners->get();

        //default data array to store the slots in
        $data = [];
        if ($start_date && $end_date) {

            //temp date to go throw each dates
            $temp_date = $start_date;
            $practitioner_ids = $practitioners->pluck('id')->toArray();

            //loop that will go throw each dates and check slots in it
            while ($temp_date->lessThanOrEqualTo($end_date)) {

                //decide which WEEKDAY it is
                $week_day = BookingManager::getWeekDay($temp_date);

                //checking is practitioner ids is empty then directly making slots empty
                if (count($practitioner_ids) <= 0) {
                    array_push($data, [
                        'date' => $temp_date->format('Y-m-d'),
                        'slots' => [],
                    ]);
                    $temp_date = $temp_date->addDay();
                    continue;
                }

                //first checking if clinic is open at that weekday
                if (CompanyBusinessHour::where('company_id', $company->id)->where('week_day', $week_day)->exists()) {
                } else {
                    array_push($unavailable_days, $temp_date->format('Y-m-d'));
                }

                //adding 1 day into temp_date to go throw next date
                $temp_date = $temp_date->addDay();
            }
        }

        return $unavailable_days;
    }

    public function receipts()
    {
        return $this->hasMany(CompanyReceipt::class, 'user_id');
    }

    public function receipt_items()
    {
        return $this->hasMany(CompanyReceiptItem::class, 'user_id');
    }

    public function receipt_items_for_receipt()
    {
        return $this->hasManyThrough(CompanyReceiptItem::class, CompanyReceipt::class, 'user_id', 'receipt_id')
            ->where('company_receipts.status', CompanyReceiptItem::PAID)
            ->where('company_receipt_items.user_id', null);
    }

    public function getTaxRates()
    {
        $country = $this->company->country;

        return config("countries.$country.vat.percentage", [0]);
    }

    /**
     * Send the email verification notification.
     *
     * @return void
     */
    public function sendEmailVerificationNotification()
    {
        $this->notify((new VerifyApiEmail($this->company->email == $this->email ? false : true))->locale(app()->getLocale()));
    }
}
