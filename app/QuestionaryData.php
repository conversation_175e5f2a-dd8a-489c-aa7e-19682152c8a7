<?php

namespace App;

use App\Client;
use App\Traits\GetEncryptedFile;
use App\Traits\LogsActivity;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Storage;

class QuestionaryData extends Model
{
    use GetEncryptedFile, LogsActivity;

    protected $fillable = [
        'client_id',
        'pdf',
        'response',
        'modelable_id',
        'modelable_type',
        'updated_at',
        'created_at',
        'sign',
        'signed_at',
        'signed_by_id',
        'questions'
    ];

    protected $appends = [
        'formatted_response',
        'should_regen_pdf',
    ];

    protected $casts = [
        'questions' => 'array',
    ];

    public function getFormattedResponseAttribute()
    {
        return collect(json_decode($this->response ?? '', true));
    }

    public function getShouldRegenPdfAttribute()
    {
        return $this->updated_at < Carbon::parse(config('app.regen_file_before', now()));
    }

    public function client()
    {
        return $this->belongsTo(Client::class);
    }
    public function signed_by()
    {
        return $this->belongsTo('App\User', 'signed_by_id')->withTrashed()->without('company')->select(['id', 'first_name', 'last_name', 'email']);
    }

    public function files()
    {
        return $this->morphMany(File::class, 'fileable')->latest();
    }
    public function modelable()
    {
        return $this->morphTo();
    }

    public function getPdfAttribute($value)
    {
        if ($value) {
            return str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl($value));
        }
        return $value;
    }

    public function getSignAttribute($value)
    {
        if ($value) {
            return str_replace(substr(Storage::url('/'), 0, -1), "", (string)$this->getS3SignedUrl($value));
        }
        return $value;
    }

    protected static $logAttributes = [];

    protected static $logName = 'Questionnaires';

    public function getTitleAttribute()
    {
        if ($this->modelable_type === "App\\AestheticInterest") {
            return "Aesthetic Interest";
        }
        if ($this->modelable_type === "App\\HealthQuestionary") {
            return "Health Questionnaire";
        }
        if ($this->modelable_type === "App\\Covid19") {
            return "Covid-19 Questionnaires";
        }
    }

    public function getDescriptionForEvent(string $eventName): string
    {
        $client_user = $this->client;

        return $client_user->first_name . ' ' . $client_user->last_name . "'s Questionary has been {$eventName} by :causer.first_name :causer.last_name";
    }
}