<?php

namespace App;

// use App\Traits\Encryptable;

use App\Traits\GetEncryptedFile;
use App\Traits\LogsActivity;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class Setting extends Model
{
    use GetEncryptedFile;
    use LogsActivity;

    //KEYS
    public const NUMBER_OF_EMPLOYEES = "NUMBER_OF_EMPLOYEES";
    public const TIME_INCREMENTS = "TIME_INCREMENTS";
    public const MINIMUM_LEAD_TIME = "MINIMUM_LEAD_TIME";
    public const MAXIMUM_LEAD_TIME = "MAXIMUM_LEAD_TIME";
    public const INTERNAL_BOOKING_CONFIRMATION = "INTERNAL_BOOKING_CONFIRMATION";
    public const INTERNAL_BOOKING_CANCELLATION = "INTERNAL_BOOKING_CANCELLATION";
    public const CLIENT_EMAIL_REMINDER = "CLIENT_EMAIL_REMINDER";
    public const BOOKING_POLICY_LINK = "BOOKING_POLICY_LINK";
    public const BOOKING_POLICY = "BOOKING_POLICY";
    public const BOOKING_SECURITY = "BOOKING_SECURITY";
    public const BOOKING_PORTAL_TEXT = "BOOKING_PORTAL_TEXT";
    public const IS_TWELVE_HOURS = "IS_TWELVE_HOURS";
    public const BANK_ID_VERIFICATION_ENABLED = "BANK_ID_VERIFICATION_ENABLED";
    public const PRIVATE_TIME_COLOR = "PRIVATE_TIME_COLOR";
    public const DATE_FORMAT = "DATE_FORMAT";
    public const CAN_WE_PUBLISH_YOUR_BEFORE_AND_AFTER_PICTURE = "CAN_WE_PUBLISH_YOUR_BEFORE_AND_AFTER_PICTURE";
    public const BOOKING_EXTRAS_STEPS_REMINDER = "BOOKING_EXTRAS_STEPS_REMINDER";
    public const BOOKING_EXTRAS_STEPS_REMINDER_SMS_TIME = "BOOKING_EXTRAS_STEPS_REMINDER_SMS_TIME";
    public const BOOKING_EXTRAS_STEPS_REMINDER_SMS_TEMPLATE_ID = "BOOKING_EXTRAS_STEPS_REMINDER_SMS_TEMPLATE_ID";
    public const LANGUAGE = "LANGUAGE";
    public const CUSTOMER_LANGUAGE = "CUSTOMER_LANGUAGE";
    public const INTERNAL_BOOKING_CONFIRMATION_PRACTITIONER = "INTERNAL_BOOKING_CONFIRMATION_PRACTITIONER";
    public const INTERNAL_BOOKING_CANCELLATION_PRACTITIONER = "INTERNAL_BOOKING_CANCELLATION_PRACTITIONER";
    public const BOOKING_CANCELLATION_AND_MODIFY_POLICY = "BOOKING_CANCELLATION_AND_MODIFY_POLICY";
    // public const CUSTOMER_LANGUAGE = "CUSTOMER_LANGUAGE";

    public const COMPANY_VERIFICATION_NOTES = "COMPANY_VERIFICATION_NOTES";

    public const DEFAULT_FEES_PRESCRIBER = "DEFAULT_FEES_PRESCRIBER";
    public const RECEIVE_EMAIL_NOTIFICATION = "RECEIVE_EMAIL_NOTIFICATION";
    public const RECEIVE_EMAIL_REPORTS_ON = "RECEIVE_EMAIL_REPORTS_ON";
    // POS
    public const Z_REPORT_EMAIL = "Z_REPORT_EMAIL";
    public const POS_WAITING_LIST = "POS_WAITING_LIST";
    public const POS_LICENSE = "POS_LICENSE";
    public const POS_SWISH_QR = "POS_SWISH_QR";
    public const ONLINE_PAYMENT = "ONLINE_PAYMENT";

    public const VIVA_WALLET_SOURCE_CODE = "VIVA_WALLET_SOURCE_CODE";



    public const SMS_CLIENT_BOOKING_REMINDER_TIME = "SMS_CLIENT_BOOKING_REMINDER_TIME";
    public const SMS_CLIENT_BOOKING_REMINDER_TEMPLATE_ID = "SMS_CLIENT_BOOKING_REMINDER_TEMPLATE_ID";
    public const SMS_CLIENT_BOOKING_CONFIRMATION_ON = "SMS_CLIENT_BOOKING_CONFIRMATION_ON";
    public const SMS_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID = "SMS_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID";
    public const SMS_CLIENT_BOOKING_CANCELLATION_ON = "SMS_CLIENT_BOOKING_CANCELLATION_ON";
    public const SMS_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID = "SMS_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID";
    public const SMS_INTERNAL_BOOKING_CONFIRMATION_ON = "SMS_INTERNAL_BOOKING_CONFIRMATION_ON";
    public const SMS_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID = "SMS_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID";
    public const SMS_INTERNAL_BOOKING_CANCELLATION_ON = "SMS_INTERNAL_BOOKING_CANCELLATION_ON";
    public const SMS_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID = "SMS_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID";
    public const CALENDAR_CUSTOM_START_TIME = "CALENDAR_CUSTOM_START_TIME";
    public const CALENDAR_CUSTOM_END_TIME = "CALENDAR_CUSTOM_END_TIME";
    public const SMS_SENDER_ID = "SMS_SENDER_ID";

    public const EMAIL_CLIENT_BOOKING_ON = "EMAIL_CLIENT_BOOKING_ON";
    public const EMAIL_CLIENT_BOOKING_TEMPLATE_ID = "EMAIL_CLIENT_BOOKING_TEMPLATE_ID";
    public const EMAIL_CLIENT_BOOKING_CONFIRMATION_ON = "EMAIL_CLIENT_BOOKING_CONFIRMATION_ON";
    public const EMAIL_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID = "EMAIL_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID";
    public const EMAIL_CLIENT_BOOKING_CANCELLATION_ON = "EMAIL_CLIENT_BOOKING_CANCELLATION_ON";
    public const EMAIL_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID = "EMAIL_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID";
    public const EMAIL_CLIENT_BOOKING_RESCHEDULED_ON = "EMAIL_CLIENT_BOOKING_RESCHEDULED_ON";
    public const EMAIL_CLIENT_BOOKING_RESCHEDULED_TEMPLATE_ID = "EMAIL_CLIENT_BOOKING_RESCHEDULED_TEMPLATE_ID";
    public const EMAIL_CLIENT_BOOKING_REMINDER_TEMPLATE_ID = "EMAIL_CLIENT_BOOKING_REMINDER_TEMPLATE_ID";
    public const EMAIL_CLIENT_SEND_AFTER_CARE_TEMPLATE_ID = "EMAIL_CLIENT_SEND_AFTER_CARE_TEMPLATE_ID";
    public const EMAIL_CLIENT_BOOKING_EXTRA_STEPS_REMINDER_TEMPLATE_ID = "EMAIL_CLIENT_BOOKING_EXTRA_STEPS_REMINDER_TEMPLATE_ID";
    public const EMAIL_INTERNAL_BOOKING_CONFIRMATION_ON = "EMAIL_INTERNAL_BOOKING_CONFIRMATION_ON";
    public const EMAIL_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID = "EMAIL_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID";
    public const EMAIL_INTERNAL_BOOKING_CANCELLATION_ON = "EMAIL_INTERNAL_BOOKING_CANCELLATION_ON";
    public const EMAIL_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID = "EMAIL_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID";
    public const INTERNAL_BOOKING_RESCHEDULE = "INTERNAL_BOOKING_RESCHEDULE";
    public const INTERNAL_BOOKING_RESCHEDULE_PRACTITIONER = "INTERNAL_BOOKING_RESCHEDULE_PRACTITIONER";
    public const EMAIL_INTERNAL_BOOKING_RESCHEDULED_TEMPLATE_ID = "EMAIL_INTERNAL_BOOKING_RESCHEDULED_TEMPLATE_ID";
    public const EMAIL_INTERNAL_BOOKING_UPDATED_ON = "EMAIL_INTERNAL_BOOKING_UPDATED_ON";
    public const EMAIL_INTERNAL_BOOKING_UPDATED_TEMPLATE_ID = "EMAIL_INTERNAL_BOOKING_UPDATED_TEMPLATE_ID";
    public const EMAIL_INTERNAL_BOOKING_REMINDER_ON = "EMAIL_INTERNAL_BOOKING_REMINDER_ON";
    public const EMAIL_INTERNAL_BOOKING_REMINDER_TEMPLATE_ID = "EMAIL_INTERNAL_BOOKING_REMINDER_TEMPLATE_ID";
    public const EMAIL_CLIENT_REGISTRATION_TEMPLATE_ID = "EMAIL_CLIENT_REGISTRATION_TEMPLATE_ID";
    public const SEND_CLIENT_WELCOME_EMAIL = "SEND_CLIENT_WELCOME_EMAIL";
    public const DELETE_COMPANY = "DELETE_COMPANY";

    // PORTAL VIEW
    public const PORTAL_VIEW_PROFILE = "PORTAL_VIEW_PROFILE";
    public const PORTAL_VIEW_PERSONAL_ID = "PORTAL_VIEW_PERSONAL_ID";
    public const PORTAL_VIEW_CPR_ID = "PORTAL_VIEW_CPR_ID";
    public const PORTAL_VIEW_EMAIL = "PORTAL_VIEW_EMAIL";
    public const PORTAL_VIEW_DATE_OF_BIRTH = "PORTAL_VIEW_DATE_OF_BIRTH";
    public const PORTAL_VIEW_PHONE = "PORTAL_VIEW_PHONE";
    public const PORTAL_VIEW_STREET_ADDRESS = "PORTAL_VIEW_STREET_ADDRESS";
    public const PORTAL_VIEW_CITY = "PORTAL_VIEW_CITY";
    public const PORTAL_VIEW_ZIPCODE = "PORTAL_VIEW_ZIPCODE";
    public const PORTAL_VIEW_STATE = "PORTAL_VIEW_STATE";
    public const PORTAL_VIEW_COUNTRY = "PORTAL_VIEW_COUNTRY";

    // PORTAL QUESTIONARY REQUIRED
    public const SHOW_LETTER_OF_CONSENT = "SHOW_LETTER_OF_CONSENT";
    public const SHOW_LETTER_OF_CONSENT_IDS = "SHOW_LETTER_OF_CONSENT_IDS";

    //PORTAL REQUIRED
    public const PORTAL_REQUIRED_PERSONAL_ID = "PORTAL_REQUIRED_PERSONAL_ID";
    public const PORTAL_REQUIRED_CPR_ID = "PORTAL_REQUIRED_CPR_ID";
    public const PORTAL_REQUIRED_EMAIL = "PORTAL_REQUIRED_EMAIL";

    public const HIPPA_TERMS = "HIPPA_TERMS";
    public const HIPPA_TERMS_SV = "HIPPA_TERMS_SV";

    public const SMS_VERIFICATION_CODE = "SMS_VERIFICATION_CODE";

    public const HOLIDAY_CALENDARS = "HOLIDAY_CALENDARS";

    public const PORTAL_QUESTIONARY_SETTINGS = [
        Setting::PORTAL_REQUIRED_PERSONAL_ID,
        Setting::PORTAL_REQUIRED_CPR_ID,
        Setting::PORTAL_REQUIRED_EMAIL,
    ];

    public const PORTAL_VIEW_SETTINGS = [
        Setting::PORTAL_VIEW_PROFILE,
        Setting::PORTAL_VIEW_PERSONAL_ID,
        Setting::PORTAL_VIEW_DATE_OF_BIRTH,
        Setting::PORTAL_VIEW_PHONE,
        Setting::PORTAL_VIEW_STREET_ADDRESS,
        Setting::PORTAL_VIEW_CITY,
        Setting::PORTAL_VIEW_ZIPCODE,
        Setting::PORTAL_VIEW_STATE,
        Setting::PORTAL_VIEW_COUNTRY,
    ];

    public const BOOKING_SETTINGS = [
        Setting::TIME_INCREMENTS,
        Setting::MINIMUM_LEAD_TIME,
        Setting::MAXIMUM_LEAD_TIME,
        Setting::INTERNAL_BOOKING_CONFIRMATION,
        Setting::INTERNAL_BOOKING_CANCELLATION,
        Setting::CLIENT_EMAIL_REMINDER,
        Setting::BOOKING_POLICY_LINK,
        Setting::BOOKING_POLICY,
        Setting::BOOKING_SECURITY,
        Setting::IS_TWELVE_HOURS,
        Setting::PRIVATE_TIME_COLOR,
        Setting::DATE_FORMAT,
        Setting::CAN_WE_PUBLISH_YOUR_BEFORE_AND_AFTER_PICTURE,
        Setting::BOOKING_EXTRAS_STEPS_REMINDER,
        Setting::LANGUAGE,
        Setting::INTERNAL_BOOKING_CONFIRMATION_PRACTITIONER,
        Setting::INTERNAL_BOOKING_CANCELLATION_PRACTITIONER,
        Setting::BOOKING_CANCELLATION_AND_MODIFY_POLICY,
    ];

    public const BOOKING_SMS_SETTINGS = [
        Setting::SMS_CLIENT_BOOKING_REMINDER_TIME,
        Setting::SMS_CLIENT_BOOKING_REMINDER_TEMPLATE_ID,
        Setting::SMS_CLIENT_BOOKING_CONFIRMATION_ON,
        Setting::SMS_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID,
        Setting::SMS_CLIENT_BOOKING_CANCELLATION_ON,
        Setting::SMS_CLIENT_BOOKING_CANCELLATION_TEMPLATE_ID,
        Setting::SMS_INTERNAL_BOOKING_CONFIRMATION_ON,
        Setting::SMS_INTERNAL_BOOKING_CONFIRMATION_TEMPLATE_ID,
        Setting::SMS_INTERNAL_BOOKING_CANCELLATION_ON,
        Setting::SMS_INTERNAL_BOOKING_CANCELLATION_TEMPLATE_ID,
    ];

    // protected $encrypted = [
    //     'key', 'value'
    // ];

    protected $fillable = [
        'key',
        'value',
        'company_id'
    ];

    protected $hidden = [
        'created_at',
        'updated_at'
    ];

    public function company()
    {
        return $this->belongsTo('App\Company');
    }

    protected static $logAttributes = [];

    protected static $logName = 'company setting';

    public function getDescriptionForEvent(string $eventName): string
    {
        return "company setting has been {$eventName} by :causer.first_name :causer.last_name";
    }

    public function getValueAttribute($value)
    {
        try {
            if (in_array($this->key, [self::POS_LICENSE, self::POS_SWISH_QR])  && $value) {
                $data = str_replace(substr(Storage::url('/'), 0, -1), '', (string) $this->getS3SignedUrl($value));

                return $data;
            }
        } catch (\Throwable $th) {
            throw $th;
        }

        return $value;
    }

    public static function getDefaultValue($key)
    {
        switch ($key) {
            case Setting::TIME_INCREMENTS:
                return 30;
                break;
            case Setting::MINIMUM_LEAD_TIME:
                return 0;
                break;
            case Setting::MAXIMUM_LEAD_TIME:
                return 365;
                break;
            case Setting::INTERNAL_BOOKING_CONFIRMATION:
                return '';
                break;
            case Setting::INTERNAL_BOOKING_CANCELLATION:
                return '';
                break;
            case Setting::CLIENT_EMAIL_REMINDER:
                return 1440;
                break;
            case Setting::BOOKING_EXTRAS_STEPS_REMINDER:
                return 1440;
                break;
            case Setting::BOOKING_POLICY_LINK:
                return '';
                break;
            case Setting::BOOKING_POLICY:
                return '';
                break;
            case Setting::BOOKING_SECURITY:
                return 0;
                break;
            case Setting::IS_TWELVE_HOURS:
                return 0;
                break;
            case Setting::PRIVATE_TIME_COLOR:
                return '';
            case Setting::BOOKING_PORTAL_TEXT:
                return '';
            case Setting::DATE_FORMAT:
                return 'YYYY-MM-DD';
                break;
            case Setting::CAN_WE_PUBLISH_YOUR_BEFORE_AND_AFTER_PICTURE:
                return '1';
                break;
            case Setting::LANGUAGE:
                return 'en';
                break;
            case Setting::BANK_ID_VERIFICATION_ENABLED:
                return '0';
                break;
            case Setting::INTERNAL_BOOKING_CONFIRMATION_PRACTITIONER:
                return '0';
                break;
            case Setting::INTERNAL_BOOKING_CANCELLATION_PRACTITIONER:
                return '0';
                break;
            case Setting::INTERNAL_BOOKING_RESCHEDULE_PRACTITIONER:
                return '0';
                break;
            case Setting::BOOKING_CANCELLATION_AND_MODIFY_POLICY:
                return '1440';
            case Setting::DEFAULT_FEES_PRESCRIBER:
                return '300';
                break;
            case Setting::RECEIVE_EMAIL_NOTIFICATION:
                return '1';
                break;
            case Setting::Z_REPORT_EMAIL:
                return '';
                break;
            case Setting::SMS_CLIENT_BOOKING_REMINDER_TIME:
                return '24';
                break;
            case Setting::CALENDAR_CUSTOM_START_TIME:
                return '00:00';
                break;
            case Setting::CALENDAR_CUSTOM_END_TIME:
                return '23:59';
                break;
            case Setting::POS_WAITING_LIST:
                return '';
                break;
            case Setting::ONLINE_PAYMENT:
                return true;
                break;
            case Setting::SMS_SENDER_ID:
                $user = Auth::user();
                $company = $user?->company;
                return $company?->company_name ? Setting::replaceAndCapLength($company->company_name) : '';
                break;
            case Setting::EMAIL_CLIENT_BOOKING_CONFIRMATION_ON:
                return '1';
                break;
            case Setting::EMAIL_CLIENT_BOOKING_CANCELLATION_ON:
                return '1';
                break;
            case Setting::EMAIL_CLIENT_BOOKING_RESCHEDULED_ON:
                return '1';
                break;
            default:
                return '';
                break;
        }
    }

    public static function getSetting(Company $company, $key)
    {
        $setting = Setting::where('company_id', $company->id)->where('key', $key)->first();
        if (!$setting) {
            if ($key == Setting::CUSTOMER_LANGUAGE) {
                if ($company->stripe_id) {
                    $customer = $company->createOrGetStripeCustomer();
                    if ($customer->currency) {
                        if ($customer->currency == 'kr') {
                            $setting = Setting::updateOrCreate([
                                'value' => 'sv',
                            ], [
                                'key' => Setting::CUSTOMER_LANGUAGE,
                                'company_id' => $company->id
                            ]);
                        } else {
                            $setting = Setting::updateOrCreate([
                                'value' => 'en',
                            ], [
                                'key' => Setting::CUSTOMER_LANGUAGE,
                                'company_id' => $company->id
                            ]);
                        }
                    } else {
                        if ($company->country && $company->country == 'Sweden') {
                            $setting = Setting::updateOrCreate([
                                'value' => 'sv',
                            ], [
                                'key' => Setting::CUSTOMER_LANGUAGE,
                                'company_id' => $company->id
                            ]);
                        } else {
                            $setting = Setting::updateOrCreate([
                                'value' => 'en',
                            ], [
                                'key' => Setting::CUSTOMER_LANGUAGE,
                                'company_id' => $company->id
                            ]);
                        }
                    }
                }
            } else {
                $setting = Setting::create([
                    'key' => $key,
                    'value' => Setting::getDefaultValue($key),
                    'company_id' => $company->id
                ]);
            }

            if ($setting) {
                $setting = $setting->refresh();
            }
        }
        if ($key === Setting::MAXIMUM_LEAD_TIME) {
            // if (is_int($setting->value)) {
            $setting->value = (string)(1440 * (int)$setting->value);
            // } else {
            //     $setting->value = 0;
            // }
        }
        return $setting;
    }

    public static function formateTime(Company $company, Carbon $time_to_format)
    {
        $is_twelve_hours =  Setting::getSetting($company, Setting::IS_TWELVE_HOURS)->value;
        $format = '';
        if ($is_twelve_hours) {
            $format = $format . 'g:i A';
        } else {
            $format = $format . 'G:i';
        }
        return Carbon::parse($time_to_format)->translatedFormat($format);
    }

    public static function formateDate(Company $company, Carbon $date_to_format, $should_format_time = false, $with_day = false)
    {
        $selected_date_format =  Setting::getSetting($company, Setting::DATE_FORMAT)->value;
        $is_twelve_hours =  Setting::getSetting($company, Setting::IS_TWELVE_HOURS)->value;
        switch ($selected_date_format) {
            case 'DD/MM/YYYY':

                $format = '';
                if ($with_day) {
                    $format = $format . 'l ';
                }
                $format = $format . 'd/m/Y ';
                if ($should_format_time) {
                    if ($is_twelve_hours) {
                        $format = $format . 'g:i A';
                    } else {
                        $format = $format . 'G:i';
                    }
                }
                return Carbon::parse($date_to_format)->translatedFormat($format);


                break;
            case 'MM/DD/YYYY':
                $format = '';
                if ($with_day) {
                    $format = $format . 'l ';
                }
                $format = $format . 'm/d/Y ';
                if ($should_format_time) {
                    if ($is_twelve_hours) {
                        $format = $format . 'g:i A';
                    } else {
                        $format = $format . 'G:i';
                    }
                }
                return Carbon::parse($date_to_format)->translatedFormat($format);
                break;
            case 'DD.MM.YYYY':
                $format = '';
                if ($with_day) {
                    $format = $format . 'l ';
                }
                $format = $format . 'd.m.Y ';
                if ($should_format_time) {
                    if ($is_twelve_hours) {
                        $format = $format . 'g:i A';
                    } else {
                        $format = $format . 'G:i';
                    }
                }
                return Carbon::parse($date_to_format)->translatedFormat($format);
                break;
            case 'YYYY-MM-DD':
                $format = '';
                if ($with_day) {
                    $format = $format . 'l ';
                }
                $format = $format . 'Y-m-d ';
                if ($should_format_time) {
                    if ($is_twelve_hours) {
                        $format = $format . 'g:i A';
                    } else {
                        $format = $format . 'G:i';
                    }
                }
                return Carbon::parse($date_to_format)->translatedFormat($format);
                break;
            case 'YYYY.MM.DD':
                $format = '';
                if ($with_day) {
                    $format = $format . 'l ';
                }
                $format = $format . 'Y.m.d ';
                if ($should_format_time) {
                    if ($is_twelve_hours) {
                        $format = $format . 'g:i A';
                    } else {
                        $format = $format . 'G:i';
                    }
                }
                return Carbon::parse($date_to_format)->translatedFormat($format);
                break;
            case 'MM.DD.YYYY':
                $format = '';
                if ($with_day) {
                    $format = $format . 'l ';
                }
                $format = $format . 'm.d.Y ';
                if ($should_format_time) {
                    if ($is_twelve_hours) {
                        $format = $format . 'g:i A';
                    } else {
                        $format = $format . 'G:i';
                    }
                }
                return Carbon::parse($date_to_format)->translatedFormat($format);

                break;

            default:
                $format = '';
                if ($with_day) {
                    $format = $format . 'l ';
                }
                $format = $format . 'Y-m-d ';
                if ($should_format_time) {
                    if ($is_twelve_hours) {
                        $format = $format . 'g:i A';
                    } else {
                        $format = $format . 'G:i';
                    }
                }
                return Carbon::parse($date_to_format)->translatedFormat($format);
                break;
        }
    }

    public static function getDateTimeFormat(Company $company, $should_format_time = false, $with_day = false)
    {
        $selected_date_format =  Setting::getSetting($company, Setting::DATE_FORMAT)->value;
        $is_twelve_hours =  Setting::getSetting($company, Setting::IS_TWELVE_HOURS)->value;

        $with_day_format = $with_day ? "l" : "";
        $date_format = "Y-m-d";
        $time_format = $should_format_time ? ($is_twelve_hours ? "g:i A" : "G:i") : "";

        if ($selected_date_format == "DD/MM/YYYY") {
            $date_format = "d/m/Y";
        }

        if ($selected_date_format == "MM/DD/YYYY") {
            $date_format = "m/d/Y";
        }

        if ($selected_date_format == "DD.MM.YYYY") {
            $date_format = "d.m.Y";
        }

        if ($selected_date_format == "YYYY-MM-DD") {
            $date_format = "Y-m-d";
        }

        if ($selected_date_format == "YYYY.MM.DD") {
            $date_format = "Y.m.d";
        }

        if ($selected_date_format == "MM.DD.YYYY") {
            $date_format = "m.d.Y";
        }

        return trim(join(" ", [$with_day_format, $date_format, $time_format]));
    }

    public static function replaceAndCapLength($input)
    {
        // Use a regular expression to replace characters that are not A-Z, a-z, or 0-9 with a space
        $output = preg_replace('/[^A-Za-z0-9]/', ' ', $input);

        // Cap the length of the string to 11 characters
        $output = substr($output, 0, 11);

        return $output;
    }
}
