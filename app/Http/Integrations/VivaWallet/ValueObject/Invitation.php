<?php

namespace App\Http\Integrations\VivaWallet\ValueObject;

use App\Http\Integrations\VivaWallet\VivaWalletConnector;

class Invitation
{
    final public function __construct(
        public readonly string $email,
        public readonly string $redirectUrl,
        public readonly string $demoRedirectUrl,
        public readonly string $created,
    ) {
    }

    public static function fromArray(array $data, string $accountId): self
    {
        return new static(
            email: $data['email'] ?? '',
            redirectUrl: $data['redirectUrl'] ?? '',
            created: $data['created'] ?? '',
            demoRedirectUrl: VivaWalletConnector::DEMO_SIGNUP_URL . "?account_id=$accountId",
        );
    }
}
