<?php

namespace App\Http\Integrations\VivaWallet;

use App\Http\Integrations\POSPayment\Contracts\POSPayment;
use App\Http\Integrations\VivaWallet\Enums\Environment;
use App\Http\Integrations\VivaWallet\Resources\AccountResource;
use App\Http\Integrations\VivaWallet\Resources\SessionResource;
use App\Http\Integrations\VivaWallet\Resources\WebhookResource;
use GuzzleHttp\Psr7\Uri;
use Illuminate\Support\Facades\Cache;
use Psr\Http\Message\UriInterface;
use Saloon\Contracts\PendingRequest;
use Saloon\Contracts\Sender;
use Saloon\Http\Connector;
use Saloon\Traits\OAuth2\ClientCredentialsGrant;
use Saloon\Http\OAuth2\GetClientCredentialsTokenRequest;
use Saloon\Http\Senders\GuzzleSender;
use Saloon\Traits\Plugins\AlwaysThrowOnErrors;

class VivaWalletConnector extends Connector implements POSPayment
{
    use ClientCredentialsGrant, AlwaysThrowOnErrors;

    /**
     * Demo environment URL.
     */
    public const DEMO_URL = 'https://demo.vivapayments.com';

    /**
     * Production environment URL.
     */
    public const PRODUCTION_URL = 'https://www.vivapayments.com';

    /**
     * Demo environment accounts URL.
     */
    public const DEMO_ACCOUNTS_URL = 'https://demo-accounts.vivapayments.com';

    /**
     * Production environment accounts URL.
     */
    public const PRODUCTION_ACCOUNTS_URL = 'https://accounts.vivapayments.com';

    /**
     * Demo environment URL.
     */
    public const DEMO_API_URL = 'https://demo-api.vivapayments.com';

    /**
     * Demo environment Signup URL.
     */
    public const DEMO_SIGNUP_URL = 'https://demo.vivapayments.com/en/signup';

    /**
     * Production environment URL.
     */
    public const PRODUCTION_API_URL = 'https://api.vivapayments.com';

    protected string $token;

    protected string $username;
    protected string $password;

    public function __construct(
        protected Environment $environment,
        public string $merchantId,
        public string $resellerId,
        protected string $apiKey,
        protected string $clientId,
        protected string $clientSecret,
    ) {
        $this->username = "{$resellerId}:{$merchantId}";
        $this->password = $apiKey;

        $this->oauthConfig()->setClientId($clientId);
        $this->oauthConfig()->setClientSecret($clientSecret);
        $this->oauthConfig()->setTokenEndpoint($this->getAccountsUrl()->withPath('/connect/token'));
    }

    protected function defaultSender(): Sender
    {
        return resolve(GuzzleSender::class);
    }

    /**
     * The Base URL of the API
     *
     * @return string
     */
    public function resolveBaseUrl(): string
    {
        return $this->getApiUrl();
    }

    public function boot(PendingRequest $pendingRequest): void
    {
        if (!!$pendingRequest->getAuthenticator()) {
            return;
        }

        if ($pendingRequest->getRequest() instanceof GetClientCredentialsTokenRequest) {
            return;
        }

        // $authenticator = $this->getAccessToken();
        $authenticator = Cache::remember("viva_wallet_access_token", 3000, fn () => $this->getAccessToken());

        $pendingRequest->authenticate($authenticator);
        // $this->withTokenAuth($authenticator->getAccessToken());
    }

    /**
     * Get the URL.
     */
    public function getUrl(): UriInterface
    {
        return new Uri(match ($this->environment) {
            Environment::Production => self::PRODUCTION_URL,
            Environment::Demo => self::DEMO_URL,
        });
    }

    /**
     * Get the accounts URL.
     */
    public function getAccountsUrl(): UriInterface
    {
        return new Uri(match ($this->environment) {
            Environment::Production => self::PRODUCTION_ACCOUNTS_URL,
            Environment::Demo => self::DEMO_ACCOUNTS_URL,
        });
    }

    /**
     * Get the API URL.
     */
    public function getApiUrl(): UriInterface
    {
        return new Uri(match ($this->environment) {
            Environment::Production => self::PRODUCTION_API_URL,
            Environment::Demo => self::DEMO_API_URL,
        });
    }


    public function account(?string $merchantId = null): AccountResource
    {
        return new AccountResource($this, $merchantId);
    }

    public function session(): SessionResource
    {
        return new SessionResource($this);
    }

    public function webhook(): WebhookResource
    {
        return new WebhookResource($this);
    }

    /**
     * Use the production or demo environment.
     */
    public function withEnvironment(Environment|string $environment): self
    {
        $this->environment = is_string($environment) ? Environment::from($environment) : $environment;

        return $this;
    }

    /**
     * Use the given Merchant ID and API key for basic authentication.
     *
     * @see https://developer.vivawallet.com/getting-started/find-your-account-credentials/merchant-id-and-api-key/
     */
    public function withBasicAuthCredentials(
        #[\SensitiveParameter] ?string $username = null,
        #[\SensitiveParameter] ?string $password = null,
    ): self {
        $this->username = $username ??= $this->username;
        $this->password = $password ??= $this->password;

        return $this;
    }

    /**
     * Authenticate using basic auth.
     *
     * @return self
     */
    public function authenticateWithBasicAuth(): self
    {
        $this->withBasicAuth($this->username, $this->password);

        return $this;
    }

    /**
     * Use the given client credentials to authenticate with OAuth 2.0.
     *
     * @see https://developer.vivawallet.com/getting-started/find-your-account-credentials/client-smart-checkout-credentials/
     */
    public function withOAuthCredentials(
        #[\SensitiveParameter] string $clientId,
        #[\SensitiveParameter] string $clientSecret,
    ): self {
        $this->clientId = $clientId;
        $this->clientSecret = $clientSecret;

        return $this;
    }

    /**
     * Authenticate using the bearer token as an authorization header.
     *
     * @return self;
     *
     * @throws GuzzleException
     * @throws VivaException
     */
    public function authenticateWithBearerToken(): self
    {
        $token = $this->token ??= Cache::remember("viva_wallet_access_token_{$this->environment->value}", 3000, fn () => $this->getAccessToken());

        $this->withTokenAuth($token);

        $this->token = $token;

        return $this;
    }

    /**
     * Use the given access token to authenticate with OAuth 2.0.
     */
    public function withToken(#[\SensitiveParameter] string $token): self
    {
        $this->withTokenAuth($token);

        $this->token = $token;

        return $this;
    }

    /**
     * Default headers for every request
     *
     * @return string[]
     */
    protected function defaultHeaders(): array
    {
        return [];
    }

    /**
     * Default HTTP client options
     *
     * @return string[]
     */
    protected function defaultConfig(): array
    {
        return [];
    }
}
