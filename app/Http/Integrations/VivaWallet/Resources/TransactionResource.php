<?php

namespace App\Http\Integrations\VivaWallet\Resources;

use App\Http\Integrations\VivaWallet\DataObject\Transaction;
use App\Http\Integrations\VivaWallet\DataObject\TransactionByOrderCode;
use App\Http\Integrations\VivaWallet\DataObject\TransactionResponse;
use App\Http\Integrations\VivaWallet\Requests\CreateTransaction;
use App\Http\Integrations\VivaWallet\Requests\FindTransaction;
use App\Http\Integrations\VivaWallet\Requests\FindTransactionByOrderCode;
use App\Http\Integrations\VivaWallet\Requests\RefundTransaction;
use App\Http\Integrations\VivaWallet\VivaWalletConnector;
use Saloon\Contracts\Connector;

/**
 * @property VivaWalletConnector $connector
 */
class TransactionResource extends Resource
{
    public function __construct(
        protected Connector $connector,
        protected string $merchantId,
    ) {
    }

    /**
     * Create a new transaction.
     *
     *
     * @return string
     */
    public function create(
        string $terminalId,
        string $sessionId,
        string $cashRegisterId,
        int $amount,
        string $currencyCode,
        string $merchantReference,
        ?string $customerTrns = '',
        ?int $isvAmount = 0,
        ?int $maxInstalments = 0,
        ?int $tipAmount = 0,
    ): string {
        $response = $this->connector
            ->send(new CreateTransaction(
                merchantId: $this->merchantId,
                sessionId: $sessionId,
                terminalId: $terminalId,
                cashRegisterId: $cashRegisterId,
                amount: $amount,
                currencyCode: $currencyCode,
                merchantReference: $merchantReference,
                customerTrns: $customerTrns,
                maxInstalments: $maxInstalments,
                tipAmount: $tipAmount,
                isvAmount: $isvAmount,
            ));

        return $response->dto();
    }

    /**
     * Refund the transaction.
     *
     *
     * @return string
     */
    public function refund(
        string $sessionId,
        string $terminalId,
        string $cashRegisterId,
        string $parentSessionId,
        int $amount,
        string $currencyCode,
        string $merchantReference,
        ?string $customerTrns = null,
    ): string {

        $response = $this->connector
        ->send(new RefundTransaction(
            merchantId: $this->merchantId,
            sessionId: $sessionId,
            terminalId: $terminalId,
            cashRegisterId: $cashRegisterId,
            parentSessionId: $parentSessionId,
            amount: $amount,
            currencyCode: $currencyCode,
            merchantReference: $merchantReference,
            customerTrns: $customerTrns,
        ));

        return $response->dto();
    }

     /**
     * find the transaction by transaction id.
     *
     *
     * @return Transaction
     */


    public function find(string $transactionId): Transaction
    {
        if (empty($transactionId)) {
            throw new \InvalidArgumentException(__('pos_strings.viva.transaction_id_is_required'));
        }

        $response = $this->connector
        ->send(new FindTransaction(
            transactionId: $transactionId,
            merchantId: $this->merchantId,
        ));

        return $response->dto();
    }

    /**
     * find the transaction by order code.
     *
     *
     * @return TransactionResponse
     */


    public function findTransactionByOrderCode(
        string $orderCode,
    ): TransactionResponse {
        $response = $this->connector
            ->withBasicAuthCredentials("{$this->connector->resellerId}:{$this->merchantId}")
            ->authenticateWithBasicAuth()
            ->send((new FindTransactionByOrderCode(
                orderCode: $orderCode,
            ))->setBaseUrl($this->connector->getUrl()));

        return $response->dto();
    }
}
