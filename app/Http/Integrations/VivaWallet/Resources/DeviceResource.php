<?php

namespace App\Http\Integrations\VivaWallet\Resources;

use App\Http\Integrations\VivaWallet\DataObject\Account;
use App\Http\Integrations\VivaWallet\Requests\GetDevices;
use Illuminate\Support\Collection;
use Saloon\Contracts\Connector;

/**
 * @property Connector $connector
 */
class DeviceResource extends Resource
{
    public function __construct(
        protected Connector $connector,
        protected string $merchantId,
        protected ?string $terminalId = null,
    ) {
    }

    /**
     * List all devices.
     *
     * @return Collection
     */
    public function all(): Collection
    {
        $response = $this->connector
            ->send(new GetDevices($this->merchantId));

        return $response->dto();
    }
}
