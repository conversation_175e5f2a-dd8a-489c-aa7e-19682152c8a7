<?php

namespace App\Http\Integrations\VivaWallet\Resources;

use App\Http\Integrations\VivaWallet\Requests\CreateWebhook;
use App\Http\Integrations\VivaWallet\Requests\GetWebhookToken;
use Saloon\Contracts\Connector;
use App\Http\Integrations\VivaWallet\Enums\WebhookEventType;

/**
 * @property Connector $connector
 */
class WebhookResource extends Resource
{
    public function __construct(
        protected Connector $connector,
    ) {
    }

    /**
     * Find webhook key.
     *
     * @return string
     */
    public function getToken(): array
    {
        $response = $this->connector
            ->send(new GetWebhookToken());

        return (array) $response->json();
    }


    /**
     * Create a webhook.
     *
     *
     * @return string
     */
    public function create(string $url, WebhookEventType $eventTypeId): string
    {
        $response = $this->connector
            ->send(new CreateWebhook(url: $url, eventTypeId: $eventTypeId));

        return $response->dto();
    }
}
