<?php

namespace App\Http\Integrations\VivaWallet\Resources;

use App\Http\Integrations\VivaWallet\DataObject\Account;
use App\Http\Integrations\VivaWallet\Requests\CreateAccount;
use App\Http\Integrations\VivaWallet\Requests\FindAccount;
use Saloon\Contracts\Connector;

/**
 * @property Connector $connector
 */
class AccountResource extends Resource
{
    public function __construct(
        protected Connector $connector,
        protected ?string $merchantId,
    ) {
    }

    /**
     * Find account.
     *
     * @return Account
     */
    public function find(string $id): Account
    {
        if (empty($id)) {
            throw new \InvalidArgumentException(__('pos_strings.viva.account_id_is_required'));
        }

        $response = $this->connector
            ->send((new FindAccount($id)));

        return $response->dto();
    }

    /**
     * Get Device Resource.
     *
     * @return DeviceResource
     */
    public function devices(?string $terminalId = null): DeviceResource
    {
        if (!$this->merchantId || empty($this->merchantId)) {
            throw new \InvalidArgumentException(__('pos_strings.viva.merchantId_is_required'));
        }

        return new DeviceResource($this->connector, $this->merchantId, $terminalId);
    }

     /**
     * Create Transaction Resource.
     *
     * @return TransactionResource
     */
    public function transactions(): TransactionResource
    {
        if (!$this->merchantId || empty($this->merchantId)) {
            throw new \InvalidArgumentException(__('pos_strings.viva.merchantId_is_required'));
        }

        return new TransactionResource($this->connector, $this->merchantId);
    }

     /**
     * Payment Order.
     *
     * @return PaymentOrderResource
     */
    public function payment_order(): PaymentOrderResource
    {
        if (!$this->merchantId || empty($this->merchantId)) {
            throw new \InvalidArgumentException(__('pos_strings.viva.merchantId_is_required'));
        }

        return new PaymentOrderResource($this->connector, $this->merchantId);
    }

     /**
     * Sources.
     *
     * @return SourceResource
     */
    public function sources(): SourceResource
    {
        if (!$this->merchantId || empty($this->merchantId)) {
            throw new \InvalidArgumentException(__('pos_strings.viva.merchantId_is_required'));
        }

        return new SourceResource($this->connector, $this->merchantId);
    }

    /**
     * Create a new account.
     *
     *
     * @return Account
     */
    public function create(string $email): Account
    {
        $response = $this->connector
            ->send(new CreateAccount($email));

        return $response->dto();
    }
}
