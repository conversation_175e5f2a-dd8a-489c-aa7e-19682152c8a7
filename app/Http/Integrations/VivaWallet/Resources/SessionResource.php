<?php

namespace App\Http\Integrations\VivaWallet\Resources;

use App\Http\Integrations\VivaWallet\DataObject\Session ;
use App\Http\Integrations\VivaWallet\Requests\AbortSession;
use App\Http\Integrations\VivaWallet\Requests\FindSession;
use Saloon\Contracts\Connector;

/**
 * @property Connector $connector
 */
class SessionResource extends Resource
{
    public function __construct(
        protected Connector $connector,
    ) {
    }

    /**
     * Find account.
     *
     * @return Session
     */
    public function find(string $sessionId): Session
    {
        if (empty($sessionId)) {
            throw new \InvalidArgumentException(__('pos_strings.viva.session_id_is_required'));
        }

        $response = $this->connector
            ->send(new FindSession($sessionId));

        return $response->dto();
    }

    public function abort(string $sessionId, string $cashRegisterId): string
    {
        if (empty($sessionId)) {
            throw new \InvalidArgumentException(__('pos_strings.viva.session_id_is_required'));
        }
        if (empty($cashRegisterId)) {
            throw new \InvalidArgumentException(__('pos_strings.viva.cash_register_id_is_required'));
        }

        $response = $this->connector
            ->send(new AbortSession($sessionId,$cashRegisterId));

        return $response->dto();
    }
}
