<?php

namespace App\Http\Controllers\Api\v3;

use App\Activity;
use App\AestheticInterest;
use App\Client;
use App\ClientAddress;
use App\ClientLetterOfConsent;
use App\ClientPrescription;
use App\ClientTreatment;
use App\Company;
use App\CompanyBooking;
use App\CompanyPlatform;
use App\Contracts\Services\Subscription\SubscriptionServiceInterface;
use App\Covid19;
use App\GeneralNote;
use App\GeneralTemplate;
use App\HealthQuestionary;
use App\Http\Controllers\Api\v1\AfterCareTreatmentController;
use App\Http\Controllers\Controller;
use App\Http\Integrations\POSPayment\Facades\POSPayment;
use App\Http\Integrations\VivaWallet\Exceptions\SessionAlreadyExistException;
use App\Http\Integrations\VivaWallet\ValueObject\Customer;
use App\Imports\BookingImport;
use App\Models\UserOauthToken;
use App\QuestionaryData;
use App\Traits\Booking\BookingGoogleCalendarManager;
use App\Traits\FcmNotificationManager;
use App\Traits\FreeTrailManager;
use App\Traits\GoogleAuth;
use App\User;
use Aws\SecretsManager\SecretsManagerClient;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Log;
use Laravel\Socialite\Facades\Socialite;
use App\Contracts\Services\PDF\Company\CompanyPDFServiceInterface;
use App\Models\CompanyLegalDocument;
use App\Traits\SaveFile;

class TestController extends Controller
{
    use FreeTrailManager, FcmNotificationManager, GoogleAuth, SaveFile;

    public function test(Request $request)
    {

        $user = Auth::user();
        $company = $user->company;

        return Log::channel('slack_sales')->info("A new user has just signed up.", [
            'name' => $user->fullName(),
            'company_name' => $company->company_name,
            'email' => $user->email,
            'phone' => SMS::FORMAT_NUMBER($user->country_code, $user->mobile_number),
        ]);
    }

    public function testLegalDocumentGeneration(Request $request)
    {
        $pdfService = app(CompanyPDFServiceInterface::class);

        $company = null;
        if ($request->missing('company_id')) {
            return response()->json([
                'message' => "Please provide company id",
                'status' => '0',
            ]);
        }

        $company = Company::findOrFail($request->company_id);
        $company_legal_doc = app(CompanyLegalDocument::class);
        return $company_legal_doc->generateLegalDocument($company);
    }

    public function generatePaymentSource()
    {
        $company = Company::where('email', "<EMAIL>")->firstOrFail();

        // dd($company->findOrder(****************));
        return $company->getOrCreatePaymentSource();

        // POSPayment::account("26481d14-4887-46d1-8b3a-2d8d3ad2e7ff")
        //     ->sources()
        //     ->create(
        //         domain: "meridiq.com",
        //         isSecure: true,
        //         name: "Meridiq New",
        //         pathFail: "/payment?status=fail",
        //         pathSuccess: "/payment?status=success",
        //         sourceCode: "**********",
        //     );
    }

    public function generateOrderCode()
    {
        $company = Company::where('email', "<EMAIL>")->firstOrFail();

        return $company->createOnlineVivaPayment(
            amount: 1000,
            isvAmount: 200,
            customer: Customer::fromArray([
                "fullName" => $company->company_name,
                "email" => $company->email,
                "phone" => $company->phone(),
                "countryCode" => "IN",
                "requestLang" => "en",
            ]),
            merchantTrns: "This is merchantTrns",
            customerTrns: "This is customer Trns",
            dynamicDescriptor: "DynamicDescriptor",
            tags: [
                "tag1",
                "tag2",
                "tag3",
            ]
        );
    }

    public function sendFcmNotification(Request $request)
    {
        $user = User::where('email', $request->email)->first();
        $this->sendNotification($user->firebase_tokens, $request->title, $request->message);
        return response()->json([
            'message' => __('messages.notification_sent'),
            'status' => '1',
        ]);
    }

    public function complete_free_trial(Request $request, SubscriptionServiceInterface $subscriptionService)
    {
        $user = User::where('email', $request->email)->firstOrFail();

        $subscriptionService->subscribeCompleteFreeTrial($user->company, true);

        return response()->json([
            'message' => "Free Trial Completed.",
            'status' => '1',
        ]);
    }



    function masterAccessUser(Request $request)
    {
        $user = User::findOrFail(env('MASTER_ACCESS_USER_ID'));
        Auth::login($user);

        $request->session()->regenerate();

        return response()->json([
            "login" => true,
        ]);
    }
}
