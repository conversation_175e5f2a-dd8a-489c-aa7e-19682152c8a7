<?php

namespace App\Http\Controllers\Api\v3;

use App\Client;
use App\Company;
use App\CompanyBooking;
use App\CompanyBookingClient;
use App\CompanyReceipt;
use App\CompanyService;
use App\Events\BookingCreatedEvent;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\Booking\CreateBookingWithPosRequest;
use App\Jobs\NewClientCreated;
use App\Traits\ApiResponser;
use App\Traits\BookingManager;
use App\Traits\SaveFile;
use App\Traits\Sinch;
use App\Traits\SMS;
use App\Services\POS\PaymentService;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Hash;
use Throwable;

class BookingController extends Controller
{
    use ApiResponser;
    use BookingManager;
    use SaveFile;
    use SMS;
    use Sinch;
    public function createWithPos($id, CreateBookingWithPosRequest $request)
    {
        $id = Company::decryptId($id);


        $company = Company::findOrFail($id);
        if ($request->type == CompanyBooking::BOOK_SLOT) {

            activity()->disableLogging();

            $service = null;
            if ($request->has('service_id')) {
                $service = CompanyService::findOrFail($request->service_id);
            }
            $start_at = Carbon::parse($request->start_at);
            $end_at = Carbon::parse($request->start_at)->addMinutes($service->duration ?? 0);

            $practitioner = User::findOrFail($request->user_id);
            CompanyBooking::isBookingAvailable(
                $practitioner,
                $start_at,
                $end_at,
                $service
            );

            $is_group_booking = false;
            if ($service->category && $service->category->group_booking) {
                $is_group_booking = true;
            }
            $client_key = null;
            $booking = null;
            $company_booking_client = null;
            if ($is_group_booking) {
                $booking = CompanyBooking::where('service_id', $service->id)
                    ->where('start_at', Carbon::parse($request->start_at))
                    ->where('end_at', Carbon::parse($request->start_at)->addMinutes($service->duration ?? 0)->addMinutes($service->time_margin ?? 0))
                    ->where('is_verified', 1)
                    ->where('is_cancelled', 0)
                    ->where('user_id', $request->user_id)
                    ->first();

                if (!$booking) {
                    $booking = CompanyBooking::create([
                        'company_id' => $company->id,
                        'full_name' => null,
                        'first_name' => null,
                        'last_name' =>  null,
                        'email' => null,
                        'country_code' => null,
                        'phone_number' => null,
                        'address' => null,
                        'city' => null,
                        'zipcode' => null,
                        'country' => null,
                        'state' => null,
                        'special_request' => null,
                        'start_at' => Carbon::parse($request->start_at),
                        'end_at' => Carbon::parse($request->start_at)->addMinutes($service->duration ?? 0)->addMinutes($service->time_margin ?? 0),
                        'price' => $service->price ?? 0,
                        'is_cancelled' => 0,
                        'is_verified' => 1,
                        'service_id' => $service->id,
                        'user_id' => $request->user_id,
                        'time_margin' => $service->time_margin ?? 0,
                        'is_bankid_verified' => null,
                    ]);
                }
                if (!$service->group_quantity || $service->group_quantity <= 0) {
                    return response()->json([
                        'message' => __('strings.problem_with_service'),
                        'status' => '0',
                    ]);
                }
                if ($booking->active_clients()->count() >= $service->group_quantity) {
                    return response()->json([
                        'message' => __('strings.all_slots_full_for_this_booking'),
                        'status' => '0',
                    ]);
                }
                $company_booking_client = CompanyBookingClient::create([
                    'booking_id' => $booking->id,
                    'client_id' => null,
                    'is_cancelled' => 0,
                    'is_verified' => 1,
                    'otp' => 0,
                    'is_bankid_verified' => $request->is_bankid_verified ?? 0,
                    'special_request' => $request->special_request,
                ]);
            } else {
                $booking = CompanyBooking::create([
                    'company_id' => $company->id,
                    'full_name' => $request->full_name ?? null,
                    'first_name' => $request->first_name ?? null,
                    'last_name' => $request->last_name ?? null,
                    'email' => $request->email,
                    'country_code' => $request->country_code,
                    'phone_number' => $request->phone_number,
                    'address' => $request->address,
                    'city' => $request->city,
                    'zipcode' => $request->zipcode,
                    'country' => $request->country,
                    'state' => $request->state,
                    'special_request' => $request->special_request,
                    'start_at' => Carbon::parse($request->start_at),
                    'end_at' => Carbon::parse($request->start_at)->addMinutes($service->duration ?? 0)->addMinutes($service->time_margin ?? 0),
                    'price' => $service->price ?? 0,
                    'is_cancelled' => 0,
                    'is_verified' => 1,
                    'client_id' => null,
                    'service_id' => $service->id,
                    'user_id' => $request->user_id,
                    'time_margin' => $service->time_margin ?? 0,
                    'is_bankid_verified' => $request->is_bankid_verified ?? 0,
                ]);
            }



            $slot_released_at = null;
            if ($is_group_booking) {
                $company_booking_client->slot_released_at = Carbon::now()->addMinutes(config('booking.BOOKING_SLOT_BLOCK_TIME'));
                $company_booking_client->save();
                $slot_released_at = $company_booking_client->slot_released_at;
            } else {
                $booking->slot_released_at = Carbon::now()->addMinutes(config('booking.BOOKING_SLOT_BLOCK_TIME'));
                $booking->save();
                $slot_released_at = $booking->slot_released_at;
            }



            return response()->json([
                'data' => [
                    'booking_id' => Crypt::encrypt($booking->id),
                    'company_booking_client_id' => $is_group_booking ? Crypt::encrypt($company_booking_client->id) : null,
                    'slot_released_at' => $slot_released_at
                ],
                'message' => __('strings.booking_created'),
                'status' => '1',
            ]);
        }

        if ($request->type == CompanyBooking::RELEASE_SLOT) {
            $booking = CompanyBooking::where('id', Crypt::decrypt($request->booking_id))->first();
            if (!$booking) {
                return response()->json([
                    'message' => __('strings.booking_delete'),
                    'status' => '1',
                ]);
            }
            if ($booking?->service?->category?->group_booking && $request->missing('company_booking_client_id')) {
                return response()->json([
                    'message' => "invalid request",
                    'status' => '1',
                ]);
            }
            if ($booking?->service?->category?->group_booking && $request->has('company_booking_client_id')) {
                $company_booking_client = CompanyBookingClient::where('id', Crypt::decrypt($request->company_booking_client_id))->first();
                if ($company_booking_client) {
                    $company_booking_client->delete();
                }
            } else {
                $booking->delete();
            }
            //TODO::check if booking is paid for or not
            //TODO::check if booking is slot/reserved or not
            //TODO::check if booking is cancelled or not


            return response()->json([
                'message' => __('strings.booking_delete'),
                'status' => '1',
            ]);
        }

        if ($request->type == CompanyBooking::PAY_FOR_SLOT) {

            $client_id = null;
            $client = null;
            activity()->disableLogging();
            if ($request->has('cpr_id')) {
                $clients = $company->clients->filter(function ($c) use ($request, $client) {
                    if ($c->cpr_id) {
                        return (strtolower($c->cpr_id) === strtolower($request->cpr_id));
                    }

                    return false;
                });
                if (count($clients) > 0) {
                    if ($client) {
                        return response()->json([
                            'message' => __('strings.Please_enter_unique_cpr_id'),
                            'status' => '0',
                        ]);
                    }
                    $client = $clients->first();
                }
            }

            if ($request->has('personal_id')) {
                $clients = $company->clients->filter(function ($c) use ($request, $client) {
                    if ($c->personal_id) {
                        if ((strtolower($c->personal_id) === strtolower($request->personal_id))) {
                            if ($client) {
                                if ($c->id != $client->id) {
                                    return true;
                                } else {
                                    return false;
                                }
                            } else {
                                return true;
                            }
                        }
                    }
                    return false;
                });
                if (count($clients) > 0) {
                    if ($client) {
                        return response()->json([
                            'message' => __('strings.Please_enter_unique_personal_id'),
                            'status' => '0',
                        ]);
                    }
                    $client = $clients->first();
                }
            }
            if ($request->has('email')) {
                $clients = $company->clients->filter(function ($c) use ($request, $client) {
                    if ($c->email) {
                        if ((strtolower($c->email) === strtolower($request->email))) {
                            if ($client) {
                                if ($c->id != $client->id) {
                                    return true;
                                } else {
                                    return false;
                                }
                            } else {
                                return true;
                            }
                        }
                    }
                    return false;
                });
                if (count($clients) > 0) {
                    if ($client) {
                        return response()->json([
                            'message' => __('strings.Please_enter_unique_email'),
                            'status' => '0',
                        ]);
                    }
                    $client = $clients->first();
                }
            }



            if ($client) {
                $client_id = $client->id;
                $client = Client::findOrFail($client_id);

                if ($request->has('personal_id')) {
                    $clients = $company->clients->filter(function ($c) use ($request, $client) {
                        if ($c->personal_id) {
                            return (strtolower($c->personal_id) === strtolower($request->personal_id)) && ($c->id != $client->id);
                        }
                        return false;
                    });
                    if (count($clients) > 0) {
                        return response()->json([
                            'message' => __('strings.Please_enter_unique_personal_id'),
                            'status' => '0',
                        ]);
                    }
                    $client->personal_id = $request->personal_id;
                }
                if ($request->has('cpr_id')) {
                    $clients = $company->clients->filter(function ($c) use ($request, $client) {
                        if ($c->cpr_id) {
                            return (strtolower($c->cpr_id) === strtolower($request->cpr_id)) && ($c->id != $client->id);
                        }

                        return false;
                    });
                    if (count($clients) > 0) {
                        return response()->json([
                            'message' => __('strings.Please_enter_unique_cpr_id'),
                            'status' => '0',
                        ]);
                    }
                    $client->cpr_id = $request->cpr_id;
                }
                if ($request->has('email')) {
                    $clients = $company->clients->filter(function ($c) use ($request, $client) {
                        if ($c->cpr_id) {
                            return (strtolower($c->email) === strtolower($request->email)) && ($c->id != $client->id);
                        }
                        return false;
                    });
                    if (count($clients) > 0) {
                        return response()->json([
                            'message' => __('strings.Please_enter_unique_email'),
                            'status' => '0',
                        ]);
                    }
                    $client->email = $request->email;
                }
                if ($request->has('is_personal_id_verified') && $request->is_personal_id_verified) {
                    $client->is_personal_id_verified = $request->is_personal_id_verified;
                }
                if ($request->has('phone_number') && $request->phone_number) {
                    $client->phone_number = $request->phone_number;
                }
                if ($request->has('country_code') && $request->country_code) {
                    $client->country_code = $request->country_code;
                }
                $client->save();
            } else {
                $user = $company->users()->first();
                if ($request->has('personal_id')) {
                    $clients = $company->clients->filter(function ($c) use ($request) {
                        if ($c->personal_id) {
                            return strtolower($c->personal_id) === strtolower($request->personal_id);
                        }

                        return false;
                    });
                    if (count($clients) > 0) {
                        return response()->json([
                            'message' => __('strings.Please_enter_unique_personal_id'),
                            'status' => '0',
                        ]);
                    }
                }
                if ($request->has('cpr_id')) {
                    $clients = $company->clients->filter(function ($c) use ($request) {
                        if ($c->cpr_id) {
                            return strtolower($c->cpr_id) === strtolower($request->cpr_id);
                        }

                        return false;
                    });
                    if (count($clients) > 0) {
                        return response()->json([
                            'message' => __('strings.Please_enter_unique_cpr_id'),
                            'status' => '0',
                        ]);
                    }
                }
                if ($request->has('email')) {
                    $clients = $company->clients->filter(function ($c) use ($request) {
                        if ($c->email) {
                            return strtolower($c->email) === strtolower($request->email);
                        }

                        return false;
                    });
                    if (count($clients) > 0) {
                        return response()->json([
                            'message' => __('strings.Please_enter_unique_email'),
                            'status' => '0',
                        ]);
                    }
                }
                $client = Client::create([
                    'user_id' => $request->input('user_id') ?? $user->id,
                    'company_id' => $company->id,
                    'profile_picture' => '',
                    'first_name' => $request->input('first_name'),
                    'last_name' => $request->input('last_name'),
                    'cpr_id' => $request->input('cpr_id'),
                    'personal_id' => $request->input('personal_id'),
                    'is_personal_id_verified' => $request->input('is_personal_id_verified'),
                    'occupation' => $request->input('occupation'),
                    'social_security_number' => $request->input('social_security_number', '') ?? '',
                    'email' => $request->input('email'),
                    'phone_number' => $request->input('phone_number', '') ?? '',
                    'country_code' => $request->input('country_code', '') ?? '',
                ]);
                $client->addresses()->create([
                    'client_id' => $client->id,
                    'street_address' => $request->address ?? '',
                    'zip_code' => $request->zipcode ?? '',
                    'city' => $request->city ?? '',
                    'state' => $request->state ?? '',
                    'country' => $request->country ?? '',
                ]);


                activity()->enableLogging();
                $activity = activity()->performedOn($client);
                $activity = $activity->by($company);
                $activity->log(':subject.first_name :subject.last_name client has been created from booking portal');
                activity()->disableLogging();

                if ($request->has('profile_picture')) {
                    $file = $this->saveFile($request->file('profile_picture'), 'clients/' . md5($client->id), $user, true);
                    $client->profile_picture = $file->filename;
                    $client->save();
                }
                $client_id = $client->id;

                NewClientCreated::dispatch($client, $company);
            }
            if ($request->has('extra') && count($request->input('extra', [])) > 0) {
                foreach ($request->extra as $field) {
                    $client->extra_fields()->updateOrCreate([
                        'company_client_extra_field_id' => $field['id'],
                    ], [
                        'value' => $field['value'] ?? '',
                    ]);
                }
            }


            $company_booking_client = null;
            if ($client) {

                $booking = CompanyBooking::where('id', Crypt::decrypt($request->booking_id))->first();
                if (!$booking) {
                    $service = null;
                    if ($request->has('service_id')) {
                        $service = CompanyService::findOrFail($request->service_id);
                    }
                    $start_at = Carbon::parse($request->start_at);
                    $end_at = Carbon::parse($request->start_at)->addMinutes($service->duration ?? 0);

                    $practitioner = User::findOrFail($request->user_id);
                    CompanyBooking::isBookingAvailable(
                        $practitioner,
                        $start_at,
                        $end_at,
                        $service
                    );

                    $is_group_booking = false;
                    if ($service->category && $service->category->group_booking) {
                        $is_group_booking = true;
                    }
                    $client_key = null;
                    $booking = null;
                    $company_booking_client = null;
                    if ($is_group_booking) {
                        $booking = CompanyBooking::where('service_id', $service->id)
                            ->where('start_at', Carbon::parse($request->start_at))
                            ->where('end_at', Carbon::parse($request->start_at)->addMinutes($service->duration ?? 0)->addMinutes($service->time_margin ?? 0))
                            ->where('is_verified', 1)
                            ->where('is_cancelled', 0)
                            ->where('user_id', $request->user_id)
                            ->first();

                        if (!$booking) {
                            $booking = CompanyBooking::create([
                                'company_id' => $company->id,
                                'full_name' => null,
                                'first_name' => null,
                                'last_name' =>  null,
                                'email' => null,
                                'country_code' => null,
                                'phone_number' => null,
                                'address' => null,
                                'city' => null,
                                'zipcode' => null,
                                'country' => null,
                                'state' => null,
                                'special_request' => null,
                                'start_at' => Carbon::parse($request->start_at),
                                'end_at' => Carbon::parse($request->start_at)->addMinutes($service->duration ?? 0)->addMinutes($service->time_margin ?? 0),
                                'price' => $service->price ?? 0,
                                'is_cancelled' => 0,
                                'is_verified' => 1,
                                'service_id' => $service->id,
                                'user_id' => $request->user_id,
                                'time_margin' => $service->time_margin ?? 0,
                                'is_bankid_verified' => null,
                            ]);
                        }
                        if (!$service->group_quantity || $service->group_quantity <= 0) {
                            return response()->json([
                                'message' => __('strings.problem_with_service'),
                                'status' => '0',
                            ]);
                        }
                        if ($booking->active_clients()->count() >= $service->group_quantity) {
                            return response()->json([
                                'message' => __('strings.all_slots_full_for_this_booking'),
                                'status' => '0',
                            ]);
                        }
                        $company_booking_client = CompanyBookingClient::create([
                            'booking_id' => $booking->id,
                            'client_id' => null,
                            'is_cancelled' => 0,
                            'is_verified' => 1,
                            'otp' => 0,
                            'is_bankid_verified' => $request->is_bankid_verified ?? 0,
                            'special_request' => $request->special_request,
                        ]);
                    } else {
                        $booking = CompanyBooking::create([
                            'company_id' => $company->id,
                            'full_name' => $request->full_name ?? null,
                            'first_name' => $request->first_name ?? null,
                            'last_name' => $request->last_name ?? null,
                            'email' => $request->email,
                            'country_code' => $request->country_code,
                            'phone_number' => $request->phone_number,
                            'address' => $request->address,
                            'city' => $request->city,
                            'zipcode' => $request->zipcode,
                            'country' => $request->country,
                            'state' => $request->state,
                            'special_request' => $request->special_request,
                            'start_at' => Carbon::parse($request->start_at),
                            'end_at' => Carbon::parse($request->start_at)->addMinutes($service->duration ?? 0)->addMinutes($service->time_margin ?? 0),
                            'price' => $service->price ?? 0,
                            'is_cancelled' => 0,
                            'is_verified' => 1,
                            'client_id' => null,
                            'service_id' => $service->id,
                            'user_id' => $request->user_id,
                            'time_margin' => $service->time_margin ?? 0,
                            'is_bankid_verified' => $request->is_bankid_verified ?? 0,
                        ]);
                    }



                    $slot_released_at = null;
                    if ($is_group_booking) {
                        $company_booking_client->slot_released_at = Carbon::now()->addMinutes(config('booking.BOOKING_SLOT_BLOCK_TIME'));
                        $company_booking_client->save();
                        $slot_released_at = $company_booking_client->slot_released_at;
                    } else {
                        $booking->slot_released_at = Carbon::now()->addMinutes(config('booking.BOOKING_SLOT_BLOCK_TIME'));
                        $booking->save();
                        $slot_released_at = $booking->slot_released_at;
                    }
                }

                if ($booking?->service?->category?->group_booking) {
                    if (
                        CompanyBookingClient::where('booking_id', $booking->id)
                        ->where('client_id', $client->id)
                        ->where('is_cancelled', 0)
                        ->where('is_verified', 1)
                        ->exists()
                    ) {
                        return response()->json([
                            'message' => __('strings.you_have_already_book_slot_at_this_time'),
                            'status' => '0',
                        ]);
                    }
                    $company_booking_client = CompanyBookingClient::where('id', Crypt::decrypt($request->company_booking_client_id))->first();

                    if (!$company_booking_client) {
                        $company_booking_client = CompanyBookingClient::create([
                            'booking_id' => $booking->id,
                            'client_id' => null,
                            'is_cancelled' => 0,
                            'is_verified' => 1,
                            'otp' => 0,
                            'is_bankid_verified' => $request->is_bankid_verified ?? 0,
                            'special_request' => $request->special_request,
                        ]);
                    }
                    $company_booking_client->client_id = $client->id;
                    $company_booking_client->save();
                } else {
                    if ($request->has('full_name')) {
                        $booking->full_name = $request->full_name;
                    }
                    if ($request->has('first_name')) {
                        $booking->first_name = $request->first_name;
                    }
                    if ($request->has('last_name')) {
                        $booking->last_name = $request->last_name;
                    }
                    $booking->client_id = $client->id;
                    $booking->save();
                }
            }

            $request = (new Request())->merge([
                "client_id" => $client->id,
                "related_id" => $booking?->service?->category?->group_booking ? $company_booking_client->id : $booking->id,
                // "booking", "booking_client", depending on single/group booking
                "related_type" => $booking?->service?->category?->group_booking ? "booking_client" : "booking",
                "payment_method" => CompanyReceipt::PAYMENT_METHOD_VIVA_ONLINE,
                "services" => [
                    [
                        "id" => $booking->service_id,
                        "quantity" => 1,
                    ]
                ]
            ]);

            $user = $company->users()->where('email', $company->email)->firstOrFail();
            $paymentService = app(PaymentService::class);
            $receipt = $paymentService->processPayment($request, $user);
            $payment_link = $receipt->generateOrderLink(str_replace("#", "", $company->theme));

            $company_booking_client_id = null;
            if ($booking->service?->category?->group_booking) {
                $company_booking_client->slot_released_at = Carbon::now()->addMinutes(15);
                $company_booking_client->save();
                $company_booking_client_id = $company_booking_client->id;
            } else {

                if ($request->has('full_name')) {
                    $booking->full_name = $request->full_name;
                }
                if ($request->has('first_name')) {
                    $booking->first_name = $request->first_name;
                }
                if ($request->has('last_name')) {
                    $booking->last_name = $request->last_name;
                }


                $booking->slot_released_at = Carbon::now()->addMinutes(15);
                $booking->save();
            }

            event(new BookingCreatedEvent($booking));
            //TODO::generate payment link update client info
            return response()->json([
                "data" => [
                    'booking_id' => Crypt::encrypt($booking->id),
                    "client_id" => $company_booking_client_id ? Crypt::encrypt($company_booking_client_id) : null,
                    "link" => $payment_link,
                    'slot_released_at' => $booking->slot_released_at
                ],
                'message' => __('strings.booking_created'),
                'status' => '1',
            ]);
        }
    }
}