<?php

namespace App\Http\Controllers\Api\v3;

use App\Client;
use App\ClientFileBatch;
use App\ClientLetterOfConsent;
use App\ClientPrescription;
use App\ClientTreatment;
use App\GeneralNote;
use App\Http\Controllers\Controller;
use App\Http\Requests\v3\CreateClientFileBatchRequest;
use App\Http\Requests\v3\GetClientFileBatchListRequest;
use App\Traits\SaveFile;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ClientFileBatchController extends Controller
{
    use SaveFile;
    public function index(Client $client, GetClientFileBatchListRequest $request)
    {
        $client_file_batches = ClientFileBatch::with(['signed_by' => function ($query) {
            $query->setEagerLoads([]);
        }, 'files'])->where('client_id', $client->id);

        switch ($request->signable_type) {
            case 'procedure':
                $client_file_batches->where('signable_type', ClientTreatment::class)->where('signable_id', $request->signable_id);
                break;
            case 'note':
                $client_file_batches->where('signable_type', GeneralNote::class)->where('signable_id', $request->signable_id);
                break;
            case 'loc':
                $client_file_batches->where('signable_type', ClientLetterOfConsent::class)->where('signable_id', $request->signable_id);
                break;
            case 'prescription':
                $client_file_batches->where('signable_type', ClientPrescription::class)->where('signable_id', $request->signable_id);
                break;
            default:
                # code...
                break;
        }


        $client_file_batches->latest();
        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('file_batches.file_batches_returned'),
                    'status' => '1',
                ])->merge($client_file_batches->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $client_file_batches->get(),
                'message' => __('file_batches.file_batches_returned'),
                'status' => '1',
            ]);
        }
    }

    public function store(Client $client, CreateClientFileBatchRequest $request)
    {
        ini_set('max_execution_time', 900);
        $auth_user = Auth::user();
        $client_file_batch = DB::transaction(function () use ($client, $request, $auth_user) {
            $file = $this->saveFile($request->file('sign'), 'clients/' . md5($client->id) . '/file-batches/signs');
            $log_message = null;

            $client_file_batch = null;
            switch ($request->signable_type) {
                case 'procedure':
                    $log_message = "{$auth_user->first_name} {$auth_user->last_name} added new images/files on Procedure in patient {$client->first_name} {$client->last_name}.";
                    $client_treatment = ClientTreatment::findOrFail($request->signable_id);
                    $client_file_batch = ClientFileBatch::create([
                        'client_id' => $client->id,
                        'signed_by_id' => $auth_user->id,
                        'sign_path' => $file->filename,
                        'signed_at' => now(),
                        'signable_type' => ClientTreatment::class,
                        'signable_id' => $client_treatment->id,
                        'description' => $request->description
                    ]);
                    break;
                case 'note':
                    $log_message = "{$auth_user->first_name} {$auth_user->last_name} added new images/files on Note & Files in patient {$client->first_name} {$client->last_name}.";
                    $general_note = GeneralNote::findOrFail($request->signable_id);
                    $client_file_batch = ClientFileBatch::create([
                        'client_id' => $client->id,
                        'signed_by_id' => $auth_user->id,
                        'sign_path' => $file->filename,
                        'signed_at' => now(),
                        'signable_type' => GeneralNote::class,
                        'signable_id' => $general_note->id,
                        'description' => $request->description
                    ]);
                    break;
                case 'loc':
                    $log_message = "{$auth_user->first_name} {$auth_user->last_name} added new images/files on LOC in patient {$client->first_name} {$client->last_name}.";
                    $client_loc = ClientLetterOfConsent::findOrFail($request->signable_id);
                    $client_file_batch = ClientFileBatch::create([
                        'client_id' => $client->id,
                        'signed_by_id' => $auth_user->id,
                        'sign_path' => $file->filename,
                        'signed_at' => now(),
                        'signable_type' => GeneralNote::class,
                        'signable_id' => $client_loc->id,
                        'description' => $request->description
                    ]);
                    break;
                case 'prescription':
                    $log_message = "{$auth_user->first_name} {$auth_user->last_name} added new images/files on Prescription in patient {$client->first_name} {$client->last_name}.";
                    $client_prescription = ClientPrescription::findOrFail($request->signable_id);
                    $client_file_batch = ClientFileBatch::create([
                        'client_id' => $client->id,
                        'signed_by_id' => $auth_user->id,
                        'sign_path' => $file->filename,
                        'signed_at' => now(),
                        'signable_type' => ClientPrescription::class,
                        'signable_id' => $client_prescription->id,
                        'description' => $request->description
                    ]);
                    break;

                default:
                    # code...
                    break;
            }
            dispatch(function () use ($auth_user, $client, $log_message) {
                activity()
                    ->performedOn($client)
                    ->by($auth_user)
                    ->log($log_message);
            });
            if ($request->has('files')) {

                $filenames = [];
                foreach ($request->files as $index => $file) {
                    foreach ($file as $f) {
                        array_push($filenames, $f->getClientOriginalName());
                    }
                }
                $client_file_batch->filenames = $filenames;
                $client_file_batch->save();
                foreach ($request->file('files') as $index => $data) {
                    $file = $this->saveFile($data, 'clients/' . md5($client->id) . '/file-batches', null, false, true);
                    $client_file_batch->files()->save($file);
                }
            }

            return $client_file_batch;
        });


        return response()->json([
            'data' => $client_file_batch->refresh()->loadMissing(['files', 'signed_by']),
            'message' => __('file_batches.file_batch_created'),
            'status' => '1'
        ]);
    }
}
