<?php

namespace App\Http\Controllers\Api;

use App\ClientLetterOfConsent;
use App\ClientTreatment;
use App\Company;
use App\CompanyBooking;
use App\Http\Controllers\Controller;
use App\Mail\GenericBookingMail;
use App\Mail\NewAccountUserMail;
use App\Setting;
use App\Traits\GetEncryptedFile;
use App\Traits\SaveFile;
use App\User;
use Illuminate\Http\Request;
use Aws\S3\S3Client;
use Aws\Exception\AwsException;
use Carbon\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Throwable;
use App\Traits\TimeZoneManager;
use Illuminate\Support\Facades\Cache;
use App\Plan;
use App\Models\Cashier\Subscription;
use App\Traits\BookingManager;
use App\Traits\PlanManager;
use App\UserTimeSlot;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Laravel\Cashier\Exceptions\IncompletePayment;

class TestController extends Controller
{
    use GetEncryptedFile;
    use SaveFile;
    use PlanManager;
    use BookingManager;

    public function getRegPortalURLs()
    {
        $companies = Company::all();
        $csv = "id,company name,encID\n";
        foreach ($companies as $key => $company) {
            $csv = $csv . "$company->id,\"$company->company_name\",$company->encrypted_id\n";
        }
        return "<p style='white-space: pre-wrap;'>$csv</p>";
    }

    public function getWeekDay(Request $request)
    {
        return config('phone_country')['46'];
        return [
            'start_date' => Carbon::now(),
            'start_day' => BookingManager::getWeekDay(Carbon::now()),
            'end_date' => Carbon::now()->addDay(),
            'end_day' => BookingManager::getWeekDay(Carbon::now()->addDay()),
            'lang' => app()->getLocale(),
        ];
    }

    public function syncSubscription(Request $request)
    {
        // $company = Company::findOrFail($request->company_id)->firstOrFail();
        $sub = Subscription::findOrFail($request->subscription_id);
        if ($sub && $request->has('stripe_id') && $request->has('stripe_plan') && $request->has('quantity')) {
            $sub->stripe_id = $request->stripe_id;
            $sub->stripe_plan = $request->stripe_plan;
            $sub->quantity = $request->quantity;
            $sub->stripe_status = 'active';
            $sub->ends_at = null;
            $sub->save();
        }

        return $sub;
    }

    public function updatePlane()
    {
        $free_plans = Plan::where('is_2022', 1)->get()->where('is_free', true);
        foreach ($free_plans as $free_plan) {
            $plan_to_update = Plan::findOrFail($free_plan->id);
            $plan_to_update->client = '21';
            $plan_to_update->save();
        }
        return "dome update";
    }
    public function changePlanToNew(Request $request)
    {
        $companies = Company::query();
        $problematic_companies = [];

        if ($request->has('company_id')) {
            $companies = $companies->where('id', $request->company_id);
        }
        if ($request->has('start_company_id')) {
            $companies = $companies->where('id', '>=', $request->start_company_id);
        }
        if ($request->has('end_company_id')) {
            $companies = $companies->where('id', '<=', $request->end_company_id);
        }

        if ($request->has('show_count_only')) {
            return $companies->count();
        }

        $companies = $companies->cursor();
        if ($request->has('show_all_company')) {
            return $companies;
        }
        foreach ($companies as $company) {
            try {
                PlanManager::updateNewPlanForThisCompany($company, $request);
            } catch (\Throwable $th) {
                array_push($problematic_companies, [
                    "company_id" => $company->id,
                    "reason" => $th->getMessage(),
                ]);
            }
        }
        return $problematic_companies;
    }

    public function test(Request $request)
    {

        $companies_to_change = [
            [
                "company_id" => 5,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_HIoczaOgoznBhQ",
                "old_stripe_id" => "cus_MzC4uxsXU0gu18"
            ],
            [
                "company_id" => 10,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_HLOQ6f1qBDQCMM",
                "old_stripe_id" => "cus_MzC4jzOhEd0R0T"
            ],
            [
                "company_id" => 20,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_HPoCeTYjZOpb5h",
                "old_stripe_id" => "cus_MzC7k9fZ0Be1M3"
            ],
            [
                "company_id" => 23,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_HSQSJD9xBSRDQQ",
                "old_stripe_id" => "cus_MzC72m2qz0Soap"
            ],
            [
                "company_id" => 26,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_HTwObznB6AzkfI",
                "old_stripe_id" => "cus_MzC7oy87pwGvQt"
            ],
            [
                "company_id" => 31,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_HWZ0UI48elkzMY"
            ],
            [
                "company_id" => 33,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_HYZBQc37Wz7IDQ",
                "old_stripe_id" => "cus_MzC8No4HjOlvaW"
            ],
            [
                "company_id" => 34,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_HaUAdsrcT7c4Cl",
                "old_stripe_id" => "cus_L2J0pwDlTPBGDW"
            ],
            [
                "company_id" => 35,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_HafP6P1a0Ibnp2",
                "old_stripe_id" => "cus_MzC8BlrleH9J3m"
            ],
            [
                "company_id" => 36,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_HcYTRfEncwf7VJ",
                "old_stripe_id" => "cus_MzC8huGjOshUXe"
            ],
            [
                "company_id" => 37,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_HeQopibA3pTm42",
                "old_stripe_id" => "cus_MzC8vw6xeG1FhT"
            ],
            [
                "company_id" => 38,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_HeXjZs1p9vJjnU",
                "old_stripe_id" => "cus_MzC8xIsvWikc7I"
            ],
            [
                "company_id" => 39,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_HecRVQggxlxVJH",
                "old_stripe_id" => "cus_MzC88nzOHktRct"
            ],
            [
                "company_id" => 40,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Hgkpyk0EKUGJUJ",
                "old_stripe_id" => "cus_MzC8MpdyYjvuhu"
            ],
            [
                "company_id" => 41,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Hgs01BWMHk4vU9",
                "old_stripe_id" => "cus_MzC8ZptuJ76sQS"
            ],
            [
                "company_id" => 42,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_HjUe2KNTbcDwJe",
                "old_stripe_id" => "cus_MzC8vYhp3aQ6nQ"
            ],
            [
                "company_id" => 43,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_HjqIN4QEPmMH8h",
                "old_stripe_id" => "cus_MzC81z6KE7jIrd"
            ],
            [
                "company_id" => 44,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_HkmTI9suFt4RJh",
                "old_stripe_id" => "cus_MzC8NEQaBYXsmV"
            ],
            [
                "company_id" => 45,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Hs1BndcpKnQDNj",
                "old_stripe_id" => "cus_MzC8JuP2c1V2Gu"
            ],
            [
                "company_id" => 46,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_HsUmrAsfnqdAWv",
                "old_stripe_id" => "cus_MzC835VHBN5Sbi"
            ],
            [
                "company_id" => 47,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_Hu54ONIuhKLNY8"
            ],
            [
                "company_id" => 48,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_HxItzBJCu8YKBf",
                "old_stripe_id" => "cus_MzC8mpq33NTt1X"
            ],
            [
                "company_id" => 50,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_I0icO03dDIUSiN",
                "old_stripe_id" => "cus_MzC8U42X5Nu46o"
            ],
            [
                "company_id" => 51,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_I1iYm9Om58ikUg",
                "old_stripe_id" => "cus_MzCAPHFOF5G5I8"
            ],
            [
                "company_id" => 52,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_I29JPE0RrskqmT",
                "old_stripe_id" => "cus_MzCAf5HLuURkLy"
            ],
            [
                "company_id" => 53,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_I2yYSTfMpfQOxt",
                "old_stripe_id" => "cus_MzCAgDzWdTwGjW"
            ],
            [
                "company_id" => 54,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_I3fiXHdY4JmrSH",
                "old_stripe_id" => "cus_MzCAH7qH7eO31N"
            ],
            [
                "company_id" => 55,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_I3iazfJZpt6SFA",
                "old_stripe_id" => "cus_MzCAhTa2BrvaR2"
            ],
            [
                "company_id" => 56,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JVm7AdSvaBnOPl",
                "old_stripe_id" => "cus_JbNdiDDv3LfVHj"
            ],
            [
                "company_id" => 58,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_I7jIib3KNclGRm",
                "old_stripe_id" => "cus_MzCAyKVIiJRHN2"
            ],
            [
                "company_id" => 60,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_I8SezvVuVJ6t0M",
                "old_stripe_id" => "cus_MzCAZQ479eAwNF"
            ],
            [
                "company_id" => 61,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_I8unpiRQQv3Hq6",
                "old_stripe_id" => "cus_MpqlI7ZAfJbldm"
            ],
            [
                "company_id" => 62,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IBY7K3JNZ20zRQ",
                "old_stripe_id" => "cus_MzCKej8Z9PqwTe"
            ],
            [
                "company_id" => 63,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_IBsx0tKq3Wnyun"
            ],
            [
                "company_id" => 64,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IBurAt7I9mDNVd",
                "old_stripe_id" => "cus_MzCK8z7KP2fgbs"
            ],
            [
                "company_id" => 65,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IC3hgbVGX4R8gp",
                "old_stripe_id" => "cus_MzCKrwpDOXqTro"
            ],
            [
                "company_id" => 66,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ICDpMMxpk8XFRU",
                "old_stripe_id" => "cus_MK0m0ZMJAZWyEW"
            ],
            [
                "company_id" => 67,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ICDs9cJhBtaY97",
                "old_stripe_id" => "cus_MzCLfWAxEklk8E"
            ],
            [
                "company_id" => 68,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ICGvbKtbvgQsDk",
                "old_stripe_id" => "cus_MzCL45Y8JuNYBt"
            ],
            [
                "company_id" => 69,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ICJ8mzpvF2BbWj",
                "old_stripe_id" => "cus_MzCLtJtJebk2Qu"
            ],
            [
                "company_id" => 70,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ICWHaJiBWeYSu2",
                "old_stripe_id" => "cus_MzCLJImU28IGCl"
            ],
            [
                "company_id" => 71,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ICfzv75XBKunGW",
                "old_stripe_id" => "cus_MzCL0WUF7XwNEW"
            ],
            [
                "company_id" => 73,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_IDAqllCD6LDVk0"
            ],
            [
                "company_id" => 74,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_IDCKXLtC7vdi8p"
            ],
            [
                "company_id" => 75,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IDJFe4YYqxMM27",
                "old_stripe_id" => "cus_MzCLeQM6LLoyNn"
            ],
            [
                "company_id" => 76,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IDa6MegTFrp94Q",
                "old_stripe_id" => "cus_MzCLCqYC3NTzTM"
            ],
            [
                "company_id" => 77,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_IEIyUN5NY9V36p"
            ],
            [
                "company_id" => 79,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IEqNbv6UjXGN1z",
                "old_stripe_id" => "cus_LIUeICsIivRT97"
            ],
            [
                "company_id" => 80,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IEvd7ubZkcca14",
                "old_stripe_id" => "cus_MzCLE4pjAVbnfs"
            ],
            [
                "company_id" => 81,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IGKt2sUPOkKHtu",
                "old_stripe_id" => "cus_MzCLSGjnk2vA50"
            ],
            [
                "company_id" => 82,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IGdEp4fxdJB7hZ",
                "old_stripe_id" => "cus_MzCLC4yjo0diIR"
            ],
            [
                "company_id" => 83,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IGgTe7MG8xRK9R",
                "old_stripe_id" => "cus_MzCLSQjMcLLyMC"
            ],
            [
                "company_id" => 87,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IHAl443NpEPJzA",
                "old_stripe_id" => "cus_MzCLnvWtz2u5mQ"
            ],
            [
                "company_id" => 88,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IHAlGGEh9iROJ1",
                "old_stripe_id" => "cus_MzCLFgIKMvTP50"
            ],
            [
                "company_id" => 89,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IHBSu6vD8esCv2",
                "old_stripe_id" => "cus_MzCL3TJTiSdf66"
            ],
            [
                "company_id" => 91,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_IHJOOFS6id3EN1"
            ],
            [
                "company_id" => 93,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IHnA11xxjupqJI",
                "old_stripe_id" => "cus_MzCLtpd6GInGuA"
            ],
            [
                "company_id" => 94,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IHxpGiO2aR2CNK",
                "old_stripe_id" => "cus_MzCLli8qWqc4XU"
            ],
            [
                "company_id" => 95,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IJ3blbISVqhgWU",
                "old_stripe_id" => "cus_MzCMF5GyJmCr9x"
            ],
            [
                "company_id" => 96,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IJB0J8WbAJEeRR",
                "old_stripe_id" => "cus_MzCMyNbaJLYVS6"
            ],
            [
                "company_id" => 98,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IJEBEz2qvAy6ff",
                "old_stripe_id" => "cus_MzCM7rqpr2L5WI"
            ],
            [
                "company_id" => 99,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IJISui6xFkg7Hn",
                "old_stripe_id" => "cus_MzCMsQph9XpPNl"
            ],
            [
                "company_id" => 101,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IJJq1dGfG3liiv",
                "old_stripe_id" => "cus_LLr6RlojCEeF9A"
            ],
            [
                "company_id" => 103,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IJgzkGUdC9PlFg",
                "old_stripe_id" => "cus_MzCM1JllFilKoK"
            ],
            [
                "company_id" => 104,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IJksYOeOBuMRTy",
                "old_stripe_id" => "cus_MzCMzMxGop2QNH"
            ],
            [
                "company_id" => 105,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IJpBIHBUeDQFUM",
                "old_stripe_id" => "cus_MzCM1j6wsiRgta"
            ],
            [
                "company_id" => 106,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IJqSD9hEgjfFSm",
                "old_stripe_id" => "cus_MzCMSMyuhjHehD"
            ],
            [
                "company_id" => 107,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IK6Sbz62l8aDBx",
                "old_stripe_id" => "cus_MzCMZLy3XsRbCN"
            ],
            [
                "company_id" => 109,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IKAWuR7fLiM39f",
                "old_stripe_id" => "cus_MzCMkFmPTg0RyA"
            ],
            [
                "company_id" => 111,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IKJTG5QIsZLVBY",
                "old_stripe_id" => "cus_MzCMgpOG7Ksf0y"
            ],
            [
                "company_id" => 112,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IKPqciqanVGrTO",
                "old_stripe_id" => "cus_MzCNvRavw1TqRL"
            ],
            [
                "company_id" => 113,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IKQnF2lsR01zVy",
                "old_stripe_id" => "cus_MzCNEX0mnobxeb"
            ],
            [
                "company_id" => 116,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ILBJwdV8xfqTc5",
                "old_stripe_id" => "cus_JigCT28N1ut3a4"
            ],
            [
                "company_id" => 117,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IMiwNjip2AUzX8",
                "old_stripe_id" => "cus_MzCNNEpyPTPitk"
            ],
            [
                "company_id" => 118,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IMo2aiQLG78YZU",
                "old_stripe_id" => "cus_MzCNGyl94SRPy3"
            ],
            [
                "company_id" => 120,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_IO919p7UhatgqD"
            ],
            [
                "company_id" => 121,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IOAofip64ztgTz",
                "old_stripe_id" => "cus_MzCN9jen86Ztdf"
            ],
            [
                "company_id" => 122,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IOLfpK5satP9MV",
                "old_stripe_id" => "cus_MzCN3TLxHEIU2v"
            ],
            [
                "company_id" => 123,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IOTd9h8FXBwpcz",
                "old_stripe_id" => "cus_MzCNpRlm8NSLck"
            ],
            [
                "company_id" => 124,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IOdmHuDeaDDMMU",
                "old_stripe_id" => "cus_MzCNYKHnTDNp5n"
            ],
            [
                "company_id" => 125,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IPjbKpmgNiHVUI",
                "old_stripe_id" => "cus_MzCNV3AHoQoWLq"
            ],
            [
                "company_id" => 126,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IQ0dGaunLdTbiM",
                "old_stripe_id" => "cus_MzCNp47RFsinRy"
            ],
            [
                "company_id" => 127,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IQN4qUtE1DDp5k",
                "old_stripe_id" => "cus_MzCNuiKZgErUBi"
            ],
            [
                "company_id" => 128,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IQVwHFn4CLvKY1",
                "old_stripe_id" => "cus_MzCNyh2FRiK7Tw"
            ],
            [
                "company_id" => 129,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IR6lQi5kNC3pm4",
                "old_stripe_id" => "cus_MzCNuRhGkUo4qn"
            ],
            [
                "company_id" => 130,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IR8kZGSYRxSJy3",
                "old_stripe_id" => "cus_MzCN9HWUnq05aV"
            ],
            [
                "company_id" => 132,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IRa3gU2CR8IG2V",
                "old_stripe_id" => "cus_MzCNVn8nQgvTdG"
            ],
            [
                "company_id" => 133,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IReqCmselUzaRR",
                "old_stripe_id" => "cus_MzCNv2Xb1jpqq2"
            ],
            [
                "company_id" => 134,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IRm6zShqXcNK78",
                "old_stripe_id" => "cus_M2GgkljD79rJRN"
            ],
            [
                "company_id" => 137,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ISKBaTTri7Extm",
                "old_stripe_id" => "cus_MzCNpUTHKda1Xx"
            ],
            [
                "company_id" => 138,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ISQrXwR33nF8V7",
                "old_stripe_id" => "cus_MzCNOAvZ7NLm1c"
            ],
            [
                "company_id" => 140,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ISxeMxEPJeTU7S",
                "old_stripe_id" => "cus_MzCNlWlKZBXD8y"
            ],
            [
                "company_id" => 142,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IT5s7gs9HrNX5A",
                "old_stripe_id" => "cus_MzCOblyrX5DiRm"
            ],
            [
                "company_id" => 144,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ITRgxrjXGwzov8",
                "old_stripe_id" => "cus_MzCOHwC5tpy9et"
            ],
            [
                "company_id" => 145,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ITlIprvhudJc4d",
                "old_stripe_id" => "cus_MzCOWnXwTQh8oM"
            ],
            [
                "company_id" => 147,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IUD9SPfaUDYOF1",
                "old_stripe_id" => "cus_MzCOvOFB1fNGfx"
            ],
            [
                "company_id" => 148,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IUIprkhxEOvo7N",
                "old_stripe_id" => "cus_MzAhdGKnp4T5vB"
            ],
            [
                "company_id" => 149,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IUpcKrA3aEScin",
                "old_stripe_id" => "cus_MzAhENAnhhQSeY"
            ],
            [
                "company_id" => 151,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IVR1pDAfrYORN1",
                "old_stripe_id" => "cus_MzAhyDVbk643lL"
            ],
            [
                "company_id" => 152,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IVTqe8NkdBz2ix",
                "old_stripe_id" => "cus_MzAhg293dNftIp"
            ],
            [
                "company_id" => 153,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IVas8SmaocOTUj",
                "old_stripe_id" => "cus_MzAhMjHCb9x5qn"
            ],
            [
                "company_id" => 154,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IVhKAU513ru7Yj",
                "old_stripe_id" => "cus_MzAhIzua7GVegV"
            ],
            [
                "company_id" => 155,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IVlogwfoBu6v3E",
                "old_stripe_id" => "cus_MzAiKlTqm91TCD"
            ],
            [
                "company_id" => 157,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IVnUxHniex8LiU",
                "old_stripe_id" => "cus_MzAiOSWLa3gXzj"
            ],
            [
                "company_id" => 158,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IVzVrcScSi4qwK",
                "old_stripe_id" => "cus_MzAiiAFaBqBDfy"
            ],
            [
                "company_id" => 159,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IWAt021fmagoeS",
                "old_stripe_id" => "cus_MzAiqXY7CDucqP"
            ],
            [
                "company_id" => 160,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IWBiDCFvdCztGS",
                "old_stripe_id" => "cus_MzAifQLM963RTc"
            ],
            [
                "company_id" => 161,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IWXLa9OLZMicp4",
                "old_stripe_id" => "cus_MzAitUpLOnUrD4"
            ],
            [
                "company_id" => 162,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IWkLN7kHHn2IhU",
                "old_stripe_id" => "cus_MzAiBQMMwqivcm"
            ],
            [
                "company_id" => 163,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IWkqjdAFX0MNwj",
                "old_stripe_id" => "cus_MzAiPtGtZZVN2t"
            ],
            [
                "company_id" => 164,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IWkvyG6FjgbYqG",
                "old_stripe_id" => "cus_MzAiJLWgKleuxK"
            ],
            [
                "company_id" => 166,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IWpXmrqU9OjjK1",
                "old_stripe_id" => "cus_MzAiEWOuXlWRaE"
            ],
            [
                "company_id" => 167,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IX942YGugIAP3n",
                "old_stripe_id" => "cus_MzAifbEZyi3LXH"
            ],
            [
                "company_id" => 168,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IXQ5FM38XrRVsp",
                "old_stripe_id" => "cus_MzAikTq7VOcMeI"
            ],
            [
                "company_id" => 169,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IXcooAwcp6tuHm",
                "old_stripe_id" => "cus_MzAiMNoZwAGavb"
            ],
            [
                "company_id" => 170,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IXfTP0ECq7mEqf",
                "old_stripe_id" => "cus_MzAiEA9L3RJ5xw"
            ],
            [
                "company_id" => 171,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IXnakGO5u7SWWP",
                "old_stripe_id" => "cus_MzAiCqdVSErWEz"
            ],
            [
                "company_id" => 172,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IXq9vvLfHQEfDz",
                "old_stripe_id" => "cus_MzAjUFizlI5UNH"
            ],
            [
                "company_id" => 174,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IYy25M39B13MSs",
                "old_stripe_id" => "cus_MzAjNFcCpsGKQw"
            ],
            [
                "company_id" => 175,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IZ6uUYmnydnA3M",
                "old_stripe_id" => "cus_MzAjUwZu9LB5wD"
            ],
            [
                "company_id" => 178,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IZtcbWsjg59SCu",
                "old_stripe_id" => "cus_MzAjsSabh2Amra"
            ],
            [
                "company_id" => 179,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IaB3G4vEEn6J62",
                "old_stripe_id" => "cus_MzAjLhmP18IllQ"
            ],
            [
                "company_id" => 180,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IaBs0xvnyrltHr",
                "old_stripe_id" => "cus_Km6yjZPFohxLHB"
            ],
            [
                "company_id" => 181,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IaHSJ69sZqb5Cx",
                "old_stripe_id" => "cus_MzAjFQDoL2CQwD"
            ],
            [
                "company_id" => 182,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IaIIM7ITJO9s4X",
                "old_stripe_id" => "cus_MzAjXZhjP2Y49K"
            ],
            [
                "company_id" => 183,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IaYCK70J0KhN9M",
                "old_stripe_id" => "cus_MzAjq4ncjKMDYS"
            ],
            [
                "company_id" => 185,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Iao1HHAEmyiOgF",
                "old_stripe_id" => "cus_MzAjzOQszrk8sN"
            ],
            [
                "company_id" => 186,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IbNHz2c8cVU4tv",
                "old_stripe_id" => "cus_MzAjymJlfFOsr0"
            ],
            [
                "company_id" => 187,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IbUBzMiWHlI5k3",
                "old_stripe_id" => "cus_MzAjnc3cIIqRdw"
            ],
            [
                "company_id" => 189,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IcXM1JhA93T5ih",
                "old_stripe_id" => "cus_MzAjEJOXUS4HDf"
            ],
            [
                "company_id" => 190,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IczG2mCA0L9rm5",
                "old_stripe_id" => "cus_MzAjmKiG6aHkZF"
            ],
            [
                "company_id" => 192,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IddADAlzyk5QWC",
                "old_stripe_id" => "cus_MzAj2pksna6ILo"
            ],
            [
                "company_id" => 194,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IeCgWTlU3XaCkO",
                "old_stripe_id" => "cus_MzAkB6uxDfILCo"
            ],
            [
                "company_id" => 196,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IelGtRM1b31p0D",
                "old_stripe_id" => "cus_MzAkOlnlujwGCt"
            ],
            [
                "company_id" => 197,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IezhXQ2AG2qm6x",
                "old_stripe_id" => "cus_MzAkFfG71m6EwV"
            ],
            [
                "company_id" => 198,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_If9WI2bWhujFWM",
                "old_stripe_id" => "cus_MzAkRr71beTUIE"
            ],
            [
                "company_id" => 199,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IfItd3n4SDdI1j",
                "old_stripe_id" => "cus_MzAkwHbzxl3M8g"
            ],
            [
                "company_id" => 200,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IfjvqIFCncF5rQ",
                "old_stripe_id" => "cus_MY914xt5SEHYAx"
            ],
            [
                "company_id" => 201,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IgDbvFqNkDbYUc",
                "old_stripe_id" => "cus_MzAkYw2bLQMl2l"
            ],
            [
                "company_id" => 202,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IgU43FaDTn3VAY",
                "old_stripe_id" => "cus_MzAkAVvLyUkSMd"
            ],
            [
                "company_id" => 203,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IgUhb5SunOjUA1",
                "old_stripe_id" => "cus_MzAkOdg9IpMfeu"
            ],
            [
                "company_id" => 204,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Igwz5K6bXB5kDE",
                "old_stripe_id" => "cus_MzAk6slf0olB29"
            ],
            [
                "company_id" => 205,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Ih2ArknFBoVBrF",
                "old_stripe_id" => "cus_MzAkvhNBeAbxx5"
            ],
            [
                "company_id" => 206,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IhIqZnmt3cMflU",
                "old_stripe_id" => "cus_MzAkuMuSIBdgg8"
            ],
            [
                "company_id" => 207,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Ihl93U3sChrZT1",
                "old_stripe_id" => "cus_MzAkeebEBrYVgc"
            ],
            [
                "company_id" => 208,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IiAs847RUfRSUD",
                "old_stripe_id" => "cus_MzAkMaQ0aLe185"
            ],
            [
                "company_id" => 211,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IjGYyqHhXjvL17",
                "old_stripe_id" => "cus_MzAlMniImAL0xr"
            ],
            [
                "company_id" => 213,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Ijez8l7rpBm4M8",
                "old_stripe_id" => "cus_MzAlgcHbP8y1FW"
            ],
            [
                "company_id" => 214,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_IjliqL5RwHWOzF"
            ],
            [
                "company_id" => 215,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IkJAlWUpVIo0Od",
                "old_stripe_id" => "cus_MzAl6os0h4gmSD"
            ],
            [
                "company_id" => 216,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IkMQ2pby7ewjcO",
                "old_stripe_id" => "cus_MzAlVAkMW4h0H7"
            ],
            [
                "company_id" => 217,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IkPQFnxI5bisWo",
                "old_stripe_id" => "cus_MzAl64LGPl9PPc"
            ],
            [
                "company_id" => 218,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Ikg664qEtLfWDi",
                "old_stripe_id" => "cus_MzAlig8Cor72QX"
            ],
            [
                "company_id" => 220,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IkyVxxzOZaDNhT",
                "old_stripe_id" => "cus_MzAlrt7OkkKFEW"
            ],
            [
                "company_id" => 221,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Il0e0HAjeEkKbV",
                "old_stripe_id" => "cus_MzAlgFtlVTFDQT"
            ],
            [
                "company_id" => 222,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ImDQ024TLzVgK0",
                "old_stripe_id" => "cus_MzAlv1Qqmvl3iF"
            ],
            [
                "company_id" => 223,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ImSjkBbVP86Ieq",
                "old_stripe_id" => "cus_MzAl0gjXfaRhBy"
            ],
            [
                "company_id" => 224,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ImTHPpHxUZFoxM",
                "old_stripe_id" => "cus_MzAldTosgz1QzZ"
            ],
            [
                "company_id" => 225,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ImW1wdvDitLKFj",
                "old_stripe_id" => "cus_MzAlxfTi7JLH4z"
            ],
            [
                "company_id" => 226,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ImXD3qHcxhSSKU",
                "old_stripe_id" => "cus_MzAlakM6pw9ic4"
            ],
            [
                "company_id" => 227,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ImsKcWdSnJpHup",
                "old_stripe_id" => "cus_MzAmHidnqWveXB"
            ],
            [
                "company_id" => 228,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ImxWuS4FPVjM0Z",
                "old_stripe_id" => "cus_MzAmLUmZAMhQ3N"
            ],
            [
                "company_id" => 229,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_InOV0k0B88Nhmh",
                "old_stripe_id" => "cus_MzAmvzgRrHlx44"
            ],
            [
                "company_id" => 230,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_InicKPjwJ3jOUI",
                "old_stripe_id" => "cus_MzAmlWX9vy1cdC"
            ],
            [
                "company_id" => 231,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Inlh7VKWQ2dMio",
                "old_stripe_id" => "cus_MzAmUQUKdTaWnI"
            ],
            [
                "company_id" => 232,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_InyqTtFKp28sqa",
                "old_stripe_id" => "cus_MzAm2FZr6INSTO"
            ],
            [
                "company_id" => 233,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IoF7uTVgBFvwXe",
                "old_stripe_id" => "cus_MzAmNUyHk4Ueic"
            ],
            [
                "company_id" => 234,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IoU5xlDtZRvJaq",
                "old_stripe_id" => "cus_MzAmBqnaE9XN6P"
            ],
            [
                "company_id" => 235,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IokCMULvpa3VRg",
                "old_stripe_id" => "cus_MzAmkbm9PWTIkU"
            ],
            [
                "company_id" => 236,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IokZU8ZQvbDNDX",
                "old_stripe_id" => "cus_MzAm6G8kDe2Pa0"
            ],
            [
                "company_id" => 238,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Ip8NDeYzJxtTFl",
                "old_stripe_id" => "cus_MzAmD4AXOs323A"
            ],
            [
                "company_id" => 239,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Ip9aaNRFAs5TWx",
                "old_stripe_id" => "cus_MzAmjKx1GZj1p6"
            ],
            [
                "company_id" => 240,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IpFNuSgMc8jR8A",
                "old_stripe_id" => "cus_MzAmlJGebGv1BS"
            ],
            [
                "company_id" => 241,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IpGBGLjYOqh1YN",
                "old_stripe_id" => "cus_MzAnMM6sqH7ef4"
            ],
            [
                "company_id" => 245,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Ipv0mDfTRQRUr1",
                "old_stripe_id" => "cus_MzAn5e3hyAVXcB"
            ],
            [
                "company_id" => 247,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IqAx5ynKtd2uNB",
                "old_stripe_id" => "cus_MzAnAAadg2CTsF"
            ],
            [
                "company_id" => 248,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IqYvxxxT1DLKoG",
                "old_stripe_id" => "cus_MzAnIbRtgzIXHP"
            ],
            [
                "company_id" => 249,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IqhI5vn6iVD1Wl",
                "old_stripe_id" => "cus_MqDNCqk2yD1geJ"
            ],
            [
                "company_id" => 250,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IrJKKi0ElICPpI",
                "old_stripe_id" => "cus_MzAnIaKnO26iY4"
            ],
            [
                "company_id" => 251,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IrhdYzkNyXxONf",
                "old_stripe_id" => "cus_MzAnz3rn8NMV1H"
            ],
            [
                "company_id" => 252,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IrjTSXd34d5JYF",
                "old_stripe_id" => "cus_MzAnrdjJKqMxE7"
            ],
            [
                "company_id" => 253,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IrtXIAOozPJori",
                "old_stripe_id" => "cus_MzAnt6SzNVzRRH"
            ],
            [
                "company_id" => 254,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Is7HL9IBBEblX2",
                "old_stripe_id" => "cus_MzAnSD0NjN0DA2"
            ],
            [
                "company_id" => 255,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Is8mhBrrl9yxeB",
                "old_stripe_id" => "cus_MzAn6Dkxg2Cbtl"
            ],
            [
                "company_id" => 256,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IsBspSXvrUQfxM",
                "old_stripe_id" => "cus_MzAnczcqQmo0sW"
            ],
            [
                "company_id" => 257,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IsPLNT7wEEztCE",
                "old_stripe_id" => "cus_MzAnleuvEtAkRk"
            ],
            [
                "company_id" => 258,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IsTFEoc3zviSu3",
                "old_stripe_id" => "cus_MzAnVe96TLhB6O"
            ],
            [
                "company_id" => 259,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IsVYsuaQVfYTIA",
                "old_stripe_id" => "cus_MzAnMkrbMq7fTo"
            ],
            [
                "company_id" => 260,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IsbR2z5RVQ3N7v",
                "old_stripe_id" => "cus_MzAnLUfWZeKBX3"
            ],
            [
                "company_id" => 262,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IsqfsJzC6tMyeK",
                "old_stripe_id" => "cus_MzAoRkOvyOx95D"
            ],
            [
                "company_id" => 265,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ItLuKJJuAi6V78",
                "old_stripe_id" => "cus_MzAoWjV7f4cUFG"
            ],
            [
                "company_id" => 266,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ItfE28Y3xlYOGE",
                "old_stripe_id" => "cus_MzAobZukLtCr24"
            ],
            [
                "company_id" => 268,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_ItrbBqOf9F6QSH",
                "old_stripe_id" => "cus_MzAoOyJWLXhHdS"
            ],
            [
                "company_id" => 269,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Iu0T2EftfM6OIE",
                "old_stripe_id" => "cus_MzAozmulRYZI2I"
            ],
            [
                "company_id" => 270,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IuAwNqtJC8t86A",
                "old_stripe_id" => "cus_MzAohZnhfA9IkT"
            ],
            [
                "company_id" => 271,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IuS0T3u38P684B",
                "old_stripe_id" => "cus_MzAoCSw4QYzNy9"
            ],
            [
                "company_id" => 272,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IuhNuShPguOP5o",
                "old_stripe_id" => "cus_JfflLP43f777YA"
            ],
            [
                "company_id" => 273,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IuiYLZm7j0c1iX",
                "old_stripe_id" => "cus_MzAo9cdLbpyYuR"
            ],
            [
                "company_id" => 275,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Iv44NhaM0RnOgX",
                "old_stripe_id" => "cus_MzAohIyZegEhr6"
            ],
            [
                "company_id" => 277,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IvSRzcHMUyhOOJ",
                "old_stripe_id" => "cus_MzAodn1ToltOQV"
            ],
            [
                "company_id" => 280,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IvpWYVFpkNSoPO",
                "old_stripe_id" => "cus_MzApHjw69DpQ57"
            ],
            [
                "company_id" => 281,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IvtKBvaJE5Qwhm",
                "old_stripe_id" => "cus_MzApnaVevpSgvm"
            ],
            [
                "company_id" => 282,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IwAPV23gBpg6rQ",
                "old_stripe_id" => "cus_MzAph3TwP9AvOR"
            ],
            [
                "company_id" => 284,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IwW0qzed1v9vwD",
                "old_stripe_id" => "cus_MzApIfiSZ1FfRW"
            ],
            [
                "company_id" => 285,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Iwdk85rNegq3wQ",
                "old_stripe_id" => "cus_MzApHMbXOeJ3PU"
            ],
            [
                "company_id" => 286,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IweJd2U2vM4MAx",
                "old_stripe_id" => "cus_MzApNlpFwMXW6F"
            ],
            [
                "company_id" => 288,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Ix2HW0emcG0GB0",
                "old_stripe_id" => "cus_MzApEKBYH619sr"
            ],
            [
                "company_id" => 289,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Ix4vmGOkXR3s6t",
                "old_stripe_id" => "cus_MzApY890kb1X0h"
            ],
            [
                "company_id" => 292,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IxToCF99saVqvj",
                "old_stripe_id" => "cus_MzApZ4OQtuZW8M"
            ],
            [
                "company_id" => 293,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IxgrxJY9nrk1n8",
                "old_stripe_id" => "cus_MzApBNCPiCLQ5z"
            ],
            [
                "company_id" => 297,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IyBJa5wN7UGkwT",
                "old_stripe_id" => "cus_MzApGYPHwGtnNc"
            ],
            [
                "company_id" => 298,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IyezLboZcJbhmC",
                "old_stripe_id" => "cus_MzApbVUegAl13d"
            ],
            [
                "company_id" => 299,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_IzMbEgGo2FPv9C",
                "old_stripe_id" => "cus_MzAqzzpDjd3bRD"
            ],
            [
                "company_id" => 300,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J01QINwj7hMmQk",
                "old_stripe_id" => "cus_MzAq7hjTt4VXp6"
            ],
            [
                "company_id" => 303,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J19q3OkANt5Xb5",
                "old_stripe_id" => "cus_MzAqZdmQzmbSyI"
            ],
            [
                "company_id" => 304,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J2DR9v73VzKEBR",
                "old_stripe_id" => "cus_MzAqV5ZSDFR7xk"
            ],
            [
                "company_id" => 305,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J2GRTT4nSR4Chv",
                "old_stripe_id" => "cus_M65uYyPWjXnCQb"
            ],
            [
                "company_id" => 306,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J2LAAQV3Fb9jtY",
                "old_stripe_id" => "cus_MX8Tl70Z0EZXX2"
            ],
            [
                "company_id" => 307,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J2i84YWAaaHtDk",
                "old_stripe_id" => "cus_MzAqfNtl8Q4mjP"
            ],
            [
                "company_id" => 309,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J31JtpytpFRkxN",
                "old_stripe_id" => "cus_MzAqSt7cLY0tyG"
            ],
            [
                "company_id" => 311,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J40jJmjuXuJETI",
                "old_stripe_id" => "cus_MzAqQBRm9sJ1w5"
            ],
            [
                "company_id" => 315,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J599nhrGwYnLNE",
                "old_stripe_id" => "cus_MzAqfGqVRu9piY"
            ],
            [
                "company_id" => 317,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J5Ols9o8oQucJj",
                "old_stripe_id" => "cus_MzAqGMeS8lAwZB"
            ],
            [
                "company_id" => 318,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J67gEuNwjbYU9L",
                "old_stripe_id" => "cus_MzAqhishs0FZcA"
            ],
            [
                "company_id" => 320,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J6Ms28pwYnk5rr",
                "old_stripe_id" => "cus_MzAqegs6YBHYjK"
            ],
            [
                "company_id" => 321,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J6Q5nYzZNjURrR",
                "old_stripe_id" => "cus_MzAqEYaeW21Yxt"
            ],
            [
                "company_id" => 322,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J77LNqUkxvKE4Z",
                "old_stripe_id" => "cus_MzAqVuo0vLz7R3"
            ],
            [
                "company_id" => 323,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J7aYcCYpOqVThn",
                "old_stripe_id" => "cus_MzAr5gwaHlVRZu"
            ],
            [
                "company_id" => 325,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J7oP6lCtnwCckt",
                "old_stripe_id" => "cus_MzArQN05xmKQ2w"
            ],
            [
                "company_id" => 327,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J7rJX1ebhBukax",
                "old_stripe_id" => "cus_MzAr9tcTtMbta4"
            ],
            [
                "company_id" => 328,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J7uMTisKuJwKAz",
                "old_stripe_id" => "cus_MzArAo77TdgHTh"
            ],
            [
                "company_id" => 329,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J7yZ8BDomGa8t4",
                "old_stripe_id" => "cus_MzAr46Ytp9yqRb"
            ],
            [
                "company_id" => 330,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J8mDHX5OirXFkn",
                "old_stripe_id" => "cus_MzArwB12YNoVQ9"
            ],
            [
                "company_id" => 331,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J9HS8vLkiDHdoW",
                "old_stripe_id" => "cus_KFYX7pjjfY2Ti6"
            ],
            [
                "company_id" => 332,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_J9nbe19kz0kWGh",
                "old_stripe_id" => "cus_MzAr7lL7qS2gnT"
            ],
            [
                "company_id" => 333,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JA9T5XMM3OeR6q",
                "old_stripe_id" => "cus_MzArKHTo8gUWE8"
            ],
            [
                "company_id" => 334,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JATFNDxChrMoBw",
                "old_stripe_id" => "cus_MzAr0CUCNVAFfC"
            ],
            [
                "company_id" => 335,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JBXbMOQj1SaOqZ",
                "old_stripe_id" => "cus_MzArOZSjM8ZUxf"
            ],
            [
                "company_id" => 336,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JBb96OjaHACI0j",
                "old_stripe_id" => "cus_MzArVzpYksO4kI"
            ],
            [
                "company_id" => 337,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JCcEYLqsmYFlur",
                "old_stripe_id" => "cus_MzArEVnZUE7aLV"
            ],
            [
                "company_id" => 338,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JCfLrtzeawDblq",
                "old_stripe_id" => "cus_MzArCSs0DLPPE2"
            ],
            [
                "company_id" => 339,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JCov60y6HaWPex",
                "old_stripe_id" => "cus_MzAsS8MSCxPMKS"
            ],
            [
                "company_id" => 340,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JCuJPVzExwlDdg",
                "old_stripe_id" => "cus_MzAsnYxtmK5kx7"
            ],
            [
                "company_id" => 341,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JDpqhI09qWHC3e",
                "old_stripe_id" => "cus_MzAsomMAEkYBTO"
            ],
            [
                "company_id" => 343,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JEfd5OzSH9lkZ6",
                "old_stripe_id" => "cus_MzAsOYubilOgUM"
            ],
            [
                "company_id" => 344,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JElZowXwrvcZdR",
                "old_stripe_id" => "cus_MzAsAo8ua8ImQJ"
            ],
            [
                "company_id" => 345,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JEtUQsTRNbm3aT",
                "old_stripe_id" => "cus_MzAsq0KiH849z7"
            ],
            [
                "company_id" => 346,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JFHCeCyYY8pgbC",
                "old_stripe_id" => "cus_MzAsjqeEpnmWLs"
            ],
            [
                "company_id" => 347,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JFRfamdmMTytNI",
                "old_stripe_id" => "cus_MzAsu1J5RtG3Gt"
            ],
            [
                "company_id" => 348,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_JFjl59ZGzHJCr2"
            ],
            [
                "company_id" => 349,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JFqV51CLEEUXHJ",
                "old_stripe_id" => "cus_MzAsatyyutmZYF"
            ],
            [
                "company_id" => 351,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JGOTb7kQxJvPS6",
                "old_stripe_id" => "cus_MzAswPQ8zMo8ph"
            ],
            [
                "company_id" => 352,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JGV6RO63pTC9Ju",
                "old_stripe_id" => "cus_MzAsk2k6gOdI9K"
            ],
            [
                "company_id" => 353,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JGXa9K2phKJXgt",
                "old_stripe_id" => "cus_MzAsNGvaWYZbjL"
            ],
            [
                "company_id" => 354,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JHExLCcawQPA86",
                "old_stripe_id" => "cus_MzAsW7KjrE7V3E"
            ],
            [
                "company_id" => 355,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JHIULiZaVMpHyU",
                "old_stripe_id" => "cus_MzAsE1ZRVZjoQd"
            ],
            [
                "company_id" => 356,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JHX0Uuua6aVYeI",
                "old_stripe_id" => "cus_MzAsNWGEcvd4z5"
            ],
            [
                "company_id" => 357,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JHXQlohN7d9ae7",
                "old_stripe_id" => "cus_MzAtAHivoKY9Yo"
            ],
            [
                "company_id" => 358,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JHbAQjJtOMzBY8",
                "old_stripe_id" => "cus_MzAtklbz1dpOXC"
            ],
            [
                "company_id" => 359,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JHfkLFq3SAAg7L",
                "old_stripe_id" => "cus_MzAtDiLSYr0D2T"
            ],
            [
                "company_id" => 360,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JHjlloUOaHdTnr",
                "old_stripe_id" => "cus_MzAt4yNtiz2o99"
            ],
            [
                "company_id" => 361,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JHudCv0JEExfWO",
                "old_stripe_id" => "cus_MzAtTK9OwDGx0H"
            ],
            [
                "company_id" => 362,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JHxljps0v96agp",
                "old_stripe_id" => "cus_MzAt2H53yQCLIi"
            ],
            [
                "company_id" => 364,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JIhW9WypwNvaCV",
                "old_stripe_id" => "cus_MzAtCEJkb2g6ro"
            ],
            [
                "company_id" => 365,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JJCJHLC9FyXhPS",
                "old_stripe_id" => "cus_MzAtETQlGJaqaQ"
            ],
            [
                "company_id" => 366,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JJin613VD0DwCt",
                "old_stripe_id" => "cus_MzAt0cW4FRrYyC"
            ],
            [
                "company_id" => 367,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JK38ZUxGDAwS2B",
                "old_stripe_id" => "cus_MzAtDwelLEzNJB"
            ],
            [
                "company_id" => 368,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JKcDeQnsETMQYP",
                "old_stripe_id" => "cus_MzAtbkScNNQBBC"
            ],
            [
                "company_id" => 369,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JKcyC9gsZT2eN7",
                "old_stripe_id" => "cus_MzAttaY94tWtdl"
            ],
            [
                "company_id" => 371,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JLSOoRiBj8HQPj",
                "old_stripe_id" => "cus_LeW0FY6mrfNzD8"
            ],
            [
                "company_id" => 372,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JLWKFHKdr3Sja5",
                "old_stripe_id" => "cus_MzAuZdk9NDyatW"
            ],
            [
                "company_id" => 373,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JLhPVljlfu0p5m",
                "old_stripe_id" => "cus_MzAuNjwNKCXtYr"
            ],
            [
                "company_id" => 374,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JLsrPrOT7MY2BY",
                "old_stripe_id" => "cus_MzAucF8p47R5bR"
            ],
            [
                "company_id" => 375,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JMFRjpPifFrka9",
                "old_stripe_id" => "cus_MzAujeDipAb8xq"
            ],
            [
                "company_id" => 376,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JMSsDGowTITS9E",
                "old_stripe_id" => "cus_MzAuOc24U02nrg"
            ],
            [
                "company_id" => 377,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JMXI0hLytNRwI2",
                "old_stripe_id" => "cus_MzAutSHL6jnL0E"
            ],
            [
                "company_id" => 378,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JMnu2zAGiTYhNk",
                "old_stripe_id" => "cus_MzAuRVS2mKe1No"
            ],
            [
                "company_id" => 379,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JMoeF36Eb9zjhq",
                "old_stripe_id" => "cus_MzAubq5V2BYPbh"
            ],
            [
                "company_id" => 380,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_JN9cjpQOdoafdm"
            ],
            [
                "company_id" => 382,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JNUvtaTPrPlayZ",
                "old_stripe_id" => "cus_MzAugus0rfzIAL"
            ],
            [
                "company_id" => 383,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JNWTSzIChTJuZE",
                "old_stripe_id" => "cus_MzAuBKbBryKHFB"
            ],
            [
                "company_id" => 386,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JOQ9dOulSISW3U",
                "old_stripe_id" => "cus_MzAu1wrpkAQc9I"
            ],
            [
                "company_id" => 387,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JOVdC5WQWAsMPJ",
                "old_stripe_id" => "cus_MzAur9XodL6NAZ"
            ],
            [
                "company_id" => 388,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JOcI0xXu2BUiq0",
                "old_stripe_id" => "cus_MzAumAH6H4MnuV"
            ],
            [
                "company_id" => 389,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JP2mXAFaOvxOHd",
                "old_stripe_id" => "cus_MzAu14SbLknWzP"
            ],
            [
                "company_id" => 390,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JPdEgGFEcg4FtX",
                "old_stripe_id" => "cus_MzAunsOVqagoFF"
            ],
            [
                "company_id" => 391,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JPqx4XWFT38oa8",
                "old_stripe_id" => "cus_MzAvzw0qL29IVq"
            ],
            [
                "company_id" => 393,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JQKcGwh7JhcYTl",
                "old_stripe_id" => "cus_MzAvEnnUII3RSd"
            ],
            [
                "company_id" => 394,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JQLX7mDQeKhS66",
                "old_stripe_id" => "cus_MzAviMII0Nf6MU"
            ],
            [
                "company_id" => 396,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JQtCKMvu1h6x7n",
                "old_stripe_id" => "cus_MzAvn1Tru3XP77"
            ],
            [
                "company_id" => 397,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JR1wkKfSH1Fxyw",
                "old_stripe_id" => "cus_MzAvKL3UKTdbJz"
            ],
            [
                "company_id" => 399,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JR7z084sEzn2nV",
                "old_stripe_id" => "cus_MzAvWu8ZfiXwNC"
            ],
            [
                "company_id" => 400,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JRnDEKT7W3D7h3",
                "old_stripe_id" => "cus_MzAvjoMm0QJOof"
            ],
            [
                "company_id" => 401,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JRqPVIzHVrZ0FD",
                "old_stripe_id" => "cus_MzAv695HrNuUD0"
            ],
            [
                "company_id" => 404,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JSkjh5QEgaSY9F",
                "old_stripe_id" => "cus_MzAv37PU4qXq6s"
            ],
            [
                "company_id" => 406,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JTYIYTbCfY2kdR",
                "old_stripe_id" => "cus_MzAwZIp3jp78aB"
            ],
            [
                "company_id" => 407,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JTa0Pi1aiv6jEk",
                "old_stripe_id" => "cus_MzAwNc6omamavy"
            ],
            [
                "company_id" => 408,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JThx0IKMIKVmgZ",
                "old_stripe_id" => "cus_MzAwXph5gXAgKO"
            ],
            [
                "company_id" => 412,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JV3tEApjSGagR7",
                "old_stripe_id" => "cus_MzAwpsgMaJ7WXn"
            ],
            [
                "company_id" => 413,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JV5dmB5eKhqoqn",
                "old_stripe_id" => "cus_MzAwJTXn5NJkYl"
            ],
            [
                "company_id" => 414,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JVWHL0EOoXcjAP",
                "old_stripe_id" => "cus_MzAw3jJSMeG2Iy"
            ],
            [
                "company_id" => 415,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JVsmArdVlV0G1q",
                "old_stripe_id" => "cus_MzAwKEI0crCK5a"
            ],
            [
                "company_id" => 416,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JVtptcuOQ76w7T",
                "old_stripe_id" => "cus_MzAwx3AWmBUQKO"
            ],
            [
                "company_id" => 419,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_L9cwYfnQMfGWU6",
                "old_stripe_id" => "cus_MzAwdpTl0MAwOg"
            ],
            [
                "company_id" => 420,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JWaoATmqtsAkhr",
                "old_stripe_id" => "cus_MzAwJHDsB6BovY"
            ],
            [
                "company_id" => 421,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JWfsw5YxyjXDSw",
                "old_stripe_id" => "cus_MzAw0GP49bazeR"
            ],
            [
                "company_id" => 423,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JWwZehrr2Q1Dtz",
                "old_stripe_id" => "cus_MzAx3eXe0XZ6ys"
            ],
            [
                "company_id" => 424,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JWzloXPvGFARYB",
                "old_stripe_id" => "cus_MzAxuo3FUrViK8"
            ],
            [
                "company_id" => 426,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JX5tEubihS3aXO",
                "old_stripe_id" => "cus_MzAx0fhwlnuLUA"
            ],
            [
                "company_id" => 428,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JXaZOdvuSEdESh",
                "old_stripe_id" => "cus_MzAxSGX0rn9FJw"
            ],
            [
                "company_id" => 430,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JYP9CMXZpDX3VK",
                "old_stripe_id" => "cus_MzAxIikeChHR2K"
            ],
            [
                "company_id" => 433,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JZ6o7FuFauEv7d",
                "old_stripe_id" => "cus_MzAxsiF2h5exmb"
            ],
            [
                "company_id" => 434,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JZ7vxSgsGawVq9",
                "old_stripe_id" => "cus_MzAxsZY4eD0dxK"
            ],
            [
                "company_id" => 435,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JZab24HH4vCF3I",
                "old_stripe_id" => "cus_MzAxn1XWL1YZYg"
            ],
            [
                "company_id" => 436,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JZviLFGcpzYDVH",
                "old_stripe_id" => "cus_MzAxCDGqkv3oyJ"
            ],
            [
                "company_id" => 437,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Ja1qrN5OxoFPSv",
                "old_stripe_id" => "cus_MzAx0okqJav4B1"
            ],
            [
                "company_id" => 440,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JaSi9bMcZQ8wuo",
                "old_stripe_id" => "cus_MzAxThOAaBc46T"
            ],
            [
                "company_id" => 441,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Jal45eJmNVZRye",
                "old_stripe_id" => "cus_MzAyZTSuIk40tD"
            ],
            [
                "company_id" => 442,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Jb4wfyaV3QB3oe",
                "old_stripe_id" => "cus_MzAyN2fpcmmXdu"
            ],
            [
                "company_id" => 443,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_Jb6epK7nGnfhnz",
                "old_stripe_id" => "cus_MzAyIa48RKQw4l"
            ],
            [
                "company_id" => 444,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_JbDRZAXQoVDPQ5",
                "old_stripe_id" => "cus_MzAyleQOAAKqqT"
            ],
            [
                "company_id" => 517,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_JsLjtuWXt56u29"
            ],
            [
                "company_id" => 525,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_JtQwKfgPL6Ud8Y"
            ],
            [
                "company_id" => 549,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_JxN6JQlmG4DTWV"
            ],
            [
                "company_id" => 586,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_K2lwV8rhWhYvxI"
            ],
            [
                "company_id" => 609,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_K57c7GkvigcCE8"
            ],
            [
                "company_id" => 652,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_KBTPXLaW3kTr0Y"
            ],
            [
                "company_id" => 670,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_KEOUH5qVPn2X1x"
            ],
            [
                "company_id" => 816,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_KZ4wp41VB71ZQL"
            ],
            [
                "company_id" => 969,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => null,
                "old_stripe_id" => "cus_KvwZRkraNnpnaI"
            ],
            [
                "company_id" => 1359,
                "company_email" => "<EMAIL>",
                "new_stripe_id" => "cus_LshCECfjvpjS2o",
                "old_stripe_id" => "cus_Lq0nNC5ynUeK8S"
            ]

        ];

        $companies_to_change = collect($companies_to_change);
        if ($request->has('start_id') && $request->has('end_id')) {
            $companies_to_change = $companies_to_change->where('company_id', '>=', $request->start_id)->where('company_id', '<=', $request->end_id);
        }
        foreach ($companies_to_change as $company_to_change) {
            $company = Company::where('id', $company_to_change['company_id'])->first();
            if (!$company) {
                continue;
            }
            if ($company->stripe_id != $company_to_change['new_stripe_id']) {
                $company->stripe_id = $company_to_change['new_stripe_id'];
                $company->save();
            }
        }
        return "done";

        // $plan_map = [];


        // $plan_map = [
        //     //FREE PLAN
        //     [
        //         "old_plan_id" => "price_1It9oDCBmBWVCuBdxioStgMG", //GBP
        //         "old_plan_value" => "0.00",
        //         "new_plan_id" => "price_1MErFlCBmBWVCuBdAxbSqw6r", //GBP
        //         "new_plan_value" => "0.00",
        //     ],
        //     [
        //         "old_plan_id" => "price_1It9nLCBmBWVCuBdx3tuZ4ka", //USD
        //         "old_plan_value" => "0.00",
        //         "new_plan_id" => "price_1MErGDCBmBWVCuBdH7JFHdmm", //USD
        //         "new_plan_value" => "0.00",
        //     ],
        //     [
        //         "old_plan_id" => "price_1It9msCBmBWVCuBdlfNmXtgF", //SEK
        //         "old_plan_value" => "0.00",
        //         "new_plan_id" => "price_1MErEzCBmBWVCuBdbSu1griI", //SEK
        //         "new_plan_value" => "0.00",
        //     ],
        //     [
        //         "old_plan_id" => "plan_HInwLF7p1lgjk2", //EUR
        //         "old_plan_value" => "0.00",
        //         "new_plan_id" => "price_1MErGpCBmBWVCuBd8eSwFFV1", //EUR
        //         "new_plan_value" => "0.00",
        //     ],

        //     //PAID PLAN
        //     [
        //         "old_plan_id" => "price_1It8ZfCBmBWVCuBdJq1WThzh", //GBP
        //         "old_plan_value" => "12.00",
        //         "new_plan_id" => "price_1MErHxCBmBWVCuBdtZyTFH6D", //GBP
        //         "new_plan_value" => "22.00",
        //     ],
        //     [
        //         "old_plan_id" => "price_1It8Z0CBmBWVCuBdRPatw13P", //USD
        //         "old_plan_value" => "17.00",
        //         "new_plan_id" => "price_1MErIdCBmBWVCuBdfoub9Irj", //USD
        //         "new_plan_value" => "26.00",
        //     ],
        //     [
        //         "old_plan_id" => "price_1It8aFCBmBWVCuBdpS0WqGFe", //SEK
        //         "old_plan_value" => "140.00",
        //         "new_plan_id" => "price_1MErHOCBmBWVCuBduSGHskBf", //SEK
        //         "new_plan_value" => "249.00",
        //     ],
        //     [
        //         "old_plan_id" => "price_1IlYRUCBmBWVCuBdBsmscuFH", //EUR
        //         "old_plan_value" => "14.00",
        //         "new_plan_id" => "price_1MErJACBmBWVCuBd193MrjzP", //EUR
        //         "new_plan_value" => "24.00",
        //     ],
        // ];


        // foreach ($plan_map as $plan) {
        //     $old_plan = Plan::orderBy('currency', 'asc')->get()->where('plan_id', $plan['old_plan_id'])->first();
        //     if ($old_plan) {
        //         $old_plan = Plan::findOrFail($old_plan->id);
        //         Plan::create([
        //             'name' => $old_plan->name,
        //             'subscription_id' => $old_plan->subscription_id,
        //             'plan_id' => $plan['new_plan_id'],
        //             'cost' => $old_plan->cost,
        //             'description' => $old_plan->description,
        //             'users' => $old_plan->users,
        //             'client' => $old_plan->client,
        //             'storage' => $old_plan->storage,
        //             'cost_value' => $plan['new_plan_value'],
        //             'is_free' => $old_plan->is_free,
        //             'currency' => $old_plan->currency,
        //             'is_2022' => 1,
        //         ]);
        //     }
        // }


        // return  Plan::orderBy('currency', 'asc')->get();
    }


    public function pullTest(Request $request)
    {
        return 'this is pull test test';
    }

    public function checkForPlans(Request $request)
    {
        $companies = Company::query();
        $companies_to_check = [];

        if ($request->has('start_company_id')) {
            $companies = $companies->where('id', '>=', $request->start_company_id);
        }
        if ($request->has('end_company_id')) {
            $companies = $companies->where('id', '<=', $request->end_company_id);
        }
        foreach ($companies->cursor() as $company) {
            try {
                PlanManager::checkForPlan($company);
            } catch (\Throwable $th) {
                array_push($companies_to_check, [
                    'id' => $company->id,
                    'company_email' => $company->email,
                    'problem' => $th->getMessage()
                ]);
            }
        }

        return $companies_to_check;
    }

    public function takePull(Request $request)
    {
        $torun = "cd /var/www/html/meridiq-api/ ; sudo git pull origin production";
        //echo $torun;
        try {
            exec($torun);
        } catch (\Throwable $th) {
            return $th->getMessage();
        }
    }

    public function updateCompanyStripeId(Request $request)
    {
        $company = Company::findOrFail($request->company_id);

        if ($request->has('stripe_id')) {
            $company->stripe_id = $request->stripe_id;
            $company->save();
        }


        return response()->json([
            'message' => 'updated',
            'status' => '1',
        ]);
    }

    public function getCompaniesWithoutCard(Request $request)
    {
        $companies = Company::cursor();

        if ($request->has('start_id')) {
            $companies = $companies->where('id', '>=', $request->start_id);
        }
        if ($request->has('end_id')) {
            $companies = $companies->where('id', '<=', $request->end_id);
        }

        $companies_without_cards = [];
        foreach ($companies as $company) {
            $company->createOrGetStripeCustomer();
            $payment_method = $company->defaultPaymentMethod();
            $data = [];
            if ($payment_method && $payment_method->card) {
                $data['card'] = [
                    'brand' => $payment_method->card->brand,
                    'exp_month' => $payment_method->card->exp_month,
                    'exp_year' => $payment_method->card->exp_year,
                    'last4' => $payment_method->card->last4,
                ];
            } elseif ($payment_method && $payment_method->brand && $payment_method->exp_month && $payment_method->exp_year && $payment_method->last4) {
                $data['card'] = [
                    'brand' => $payment_method->brand,
                    'exp_month' => $payment_method->exp_month,
                    'exp_year' => $payment_method->exp_year,
                    'last4' => $payment_method->last4,
                ];
            }
            if (!$data) {

                $is_free = false;
                $is_cancelled = false;
                $subscription_name = 'Subscription';
                if ($company->activeSubscription()) {
                    $subscription_name = $company->activeSubscription()->name;
                }
                $is_subscribed =  $company->subscribed($subscription_name);
                try {
                    $is_cancelled  = $company->subscription($subscription_name)->cancelled() || $company->subscription($subscription_name)->pastDue();
                } catch (\Throwable $th) {
                    $is_cancelled = false;
                }

                if ($company->activeSubscription()) {
                    if ($company->activeSubscription()->plan->is_free) {
                        $is_free = true;
                    }
                }


                array_push($companies_without_cards, [
                    'id' => $company->id,
                    'email' => $company->email,
                    'stripe_id' => $company->stripe_id,
                    'is_free' => $is_free,
                    'is_cancelled' => $is_cancelled,
                    'is_subscribed' => $is_subscribed,
                ]);
            }
        }

        return $companies_without_cards;
    }
    public function test33(Request $request)
    {
        $free_plans = Plan::where('is_2022', 1)->get()->where('is_free', true);
        foreach ($free_plans as $free_plan) {
            $plan_to_update = Plan::findOrFail($free_plan->id);
            $plan_to_update->client = '21';
            $plan_to_update->save();
        }
        return "dome update";
        // $company = Company::findOrFail(437);
        // return $company->profile_photo;
        // return Carbon::now()->format('l, F d Y g:i A');
        // $user = Auth::user();
        // $company = $user->company;
        // $subscription_name = 'Subscription';
        // if ($company->activeSubscription()) {
        //     $subscription_name = $company->activeSubscription()->name;
        // }
        // $company->subscription($subscription_name)->cancel();
        // $company->createOrGetStripeCustomer();
        // try {

        //     $company
        //         ->newSubscription('Subscription', $request->plan)
        //         ->quantity($plan->isFree() ? 1 : $request->quantity)
        //         ->create($request->has('payment_method.paymentMethod') ? $request->payment_method['paymentMethod'] : null, $billing_details, [
        //             'metadata' => $request->has('payment_method.metadata') ? $request->payment_method['metadata'] : '',
        //             'default_tax_rates' => $company->subscriptionTaxRates(),
        //         ]);
        //     // $company->subscription('Subscription')->swap([
        //     //     'price_1MABKoCBmBWVCuBdETKp49rW' => [
        //     //         'quantity' => 1,
        //     //     ]
        //     // ]);
        // } catch (IncompletePayment $th) {
        //     $invoice = $company->findInvoice($th->payment->invoice);
        //     if ($invoice) {
        //         $invoice->void();
        //     }
        //     Log::error($th);
        //     throw $th;
        // }
        // return $company->subscriptions;
    }
}
