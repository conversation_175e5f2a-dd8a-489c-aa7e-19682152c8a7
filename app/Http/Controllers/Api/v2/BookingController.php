<?php

namespace App\Http\Controllers\Api\v2;

use App\AestheticInterest;
use App\Client;
use App\ClientLetterOfConsent;
use App\Company;
use App\CompanyBooking;
use App\CompanyBookingClient;
use App\CompanyBookingLetterOfConsent;
use App\CompanyBookingQuestionary;
use App\CompanyService;
use App\Contracts\Services\PDF\PDFServiceInterface;
use App\Contracts\Services\POS\ReceiptServiceInterface;
use App\Covid19;
use App\Events\BookingCreatedEvent;
use App\Exceptions\PractitionerNotAvailableException;
use App\HealthQuestionary;
use App\Http\Controllers\Api\v1\ClientConsentController;
use App\Http\Controllers\Controller;
use App\Http\Requests\v2\AddExtraStepRequest;
use App\Http\Requests\v2\CreateBookingRequest;
use App\Http\Requests\v2\CreateOutsideBookingRequest;
use App\Http\Requests\v2\EditBookingRequest;
use App\Http\Requests\v2\GetBookingListRequest;
use App\Http\Requests\v2\ResendOtpRequest;
use App\Http\Requests\v2\VerifyBookingRequest;
use App\Jobs\NewClientCreated;
use App\LetterOfConsent;
use App\QuestionaryData;
use App\QuestionaryQuestion;
use App\Setting;
use App\SMSTemplate;
use App\Traits\ApiResponser;
use App\Traits\Booking\BookingGoogleCalendarManager;
use App\Traits\BookingManager;
use App\Traits\SaveFile;
use App\Traits\Sinch;
use App\Traits\SMS;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Throwable;
use App\Contracts\Services\ZIP\ZipServiceInterface;
use App\Http\Requests\v2\AddNoShowCommentRequest;

class BookingController extends Controller
{
    use ApiResponser;
    use BookingManager;
    use SaveFile;
    use SMS;
    use Sinch;
    public function index(GetBookingListRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;
        $client_ids = [];

        $bookings = $company->bookings()->with([
            'service.category',
            'client' => function ($query) {
                $query->setEagerLoads([]);
            },
            'user' => function ($query) {
                $query->without('company')->select('id', 'first_name', 'last_name');
            }
        ]);

        if ($request->boolean('show_note')) {
            $bookings = $bookings->with(['latest_note']);
        }

        $bookings = $bookings->where('is_verified', 1)->with(['clients']);

        if ($request->has('user_id')) {
            $bookings = $bookings->where('user_id', $request->user_id);
        }
        if ($request->has('client_id')) {
            $client_ids[] = $request->client_id;
        }
        if ($request->has('service_id')) {
            $bookings = $bookings->where('service_id', $request->service_id);
        }
        if ($request->has('user_ids')) {
            $bookings = $bookings->whereIn('user_id', $request->user_ids);
        }
        if ($request->has('client_ids')) {
            $client_ids = array_merge($client_ids, $request->client_ids ?? []);
        }

        if ($request->has('service_ids')) {
            $bookings = $bookings->whereIn('service_id', $request->service_ids);
        }
        if ($request->has('start_date')) {
            $bookings = $bookings->whereDate('start_at', Carbon::parse($request->start_date)->format('Y-m-d'));
        }
        if ($request->has('start_at')) {
            $bookings = $bookings->whereDate('start_at', '>=', Carbon::parse($request->start_at)->format('Y-m-d H:i:s'));
        }
        if ($request->has('end_at')) {
            $bookings = $bookings->whereDate('start_at', '<=', Carbon::parse($request->end_at)->format('Y-m-d H:i:s'));
        }
        if ($request->has('is_shown')) {
            $bookings = $bookings->where('is_shown', $request->is_shown);
        }
        if ($request->missing('client_ids') && $request->missing('client_id')) {
            $bookings = $bookings->where('is_cancelled', 0);
        }

        if ($user->user_role == User::USER) {
            $bookings = $bookings->where('user_id', $user->id);
        }

        if ($request->has('receipt')) {
            $bookings = $bookings->with([
                'receipt',
            ]);
        }

        if ($request->has('client_receipt')) {
            $bookings = $bookings->with([
                'clients.pivot.receipt',
            ]);
        }

        if (count($client_ids)) {
            $bookings = $bookings
                ->whereIn('client_id', $client_ids)
                ->orWhereHas('clients', function ($query) use ($request, $client_ids) {
                    $query = $query->whereIn('clients.id', $client_ids);
                })
                ->with(['clients' => function ($query) use ($request, $client_ids) {
                    $query = $query->whereIn('clients.id', $client_ids);
                }]);
        }

        $isPaginated = false;
        if ($request->has('search') && $request->search != '') {
            if (!$isPaginated) {
                $bookings = $bookings->get();
                $isPaginated = true;
            }
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);

            $bookings = $bookings->filter(function ($value) use ($search) {
                return Str::contains(Str::lower($value->full_name), $search)
                    ||
                    Str::contains(Str::lower($value->service->name), $search)
                    ||
                    Str::contains(Str::lower($value->client?->first_name), $search);
            });
        }

        if ($request->has('show_count') && $request->show_count) {
            return response()->json([
                'data' => $bookings->count(),
                'message' => __('strings.user_booking_returned'),
                'status' => '1',
            ]);
        }
        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');

            if (!$isPaginated) {
                $bookings = $bookings->get();
                $isPaginated = true;
            }

            $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
            $sortExtra = (SORT_NATURAL | SORT_FLAG_CASE);
            if ($orderBy == 'full_name') {
                $bookings = $bookings->sortBy($orderBy, $sortExtra, $isDescOrder);
            }
            if ($orderBy == 'first_name') {
                $bookings = $bookings->sortBy('client.first_name', $sortExtra, $isDescOrder);
            }
            if ($orderBy == 'service_name') {
                $bookings = $bookings->sortBy('service.name', $sortExtra, $isDescOrder);
            }
            if ($orderBy == 'booked_time') {
                $bookings = $bookings->sortBy('start_at', $sortExtra, $isDescOrder);
            }
            if ($orderBy == 'practitioner_name') {
                $bookings = $bookings->sortBy('user.first_name', $sortExtra, $isDescOrder);
            }
        }
        if (($request->missing('orderBy') || $request->orderBy == 'booked_time') && $request->has('show_upcoming_first') && $request->show_upcoming_first) {
            if (!$isPaginated) {
                $bookings = $bookings->get();
                $isPaginated = true;
            }

            $old_bookings = $bookings->where('start_at', '<', Carbon::now()->format('Y-m-d'))->sortByDesc('start_at')->pluck('id')->toArray();
            $upcoming_bookings = $bookings->where('start_at', '>=', Carbon::now()->format('Y-m-d'))->sortBy('start_at')->pluck('id')->toArray();

            $total_array = array_merge($upcoming_bookings, $old_bookings);
            if (count($total_array) > 0) {
                $ids_ordered = implode(',', $total_array);
                $bookings = CompanyBooking::query()
                    ->whereIn('id', $total_array)
                    ->with([
                        'service.category',
                        'client' => function ($query) {
                            $query->setEagerLoads([]);
                        },
                        'user' => function ($query) {
                            $query->without('company')->select('id', 'first_name', 'last_name');
                        }
                    ])
                    ->orderByRaw("FIELD(id, $ids_ordered)")
                    ->get();
            }
        }
        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => __('strings.user_booking_returned'),
                    'status' => '1',
                    'count' => $bookings->count(),
                ])->merge($isPaginated ? $this->paginate($bookings) : $bookings->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'count' => $bookings->count(),
                'data' => $isPaginated ? $bookings : $bookings->get(),
                'message' => __('strings.user_booking_returned'),
                'status' => '1',
            ]);
        }
    }

    public function detail(CompanyBooking $booking)
    {
        return response()->json([
            'data' => $booking->refresh()->loadMissing([
                'company',
                'user',
                'client',
                'receipt',
                'service.category',
                'client_letter_of_consents',
                'questionaries',
                'video_call.members',
                'clients' => function ($query) {
                    $query = $query->where('company_booking_clients.is_cancelled', '0')
                        ->where('company_booking_clients.is_verified', '1');
                },
                'clients.pivot.receipt',
            ]),
            'message' => __('strings.booking_detail'),
            'status' => '1',
        ]);
    }

    public function cancel(CompanyBooking $booking, Request $request)
    {
        try {
            if ($request->has('client_ids')) {
                $company_booking_clients =  CompanyBookingClient::where('booking_id', $booking->id)
                    ->whereIn('client_id', $request->client_ids)
                    ->cursor();
                foreach ($company_booking_clients as $company_booking_client) {
                    CompanyBooking::cancelBooking($booking, false, $company_booking_client);
                    CompanyBooking::cancelBookingSMS($booking, $company_booking_client);
                }
            } else {
                CompanyBooking::cancelBooking($booking);
                CompanyBooking::cancelBookingSMS($booking);
            }
        } catch (Throwable $th) {
            report($th);
            return response()->json([
                'message' => $th->getMessage(),
                'status' => '0',
            ]);
        }

        return response()->json([
            'data' => $booking->refresh()->loadMissing(['company', 'user', 'client', 'service']),
            'message' => __('strings.booking_cancelled'),
            'status' => '1',
        ]);
    }

    public function sendBooking(CompanyBooking $booking, Request $request)
    {
        try {
            if ($request->has('client_ids')) {
                $company_booking_clients =  CompanyBookingClient::where('booking_id', $booking->id)
                    ->whereIn('client_id', $request->client_ids)
                    ->cursor();
                foreach ($company_booking_clients as $company_booking_client) {
                    CompanyBooking::sendMail($booking, true, true, $company_booking_client);
                }
            } else {
                CompanyBooking::sendMail($booking, true, true);
            }
        } catch (Throwable $th) {
            return response()->json([
                'message' => $th->getMessage(),
                'status' => '0',
            ]);
        }

        return response()->json([
            'message' => __('strings.mail_send_to_client'),
            'status' => '1',
        ]);
    }

    public function editOnExisting(CompanyBooking $booking, EditBookingRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;

        if ($request->has('special_request')) {
            $booking->special_request = $request->special_request;
        }
        if ($request->has('special_request')) {
            $booking->special_request = $request->special_request;
        }
        if ($request->has('is_shown')) {
            $booking->is_shown = $request->is_shown;
        }
        if ($request->has('show_comment')) {
            $booking->show_comment = $request->show_comment;
        }

        $is_same_practitioner = false;
        if ($request->has('user_id')) {
            if ($booking->user_id == $request->user_id) {
                $is_same_practitioner = true;
            } else {
                $booking->user_id = $request->user_id;
            }
        }

        $service = CompanyService::findOrFail($booking->service_id);
        $is_same_time = false;
        $should_make_slot_unavailable = $booking->should_make_slot_unavailable;
        if ($request->has('start_at') && $request->has('end_at')) {
            $start_at = Carbon::parse($request->start_at);
            $end_at = $request->has('end_at') ? Carbon::parse($request->end_at) : Carbon::parse($request->start_at)->addMinutes($service->duration ?? 0)->addMinutes($service->time_margin ?? 0);

            if ($booking->start_at == $request->start_at && $booking->end_at == $request->end_at) {
                $is_same_time = true;
                // return response()->json([
                //     'message' => __('strings.can_not_reschedule_to_same_date'),
                //     'status' => '0',
                // ]);
            } else {
                if ($booking->should_make_slot_unavailable) {
                    CompanyBooking::revertAvailableSlotForThisBooking($booking);
                }
                $booking->start_at = $start_at;
                $booking->end_at = $end_at;
            }
        }

        $isDirty = $booking->isDirty();
        $booking->should_make_slot_unavailable = $should_make_slot_unavailable;
        $booking->save();
        if ($request->has('user_id') && !$is_same_practitioner) {
            CompanyBooking::practitionerChanged($booking);
        }
        if (!$is_same_time) {
            CompanyBooking::rescheduleBooking($booking);
        } elseif ($isDirty) {
            if ($request->missing('is_shown')) {
                CompanyBooking::updatedBooking($booking);
            }
        }

        return response()->json([
            'data' => $booking->refresh()->loadMissing(['company', 'user', 'client', 'service']),
            'message' => __('strings.booking_updated'),
            'status' => '1',
        ]);
    }
    public function edit(CompanyBooking $booking, EditBookingRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;

        return  DB::transaction(function () use ($booking, $request, $company) {
            $is_group_booking = 0;
            if (
                $booking->service
                &&
                $booking->service->category
                &&
                $booking->service->category->group_booking
            ) {
                $is_group_booking = 1;
            }

            if (!$is_group_booking) {
                if ($request->has('service_id')) {
                    if ($request->service_id != $booking->service_id) {
                        $service = CompanyService::findOrFail($request->service_id);
                        if ($service && $service?->category?->group_booking) {
                            return response()->json([
                                'message' => __('strings.can_not_change_service_to_group_booking'),
                                'status' => '0',
                            ]);
                        }
                        if ($service->users()->where('users.id', $request->user_id)->doesntExist()) {
                            return response()->json([
                                'message' => __('strings.practitioner_does_not_provide_the_requested_service'),
                                'status' => '0',
                            ]);
                        }
                        $booking->service_id = $request->service_id;
                        $booking->save();
                    }
                }
                if ($request->has('special_request')) {
                    $booking->special_request = $request->special_request;
                }
                if ($request->has('is_shown')) {
                    $booking->is_shown = $request->is_shown;
                }
                if ($request->has('show_comment')) {
                    $booking->show_comment = $request->show_comment;
                }
                if ($request->has('client_id')) {
                    if ($booking->client_id != $request->client_id) {
                        $booking->client_id = $request->client_id;
                        $client = Client::findOrFail($request->client_id);

                        if ($client) {
                            $booking->full_name = $client->full_name;
                            $booking->first_name = $client->first_name;
                            $booking->last_name = $client->last_name;
                            $booking->save();
                        }
                    }
                }
            } else {
                if ($request->has('client_ids')) {
                    foreach ($request->client_ids as $client_id) {
                        $booking_client =  CompanyBookingClient::where('booking_id', $booking->id)
                            ->where('client_id', $client_id)
                            ->where('is_cancelled', 0)
                            ->where('is_verified', 1)
                            ->first();

                        if ($booking_client) {
                            if ($request->has('special_request')) {
                                $booking_client->special_request = $request->special_request;
                            }
                            if ($request->has('is_shown')) {
                                $booking_client->is_shown = $request->is_shown;
                            }
                            if ($request->has('show_comment')) {
                                $booking_client->show_comment = $request->show_comment;
                            }
                            $booking_client->save();
                        } else {
                            if ($booking->active_clients()->count() >= $booking->service->group_quantity) {
                                return response()->json([
                                    'message' => __('strings.max_client_limit_reached_for_this_booking'),
                                    'status' => '0',
                                ]);
                            }
                            $booking_client = CompanyBookingClient::create([
                                'booking_id' => $booking->id,
                                'client_id' => $client_id,
                                'is_cancelled' => 0,
                                'is_verified' => 1,
                                'special_request' => $request->special_request,
                                'show_comment' => $request->show_comment,
                            ]);
                            if ($request->has('send_email')) {
                                CompanyBooking::sendMail($booking, false, true, null, $request->send_email);
                            } else {
                                CompanyBooking::sendMail($booking, false, true);
                            }
                            if ($request->input('send_sms')) {
                                CompanyBooking::sendSMS($booking, $booking_client, true, false, true);
                            }
                            if ($request->input('send_sms_practitioner')) {
                                CompanyBooking::sendSMS($booking, null, false, true, true);
                            }
                        }
                    }

                    if (
                        count($request->client_ids)
                        !=
                        CompanyBookingClient::where('booking_id', $booking->id)
                        ->where('is_cancelled', 0)
                        ->where('is_verified', 1)->count()
                    ) {
                        $company_booking_clients = CompanyBookingClient::where('booking_id', $booking->id)
                            ->where('is_cancelled', 0)
                            ->where('is_verified', 1)->cursor();
                        foreach ($company_booking_clients as $company_booking_client) {
                            if (!in_array($company_booking_client->client_id, $request->client_ids)) {
                                CompanyBooking::cancelBooking($booking, false, $company_booking_client);
                                CompanyBooking::cancelBookingSMS($booking, $company_booking_client);
                            }
                        }
                    }
                }
            }



            $is_same_practitioner = false;
            if ($request->has('user_id')) {
                if ($booking->user_id == $request->user_id) {
                    $is_same_practitioner = true;
                } else {
                    BookingGoogleCalendarManager::removeBooking($booking);
                    $booking->user_id = $request->user_id;
                }
            }

            $service = CompanyService::findOrFail($booking->service_id);
            $is_same_time = false;
            $should_make_slot_unavailable = $booking->should_make_slot_unavailable;
            if ($request->has('start_at') && $request->has('end_at')) {
                $start_at = Carbon::parse($request->start_at);
                $end_at = $request->has('end_at') ? Carbon::parse($request->end_at) : Carbon::parse($request->start_at)->addMinutes($service->duration ?? 0)->addMinutes($service->time_margin ?? 0);

                if ($booking->start_at == $request->start_at && $booking->end_at == $request->end_at) {
                    $is_same_time = true;
                    // return response()->json([
                    //     'message' => __('strings.can_not_reschedule_to_same_date'),
                    //     'status' => '0',
                    // ]);
                } else {
                    if (($request->has('send_email') && $request->send_email) || ($request->input('notify') && $request->notify)) {
                        $client_email_rescheduled_on = Setting::getSetting($company, Setting::EMAIL_CLIENT_BOOKING_RESCHEDULED_ON)?->value;
                        if (!$client_email_rescheduled_on) {
                            throw new HttpException('200', __('strings.you_have_to_configure_email_template_to_send_email'));
                        }
                    }
                    try {
                        CompanyBooking::isBookingAvailable(
                            User::findOrFail($booking->user_id),
                            $start_at,
                            $end_at,
                            $service,
                            $booking,
                            true
                        );
                    } catch (PractitionerNotAvailableException $th) {
                        try {

                            // if (!($booking->service->category && $booking->service->category->group_booking)) {
                            CompanyBooking::makeSlotAvailable(
                                User::findOrFail($booking->user_id),
                                $start_at,
                                $end_at,
                                $service,
                                $booking
                            );
                            $should_make_slot_unavailable = 1;
                            // } else {
                            //     return response()->json([
                            //         'message' => $th->getMessage(),
                            //         'status' => '0',
                            //     ]);
                            // }

                            CompanyBooking::isBookingAvailable(
                                User::findOrFail($booking->user_id),
                                $start_at,
                                $end_at,
                                $service,
                                $booking,
                                true
                            );
                        } catch (Throwable $th) {
                            if ($th->getMessage() == __('strings.practitioner_is_busy_for_selected_time')) {
                                return response()->json([
                                    'message' => $th->getMessage(),
                                    'status' => '2',
                                ]);
                            } else {
                                return response()->json([
                                    'message' => $th->getMessage(),
                                    'status' => '0',
                                ]);
                            }
                        }
                    } catch (Throwable $th) {
                        if ($th->getMessage() == __('strings.practitioner_is_busy_for_selected_time')) {
                            return response()->json([
                                'message' => $th->getMessage(),
                                'status' => '2',
                            ]);
                        } else {
                            return response()->json([
                                'message' => $th->getMessage(),
                                'status' => '0',
                            ]);
                        }
                    }
                    if ($booking->should_make_slot_unavailable) {
                        CompanyBooking::revertAvailableSlotForThisBooking($booking);
                    }
                    $booking->start_at = $start_at;
                    $booking->end_at = $end_at;
                }
            }

            $isDirty = $booking->isDirty();
            $booking->should_make_slot_unavailable = $should_make_slot_unavailable;
            $booking->save();
            if ($request->has('user_id') && !$is_same_practitioner) {
                if ($request->has('send_email')) {
                    CompanyBooking::practitionerChanged($booking, false, null, $request->send_email);
                } else {
                    CompanyBooking::practitionerChanged($booking);
                }
            }
            if (!$is_same_time) {
                if ($request->input('notify', 1)) {
                    if ($request->has('send_email')) {
                        CompanyBooking::rescheduleBooking($booking, false, null, $request->send_email);
                    } else {
                        CompanyBooking::rescheduleBooking($booking);
                    }
                } else {
                    CompanyBooking::rescheduleBooking($booking, false, null, false);
                }
                if ($request->input('send_sms')) {
                    CompanyBooking::sendSMS($booking, null, true, false, true);
                }
                if ($request->input('send_sms_practitioner')) {
                    CompanyBooking::sendSMS($booking, null, false, true, true);
                }
            } elseif ($isDirty) {
                if ($is_same_practitioner && $is_same_time) {
                    if ($request->has('send_email')) {
                        CompanyBooking::updatedBooking($booking, false, null, $request->send_email);
                    } else {
                        CompanyBooking::updatedBooking($booking);
                    }
                    if ($request->input('send_sms')) {
                        CompanyBooking::sendSMS($booking, null, true, false, true);
                    }
                    if ($request->input('send_sms_practitioner')) {
                        CompanyBooking::sendSMS($booking, null, false, true, true);
                    }
                }
            }

            return response()->json([
                'data' => $booking->refresh()->loadMissing(['company', 'user', 'client', 'service']),
                'message' => __('strings.booking_updated'),
                'status' => '1',
            ]);
        });
    }

    public function noShowComment(CompanyBooking $booking, AddNoShowCommentRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;

        $is_group_booking = 0;
        if (
            $booking->service
            &&
            $booking->service->category
            &&
            $booking->service->category->group_booking
        ) {
            $is_group_booking = 1;
        }

        if (!$is_group_booking) {
            if ($request->has('is_shown')) {
                $booking->is_shown = $request->is_shown;
            }
            if ($request->has('show_comment')) {
                $booking->show_comment = $request->show_comment;
            }
            $isDirty = $booking->isDirty();
            if ($isDirty) {
                $booking->save();
            }
        } else {
            if ($request->has('client_id')) {
                $client_id = $request->client_id;
                $booking_client =  CompanyBookingClient::where('booking_id', $booking->id)
                    ->where('client_id', $client_id)
                    ->where('is_cancelled', 0)
                    ->where('is_verified', 1)
                    ->first();

                if ($booking_client) {
                    if ($request->has('is_shown')) {
                        $booking_client->is_shown = $request->is_shown;
                    }
                    if ($request->has('show_comment')) {
                        $booking_client->show_comment = $request->show_comment;
                    }
                    $booking_client->save();
                }
            }
        }

        return response()->json([
            'data' => $booking->refresh()->loadMissing(['company', 'user', 'client', 'service']),
            'message' => __('strings.booking_updated'),
            'status' => '1',
        ]);
    }

    public function getCount(Request $request)
    {
        $user = Auth::user();
        $company = $user->company;
        $bookings = CompanyBooking::where('is_verified', 1)
            ->where('is_cancelled', 0)
            ->where('company_id', $company->id);

        if ($request->has('start_at') && $request->has('service_id')) {
            $company_service = CompanyService::where('id', $request->service_id)->first();
            if ($company_service) {
                $start_at = Carbon::parse($request->start_at);
                $end_at = Carbon::parse($request->start_at)->addMinutes($company_service->duration);
                $bookings = $bookings
                    ->where('service_id', $company_service->id)
                    ->where(function ($q) use ($start_at, $end_at) {
                        $q = $q->where(function ($q) use ($start_at, $end_at) {
                            $q = $q->where('start_at', '>=', $start_at)->where('start_at', '<', $end_at);
                        })->orWhere(function ($q) use ($start_at, $end_at) {
                            $q = $q->where('end_at', '>', $start_at)->where('end_at', '<=', $end_at);
                        })->orWhere(function ($q) use ($start_at, $end_at) {
                            $q = $q->where('start_at', '<', $start_at)->where('end_at', '>=', $end_at);
                        });
                    });
            }
        }

        return response()->json([
            'data' => $bookings->count(),
            'message' => __('strings.booking_updated'),
            'status' => '1',
        ]);
    }

    public function createInside(CreateOutsideBookingRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;
        if ($request->has('send_email') && $request->send_email) {
            $client_email_confirmation_on = Setting::getSetting($company, Setting::EMAIL_CLIENT_BOOKING_CONFIRMATION_ON)?->value;
            if (!$client_email_confirmation_on) {
                throw new HttpException('200', __('strings.you_have_to_configure_email_template_to_send_email'));
            }
        }
        $service = null;
        if ($request->has('service_id')) {
            $service = CompanyService::findOrFail($request->service_id);
        }
        $start_at = Carbon::parse($request->start_at);
        $end_at = $request->has('end_at') ? Carbon::parse($request->end_at) : Carbon::parse($request->start_at)->addMinutes($service->duration ?? 0)->addMinutes($service->time_margin ?? 0);
        $practitioner = User::findOrFail($request->user_id);
        $should_make_slot_unavailable = 0;

        $sms_confirmation = $request->input('sms_confirmation');

        $template = null;
        if ($sms_confirmation) {
            $template_id = Setting::getSetting($company, Setting::SMS_CLIENT_BOOKING_CONFIRMATION_TEMPLATE_ID);
            if (!$template_id->value) {
                throw new HttpException('200', 'No template selected for SMS confirmation.');
            }
            $template = SMSTemplate::query()->where('company_id', $company->id)->find($template_id);
        }

        $is_group_booking = 0;
        if ($service && $service->category && $service->category->group_booking) {
            $is_group_booking = 1;
        }

        $booking = null;
        if ($is_group_booking) {
            try {
                CompanyBooking::isBookingAvailable(
                    $practitioner,
                    $start_at,
                    $end_at,
                    $service,
                    null,
                    true
                );
            } catch (PractitionerNotAvailableException $th) {
                try {
                    CompanyBooking::makeSlotAvailable(
                        $practitioner,
                        $start_at,
                        $end_at,
                        $service
                    );
                    $should_make_slot_unavailable = 1;
                    CompanyBooking::isBookingAvailable(
                        $practitioner,
                        $start_at,
                        $end_at,
                        $service,
                        null,
                        true
                    );
                } catch (Throwable $th) {
                    if ($th->getMessage() == __('strings.practitioner_is_busy_for_selected_time')) {
                        return response()->json([
                            'message' => $th->getMessage(),
                            'status' => '2',
                        ]);
                    } else {
                        return response()->json([
                            'message' => $th->getMessage(),
                            'status' => '0',
                        ]);
                    }
                }
                // return response()->json([
                //     'message' => $th->getMessage(),
                //     'status' => '0',
                // ]);
            } catch (Throwable $th) {
                if ($th->getMessage() == __('strings.practitioner_is_busy_for_selected_time')) {
                    return response()->json([
                        'message' => $th->getMessage(),
                        'status' => '2',
                    ]);
                } else {
                    return response()->json([
                        'message' => $th->getMessage(),
                        'status' => '0',
                    ]);
                }
            }

            $clients = [];
            if ($request->has('client_ids')) {
                $clients = Client::with(['addresses'])->whereIn('id', $request->client_ids)->get();
            }

            if (count($clients) > $service->group_quantity) {
                return response()->json([
                    'message' => __('strings.max_client_limit_reached_for_this_booking'),
                    'status' => '0',
                ]);
            }

            $booking = CompanyBooking::where('service_id', $service->id)
                ->where('start_at', $start_at)
                ->where('end_at', $end_at)
                ->where('is_verified', 1)
                ->where('is_cancelled', 0)
                ->where('user_id', $request->user_id)
                ->first();

            if (!$booking) {
                $booking = CompanyBooking::create([
                    'company_id' => $company->id,
                    'full_name' => null,
                    'first_name' => null,
                    'last_name' =>  null,
                    'email' => null,
                    'country_code' => null,
                    'phone_number' => null,
                    'address' => null,
                    'city' => null,
                    'zipcode' => null,
                    'country' => null,
                    'state' => null,
                    'special_request' => $request->special_request,
                    'start_at' => $start_at,
                    'end_at' => $end_at,
                    'price' => $service->price ?? 0,
                    'is_cancelled' => 0,
                    'is_verified' => 1,
                    'service_id' => $service->id,
                    'user_id' => $request->user_id,
                    'time_margin' => $service->time_margin ?? 0,
                    'should_make_slot_unavailable' => $should_make_slot_unavailable,
                    'is_bankid_verified' => null,
                ]);
            }

            $existing_client_ids = $booking->active_clients()->get()->pluck('id')->toArray();
            $new_client_ids = $clients->pluck('id')->toArray();
            $array_ids = array_unique(array_merge($existing_client_ids, $new_client_ids));
            if (count($array_ids) > $service->group_quantity) {
                return response()->json([
                    'message' => __('strings.max_client_limit_reached_for_this_booking'),
                    'status' => '0',
                ]);
            }

            $is_new_client_added = false;
            foreach ($clients as $client) {
                if (
                    !CompanyBookingClient::where('booking_id', $booking->id)
                        ->where('client_id', $client->id)
                        ->where('is_cancelled', 0)
                        ->where('is_verified', 1)
                        ->exists()
                ) {
                    $is_new_client_added = true;
                    $company_booking_client = CompanyBookingClient::create([
                        'booking_id' => $booking->id,
                        'client_id' => $client->id,
                        'is_cancelled' => 0,
                        'is_verified' => 1,
                        'otp' => null,
                        'is_bankid_verified' =>  0,
                        'special_request' => $request->special_request,
                    ]);
                    if ($request->has('send_email')) {
                        CompanyBooking::sendMail($booking, false, true, $company_booking_client, $request->send_email);
                    } else {
                        CompanyBooking::sendMail($booking, false, true);
                    }
                    if ($request->input('send_sms')) {
                        CompanyBooking::sendSMS($booking, $company_booking_client, true, false, true);
                    }
                    if ($request->input('send_sms_practitioner')) {
                        CompanyBooking::sendSMS($booking, null, false, true, true);
                    }
                }
            }

            if (!$is_new_client_added) {
                return response()->json([
                    'message' => __('strings.client_already_in_booking'),
                    'status' => '0',
                ]);
            }
        } else {
            try {
                CompanyBooking::isBookingAvailable(
                    $practitioner,
                    $start_at,
                    $end_at,
                    $service,
                    null,
                    true
                );
            } catch (PractitionerNotAvailableException $th) {
                try {
                    CompanyBooking::makeSlotAvailable(
                        $practitioner,
                        $start_at,
                        $end_at,
                        $service
                    );
                    $should_make_slot_unavailable = 1;
                    CompanyBooking::isBookingAvailable(
                        $practitioner,
                        $start_at,
                        $end_at,
                        $service,
                        null,
                        true
                    );
                } catch (Throwable $th) {
                    if ($th->getMessage() == __('strings.practitioner_is_busy_for_selected_time')) {
                        return response()->json([
                            'message' => $th->getMessage(),
                            'status' => '2',
                        ]);
                    } else {
                        return response()->json([
                            'message' => $th->getMessage(),
                            'status' => '0',
                        ]);
                    }
                }
            } catch (Throwable $th) {
                if ($th->getMessage() == __('strings.practitioner_is_busy_for_selected_time')) {
                    return response()->json([
                        'message' => $th->getMessage(),
                        'status' => '2',
                    ]);
                } else {
                    return response()->json([
                        'message' => $th->getMessage(),
                        'status' => '0',
                    ]);
                }
            }

            $client = null;
            if ($request->has('client_id')) {
                $client = Client::with(['addresses'])->findOrFail($request->client_id);
            }
            $address = null;
            if (count($client->addresses) > 0) {
                $address = $client->addresses[0];
            }


            $booking = CompanyBooking::create([
                'company_id' => $company->id,
                // 'full_name' => $client->first_name . ' ' . $client->last_name,
                'email' => $client->email,
                'country_code' => null,
                'phone_number' => $client->phone_number ?? '',
                'address' => $address ? $address->street_address : null,
                'city' => $address ? $address->city : null,
                'zipcode' => $address ? $address->zipcode : null,
                'country' => $address ? $address->country : null,
                'state' => $address ? $address->state : null,
                'special_request' => $request->special_request,
                'start_at' => $start_at,
                'end_at' => $end_at,
                'price' => $service->price ?? 0,
                'is_cancelled' => 0,
                'is_verified' => 0,
                'service_id' => $service->id,
                'user_id' => $request->user_id,
                'client_id' => $client->id,
                'first_name' => $client->first_name ?? null,
                'last_name' => $client->last_name ?? null,
                'time_margin' => $service->time_margin ?? 0,
                'should_make_slot_unavailable' => $should_make_slot_unavailable,
                'is_bankid_verified' => $request->is_bankid_verified ?? 0,
            ]);
            $booking->is_verified = 1;
            $booking->save();
            if ($request->has('send_email')) {
                CompanyBooking::sendMail($booking, false, true, null, $request->send_email);
            } else {
                CompanyBooking::sendMail($booking, false, true);
            }
            if ($request->input('send_sms')) {
                CompanyBooking::sendSMS($booking, null, true, false, true);
            }
            if ($request->input('send_sms_practitioner')) {
                CompanyBooking::sendSMS($booking, null, false, true, true);
            }
        }

        event(new BookingCreatedEvent($booking));
        return response()->json([
            'data' => $booking->refresh()->loadMissing(['company', 'user', 'service', 'clients']),
            'message' => __('strings.booking_created'),
            'status' => '1',
        ]);
    }

    public function sendKeyOtp($id, $key, Request $request)
    {
        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }
        $company = Company::findOrFail($id);
        $booking = CompanyBooking::findOrFail(Crypt::decrypt($key));
        if ($request->has('client_key')) {
            $company_booking_client = CompanyBookingClient::where('id', Crypt::decrypt($request->client_key))->first();
            CompanyBooking::sendOtp($booking, $company_booking_client);
        } else {
            CompanyBooking::sendOtp($booking);
        }

        return response()->json([
            'message' => __('strings.otp_sent'),
            'status' => '1',
        ]);
    }

    public function verifyKeyOtp($id, $key, Request $request)
    {
        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }
        $company = Company::findOrFail($id);
        $booking = CompanyBooking::findOrFail(Crypt::decrypt($key));

        if ($request->has('client_key')) {
            $company_booking_client = CompanyBookingClient::where('id', Crypt::decrypt($request->client_key))->first();
            if ($company_booking_client->otp != $request->otp) {
                return response()->json([
                    'message' => __('strings.wrong_otp'),
                    'status' => '0',
                ]);
            }
            CompanyBooking::verifyBooking($booking, $company_booking_client);
        } else {
            if ($booking->otp != $request->otp) {
                return response()->json([
                    'message' => __('strings.wrong_otp'),
                    'status' => '0',
                ]);
            }
            CompanyBooking::verifyBooking($booking);
        }
        $client_key = $request->client_key;
        $booking_key = Crypt::encrypt($booking->id);
        return response()->json([
            'client_key' => $client_key,
            'booking_key' => $booking_key,
            'data' => $booking->loadMissing(['company', 'user', 'service']),
            'message' => __('strings.otp_sent'),
            'status' => '1',
        ]);
    }

    public function getKeyBooking($id, $key, Request $request, ReceiptServiceInterface $receiptService)
    {
        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }
        $company = Company::findOrFail($id);
        // $booking_security_setting = Setting::where('company_id', $company->id)->where('key', Setting::BOOKING_SECURITY)->first();
        // if (!$booking_security_setting) {
        //     $booking_security_setting = Setting::create([
        //         'key' => Setting::BOOKING_SECURITY,
        //         'value' => 1,
        //         'company_id' => $company->id
        //     ]);
        //     $booking_security_setting = $booking_security_setting->refresh();
        // }
        // if ($booking_security_setting->value) {
        //     return response()->json([
        //         'message' => __('strings.can_not_get_booking_without_verification'),
        //         'status' => '0',
        //     ]);
        // }
        try {
            $booking = CompanyBooking::findOrFail(Crypt::decrypt($key));
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }
        $company_booking_client = null;

        if ($request->has('client_key')) {

            $company_booking_client = CompanyBookingClient::where('id', Crypt::decrypt($request->client_key))->with('receipt')->first();
            if ($company_booking_client) {
                $booking = $company_booking_client->booking;
                $booking->is_extra_step_done = $company_booking_client->is_extra_step_done;
            }


            $booking = $booking->loadMissing([
                'company',
                'client',
                'user',
                'receipt',
                'service' => function ($query) use ($request) {
                    $query->with([
                        'questionnaires',
                        'letter_of_consents',
                        'company_service_questionnaires' => function ($query) {
                            $query = $query->with(['questionary.questions']);
                        },
                        'category'
                    ]);
                },
                'clients' => function ($query) use ($request) {
                    $query = $query
                        // ->where('clients.id', Crypt::decrypt($request->client_key))
                        ->wherePivot('company_booking_clients.id', Crypt::decrypt($request->client_key))
                        // ->wherePivot('company_booking_clients.is_verified', 1)
                    ;
                }
            ]);
        } else {
            $booking = $booking->loadMissing(['company', 'client', 'user', 'receipt', 'service' => function ($query) {
                $query->with(['questionnaires', 'letter_of_consents', 'category', 'company_service_questionnaires' => function ($query) {
                    $query = $query->with(['questionary.questions']);
                }]);
            }]);
        }

        $receipt = $booking?->receipt ?? $company_booking_client?->receipt;

        if ($request->boolean('status') && $receipt) {
            $receipt = $receiptService->updatePaymentStatus($receipt);
        }

        return response()->json([
            'data' => $booking,
            'receipt' => $receipt,
            'message' => __('strings.user_booking_returned'),
            'status' => '1',
        ]);
    }

    public function cancelBooking($id, CompanyBooking $booking, Request $request)
    {
        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }
        $company = Company::findOrFail($id);
        try {
            if ($request->has('client_key')) {
                $company_booking_client = CompanyBookingClient::where('id', Crypt::decrypt($request->client_key))->first();
                CompanyBooking::cancelBooking($booking, false, $company_booking_client);
                CompanyBooking::cancelBookingSMS($booking, $company_booking_client, false);
            } else {
                CompanyBooking::cancelBooking($booking);
                CompanyBooking::cancelBookingSMS($booking, null, false);
            }
        } catch (Throwable $th) {
            return response()->json([
                'message' => $th->getMessage(),
                'status' => '0',
            ]);
        }
        return response()->json([
            'message' => __('strings.booking_cancelled'),
            'status' => '1',
        ]);
    }
    public function addExtraData($id, CompanyBooking $booking, AddExtraStepRequest $request, PDFServiceInterface $pdfService, ZipServiceInterface $zipService)
    {
        $company = $booking->company;
        $client = null;

        activity()->disableLogging();

        if ($booking->service && $booking->service->category && $booking->service->category->group_booking) {
            if ($request->has('client_key')) {
                $company_booking_client = CompanyBookingClient::
                    // where('is_cancelled', 0)
                    // ->where('is_verified', 1)
                    // ->where('booking_id', $booking->id)
                    where('id', Crypt::decrypt($request->client_key))
                    ->first();
                $client = $company_booking_client->client;
            }
        } else {
            $client = $booking->client;
        }

        $user = $company->users()->first();

        // if ($booking->is_extra_step_done) {
        //     return response()->json([
        //         'message' => __('strings.booking_extra_steps_already_done'),
        //         'status' => '0',
        //     ]);
        // }
        if ($request->boolean('verify', false)) {
            $client_fields = ClientConsentController::getClientFields($client);

            $client->consent()->updateOrCreate([
                'client_id' => $client->id,
            ], [
                'fields' => json_encode($client_fields),
                'message' => __('strings.consent_body', [
                    'company_name' => $client->company->company_name,
                    'fields' => implode('', collect($client_fields[app()->getLocale()])->map(function ($data) {
                        return "•    $data<br>";
                    })->values()->all()),
                ]),
                'verified_at' => now(),
            ]);

            activity()->enableLogging();
            $activity = activity()->performedOn($client);
            $activity = $activity->by($company);
            $activity->log($client->first_name . " " . $client->last_name . "'s consent has been created from booking portal");
            activity()->disableLogging();
        }

        $created_at = now();
        if ($request->has('aesthetic_interest')) {
            $inputs = $request->all();
            if ($request->hasFile('aesthetic_interest.5.image')) {
                $file = $this->saveFile($request->file('aesthetic_interest.5.image'), 'aesthetic_interest', $user);
                $inputs['aesthetic_interest'][5]['image'] = $file->url;
            }

            $aestheticInterest = AestheticInterest::updateOrCreate([
                'client_id' => $client->id,
                'data_new' => json_encode(['aesthetic_interest' => $inputs['aesthetic_interest']]),
            ]);

            $data = ['datas' => $inputs['aesthetic_interest'], 'client' => $client, 'created_at' => $created_at];

            // $file = $this->generateStoreQuestionary($data, 'exports.client_aesthethic_interest_questionary', 'clients/' . md5($client->id) . '/aesthetic_interest/pdf', $user);
            $filename = $this->generateFilePath('clients/' . md5($client->id) . '/aesthetic_interest/pdf', $user, null, 'pdf');
            $file = $pdfService->client($client)->questionary()->aesthethicInterest($inputs['aesthetic_interest'], $created_at)->saveFile($user, $filename);

            QuestionaryData::updateOrcreate([
                'client_id' => $client->id,
                'modelable_type' => AestheticInterest::class,
                'modelable_id' => $aestheticInterest->id,
            ], [
                'pdf' => $file->filename,
                'response' => collect($data)->values(),
            ]);

            if ($request->hasFile('aesthetic_interest.5.image')) {
                $aestheticInterest->file()->save($file);
            }
            CompanyBookingQuestionary::create([
                'company_booking_id' => $booking->id,
                'modelable_type' => AestheticInterest::class,
                'modelable_id' => $aestheticInterest->id,
            ]);

            activity()->enableLogging();
            $activity = activity()->performedOn($aestheticInterest);
            $activity = $activity->by($company);
            $activity->log($client->first_name . ' ' . $client->last_name . "'s aesthetic interest has been created by :causer.company_name from booking portal");
            activity()->disableLogging();
        }

        if ($request->has('health_questions')) {
            $healthQuestionary = HealthQuestionary::updateOrCreate([
                'client_id' => $client->id,
                'data_new' => json_encode($request->input('health_questions')),
            ]);

            $data = ['datas' => $request->input('health_questions'), 'client' => $client, 'created_at' => $created_at];

            // $file = $this->generateStoreQuestionary($data, 'exports.client_health_questionary', 'clients/' . md5($client->id) . '/health_questionary/pdf', $user);
            $filename = $this->generateFilePath('clients/' . md5($client->id) . '/health_questionary/pdf', $user, null, 'pdf');
            $file = $pdfService->client($client)->questionary()->health($request->input('health_questions'), $created_at)->saveFile($user, $filename);

            QuestionaryData::updateOrcreate([
                'client_id' => $client->id,
                'modelable_type' => HealthQuestionary::class,
                'modelable_id' => $healthQuestionary->id,
            ], [
                'pdf' => $file->filename,
                'response' => collect($data)->values(),
            ]);
            CompanyBookingQuestionary::create([
                'company_booking_id' => $booking->id,
                'modelable_type' => HealthQuestionary::class,
                'modelable_id' => $healthQuestionary->id,
            ]);

            activity()->enableLogging();
            $activity = activity()->performedOn($healthQuestionary);
            $activity = $activity->by($company);
            $activity->log($client->first_name . ' ' . $client->last_name . "'s health questionary has been created by :causer.company_name from booking portal");
            activity()->disableLogging();
        }

        if ($request->has('covid19')) {
            $covid19 = Covid19::updateOrCreate([
                'client_id' => $client->id,
                'data' => json_encode($request->input('covid19')),
            ]);

            $data = ['datas' => $request->input('covid19'), 'client' => $client, 'created_at' => $created_at];

            // $file = $this->generateStoreQuestionary($data, 'exports.client_covid19_questionary', 'clients/' . md5($client->id) . '/covid19_questionary/pdf', $user);
            $filename = $this->generateFilePath('clients/' . md5($client->id) . '/covid19_questionary/pdf', $user, null, 'pdf');
            $file = $pdfService->client($client)->questionary()->covid19($request->input('covid19'), $created_at)->saveFile($user, $filename);

            $questionaryData = QuestionaryData::updateOrcreate([
                'client_id' => $client->id,
                'modelable_type' => Covid19::class,
                'modelable_id' => $covid19->id,
            ], [
                'pdf' => $file->filename,
                'response' => collect($data)->values(),
            ]);
            CompanyBookingQuestionary::create([
                'company_booking_id' => $booking->id,
                'modelable_type' => Covid19::class,
                'modelable_id' => $covid19->id,
            ]);

            activity()->enableLogging();
            $activity = activity()->performedOn($covid19);
            $activity = $activity->by($company);
            $activity->log($client->first_name . ' ' . $client->last_name . "'s covid19 questionary has been created by :causer.company_name from booking portal");
            activity()->disableLogging();
        }

        if ($request->has('letter_of_consents')) {
            foreach ($request->letter_of_consents as $index => $letter_of_consent) {
                $signature = null;
                $signature_name = null;
                $signed_file = null;
                $signed_file_name = null;
                $consent_id = null;
                $is_bad_allergic_shock = 'no';
                $is_publish_before_after_pictures = null;
                if (isset($letter_of_consent['consent_id'])) {
                    $consent_id = $letter_of_consent['consent_id'];
                }
                if (isset($letter_of_consent['is_bad_allergic_shock'])) {
                    $is_bad_allergic_shock = $letter_of_consent['is_bad_allergic_shock'];
                }
                if (isset($letter_of_consent['is_publish_before_after_pictures'])) {
                    $is_publish_before_after_pictures = $letter_of_consent['is_publish_before_after_pictures'];
                }

                $consent = LetterOfConsent::findOrFail($consent_id);
                if (isset($letter_of_consent['signature'])) {
                    $signature = $this->saveFile($letter_of_consent['signature'], 'clients/' . md5($client->id) . '/letter_of_consents', $user);
                    $signature_name = $signature->filename;
                }
                if (isset($letter_of_consent['signed_file'])) {
                    $signed_file = $this->saveFile($letter_of_consent['signed_file'], 'clients/' . md5($client->id) . '/letter_of_consents', $user);
                    $signed_file_name = $signed_file->filename;
                }

                $clientLetterOfConsent = ClientLetterOfConsent::create([
                    'signed_file' => $signed_file_name,
                    'client_id' => $client->id,
                    'consent_id' => $consent_id,
                    'is_bad_allergic_shock' => $is_bad_allergic_shock,
                    'signature' => $signature_name,
                    'consent_title' => $consent->consent_title ?? '',
                    'letter' => $consent->letter_html ?? $consent->letter ?? '',
                    'is_publish_before_after_pictures' => $is_publish_before_after_pictures,
                    'version' => $consent->version,
                ]);
                CompanyBookingLetterOfConsent::create([
                    'company_booking_id' => $booking->id,
                    'client_letter_of_consent_id' => $clientLetterOfConsent->id,
                ]);
                if ($signature) {
                    $clientLetterOfConsent->files()->save($signature);
                }
                if ($signed_file) {
                    $clientLetterOfConsent->files()->save($signed_file);
                }

                activity()->enableLogging();
                $activity = activity()->performedOn($clientLetterOfConsent);
                $activity = $activity->by($company);
                $activity->log($client->first_name . ' ' . $client->last_name . "'s letter of consent has been created by :causer.company_name from booking portal");
                activity()->disableLogging();
            }
        }

        if ($request->has('questionary')) {
            $questionaries = $booking->service->questionnaires()->get();


            foreach ($questionaries as $questionaryIndex => $questionary) {
                $data = $request->input("questionary.$questionaryIndex.data");

                $questions = [];
                $questionaryData = $questionary->datas()->create([
                    'client_id' => $client->id,
                    'pdf' => "",
                    'response' => ""
                ]);
                $zip_data = collect();

                foreach ($questionary->questions as $index => $question) {
                    array_push($questions, (object) ['question' => $question->question, 'type' => $question->type]);
                    if ($question->type == QuestionaryQuestion::IMAGE) {
                        $file = $this->saveFile($request->file("questionary.$questionaryIndex.data.$index"), 'questionary/uploads', $user);
                        $data[$index] = $file->filename;
                    }
                    if ($question->type == QuestionaryQuestion::FILE_UPLOAD) {
                        $uploaded_files = $request->file("questionary.$questionaryIndex.data.$index");
                        if ($uploaded_files) {
                            $files = [];
                            foreach ($uploaded_files['files'] as $i => $uploaded_file) {
                                $file = $this->saveFile($uploaded_file, "questionary_files", $user);
                                $questionaryData->files()->save($file);
                                array_push($files, (object) ['file_id' => $file->id, 'file_name' => $request->input("questionary.$questionaryIndex.data.$index.file_names.$i") ?? '']);

                                $zip_data->push([
                                    'path' => $file->filename,
                                    'zip_path' => "Files/Q" . ($index + 1) . "/" . $request->input("questionary.$questionaryIndex.data.$index.file_names.$i") ?? '',
                                    "delete" => false,
                                ]);
                            }
                            $data[$index] = (object) ['files' => $files];
                        }
                    }
                }

                // $fileData = ['questionary' => $questionary, 'data' => $data, 'client' => $client, 'created_at' => $created_at];

                $filename = $this->generateFilePath('clients/' . md5($client->id) . '/questionary/pdf', $user, null, 'pdf');
                $file = $pdfService->client($client)->questionary()->custom($questionary, $data, $created_at, $questions)->saveFile($user, $filename);

                if ($zip_data->count()) {
                    $zip_path = $this->generateFilePath('clients/' . md5($client->id) . '/questionary/pdf', $user, null, "zip");

                    $zip_data->push([
                        'path' => $file->filename,
                        'zip_path' => "{$questionary->title}.pdf",
                        "delete" => false,
                    ]);

                    // Generate and store zip
                    $file = $zipService->make($zip_data)->saveFile($user, $zip_path);
                }
                $questionaryData->pdf = $file->filename;
                $questionaryData->response = collect($data)->values();
                $questionaryData->questions = collect($questions)->values();
                $questionaryData->save();

                CompanyBookingQuestionary::create([
                    'company_booking_id' => $booking->id,
                    'modelable_type' => QuestionaryData::class,
                    'modelable_id' => $questionaryData->id,
                ]);
            }
        }

        if ($booking->service && $booking->service->category && $booking->service->category->group_booking) {
            if ($request->has('client_key')) {
                $company_booking_client = CompanyBookingClient::
                    // where('is_cancelled', 0)
                    //     ->where('is_verified', 1)
                    //     ->where('booking_id', $booking->id)
                    where('id', Crypt::decrypt($request->client_key))
                    ->first();
                $company_booking_client->is_extra_step_done = 1;
                $company_booking_client->save();
            }
            if (!CompanyBookingClient::where('booking_id', $booking->id)
                ->where('is_cancelled', 0)
                ->where('is_verified', 1)
                ->where('is_extra_step_done', 0)
                ->exists()) {
                $booking->is_extra_step_done = 1;
                $booking->save();
            }
        } else {
            $booking->is_extra_step_done = 1;
            $booking->save();
        }


        return response()->json([
            'data' => $booking->loadMissing(['client_letter_of_consents']),
            'message' => __('strings.booking_extra_steps_done'),
            'status' => '1',
        ]);
    }
    public function rescheduleBooking($id, CompanyBooking $booking, Request $request)
    {
        $booking_client = null;

        $final_booking = null;
        $client_key = null;
        if ($request->has('client_key')) {
            $client_key = $request->client_key;
            $client_id = null;
            try {
                $client_id = Crypt::decrypt($request->client_key);
            } catch (\Throwable $th) {
                //throw $th;
            }
            if ($client_id) {
                $booking_client = CompanyBookingClient::where('id', $client_id)->firstOrFail();
            }
        }
        $service = CompanyService::findOrFail($booking->service_id);
        $start_at = Carbon::parse($request->start_at);
        $end_at = Carbon::parse($request->start_at)
            ->addMinutes($service->duration ?? 0)
            ->addMinutes($service->time_margin ?? 0);

        $practitioner = User::findOrFail($booking->user_id);
        if ($request->has('user_id')) {
            if ($booking->user_id != $request->user_id) {
                BookingGoogleCalendarManager::removeBooking($booking);
            }
            $booking->user_id = $request->user_id;
            $practitioner = User::findOrFail($request->user_id);
        }
        if ($service->category && $service->category->group_booking && $booking_client) {
            if ($request->has('start_at')) {
                if ($booking->start_at == $request->start_at) {
                    return response()->json([
                        'message' => __('strings.can_not_reschedule_to_same_date'),
                        'status' => '0',
                    ]);
                }
                try {
                    CompanyBooking::isBookingAvailable(
                        $practitioner,
                        $start_at,
                        $end_at,
                        $service,
                        $booking,
                    );
                } catch (Throwable $th) {
                    return response()->json([
                        'message' => $th->getMessage(),
                        'status' => '0',
                    ]);
                }
                $new_booking = CompanyBooking::where('service_id', $service->id)
                    ->where('start_at', Carbon::parse($start_at))
                    ->where('end_at', Carbon::parse($end_at))
                    ->where('is_cancelled', 0)
                    ->where('is_verified', 1)
                    ->where('user_id', $practitioner->id)
                    ->first();

                if (!$new_booking) {
                    $new_booking = CompanyBooking::create([
                        'company_id' => $booking->company_id,
                        'full_name' => null,
                        'first_name' => null,
                        'last_name' =>  null,
                        'email' => null,
                        'country_code' => null,
                        'phone_number' => null,
                        'address' => null,
                        'city' => null,
                        'zipcode' => null,
                        'country' => null,
                        'state' => null,
                        'special_request' => null,
                        'start_at' => Carbon::parse($request->start_at),
                        'end_at' => Carbon::parse($end_at),
                        'price' => $service->price ?? 0,
                        'is_cancelled' => 0,
                        'is_verified' => 1,
                        'service_id' => $service->id,
                        'user_id' => $request->user_id,
                        'time_margin' => $service->time_margin ?? 0,
                        'is_bankid_verified' => null,
                    ]);
                }
                // $booking->start_at = $start_at;
                // $booking->end_at = $end_at;
                $booking->save();
                if (!CompanyBookingClient::where('booking_id', $new_booking->id)->where('client_id', $booking_client->client_id)->exists()) {
                    $booking_client->booking_id = $new_booking->id;
                } else {
                    $booking_client->delete();
                }
                // $new_company_booking_client = CompanyBookingClient::create([
                //     'booking_id' => $new_booking->id,
                //     'client_id' => $booking_client->client_id,
                //     'is_cancelled' => $booking_client->is_cancelled,
                //     'is_verified' => $booking_client->is_verified,
                //     'otp' => $booking_client->is_verified,
                //     'is_bankid_verified' => $booking_client->is_bankid_verified ?? 0,
                //     'special_request' => $booking_client->special_request,
                // ]);
                // $booking_client->save();
                $final_booking = $new_booking;
                CompanyBooking::checkBookingForDeletion($booking);
                CompanyBooking::rescheduleBooking($new_booking, false, $booking_client);
            }
        } else {
            if ($request->has('start_at')) {
                if ($booking->start_at == $request->start_at) {
                    return response()->json([
                        'message' => __('strings.can_not_reschedule_to_same_date'),
                        'status' => '0',
                    ]);
                }
                try {
                    CompanyBooking::isBookingAvailable(
                        $practitioner,
                        $start_at,
                        $end_at,
                        $service,
                        $booking,
                    );
                } catch (Throwable $th) {
                    return response()->json([
                        'message' => $th->getMessage(),
                        'status' => '0',
                    ]);
                }
                $booking->start_at = $start_at;
                $booking->end_at = $end_at;
            }
            $booking->save();
            $final_booking = $booking;
            CompanyBooking::rescheduleBooking($booking);
        }

        return response()->json([
            'data' => [
                'key' => Crypt::encrypt($final_booking->id),
                'client_key' => $client_key
            ],
            'message' => __('strings.booking_rescheduled'),
            'status' => '1',
        ]);
    }

    public function create($id, CreateBookingRequest $request)
    {
        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }

        activity()->disableLogging();

        $company = Company::findOrFail($id);
        $service = null;
        if ($request->has('service_id')) {
            $service = CompanyService::findOrFail($request->service_id);
        }
        $start_at = Carbon::parse($request->start_at);
        $end_at = Carbon::parse($request->start_at)->addMinutes($service->duration ?? 0);

        $practitioner = User::findOrFail($request->user_id);
        try {
            CompanyBooking::isBookingAvailable(
                $practitioner,
                $start_at,
                $end_at,
                $service
            );
        } catch (Throwable $th) {
            return response()->json([
                'message' => $th->getMessage(),
                'status' => '0',
            ]);
        }
        $client_id = null;
        $client = null;
        if ($request->has('cpr_id')) {
            $clients = $company->clients->filter(function ($c) use ($request, $client) {
                if ($c->cpr_id) {
                    return (strtolower($c->cpr_id) === strtolower($request->cpr_id));
                }

                return false;
            });
            if (count($clients) > 0) {
                if ($client) {
                    return response()->json([
                        'message' => __('strings.Please_enter_unique_cpr_id'),
                        'status' => '0',
                    ]);
                }
                $client = $clients->first();
            }
        }

        if ($request->has('personal_id')) {
            $clients = $company->clients->filter(function ($c) use ($request, $client) {
                if ($c->personal_id) {
                    if ((strtolower($c->personal_id) === strtolower($request->personal_id))) {
                        if ($client) {
                            if ($c->id != $client->id) {
                                return true;
                            } else {
                                return false;
                            }
                        } else {
                            return true;
                        }
                    }
                }
                return false;
            });
            if (count($clients) > 0) {
                if ($client) {
                    return response()->json([
                        'message' => __('strings.Please_enter_unique_personal_id'),
                        'status' => '0',
                    ]);
                }
                $client = $clients->first();
            }
        }
        if ($request->has('email')) {
            $clients = $company->clients->filter(function ($c) use ($request, $client) {
                if ($c->email) {
                    if ((strtolower($c->email) === strtolower($request->email))) {
                        if ($client) {
                            if ($c->id != $client->id) {
                                return true;
                            } else {
                                return false;
                            }
                        } else {
                            return true;
                        }
                    }
                }
                return false;
            });
            if (count($clients) > 0) {
                if ($client) {
                    return response()->json([
                        'message' => __('strings.Please_enter_unique_email'),
                        'status' => '0',
                    ]);
                }
                $client = $clients->first();
            }
        }



        if ($client) {
            $client_id = $client->id;
            $client = Client::findOrFail($client_id);

            if ($request->has('personal_id')) {
                $clients = $company->clients->filter(function ($c) use ($request, $client) {
                    if ($c->personal_id) {
                        return (strtolower($c->personal_id) === strtolower($request->personal_id)) && ($c->id != $client->id);
                    }
                    return false;
                });
                if (count($clients) > 0) {
                    return response()->json([
                        'message' => __('strings.Please_enter_unique_personal_id'),
                        'status' => '0',
                    ]);
                }
                $client->personal_id = $request->personal_id;
            }
            if ($request->has('cpr_id')) {
                $clients = $company->clients->filter(function ($c) use ($request, $client) {
                    if ($c->cpr_id) {
                        return (strtolower($c->cpr_id) === strtolower($request->cpr_id)) && ($c->id != $client->id);
                    }

                    return false;
                });
                if (count($clients) > 0) {
                    return response()->json([
                        'message' => __('strings.Please_enter_unique_cpr_id'),
                        'status' => '0',
                    ]);
                }
                $client->cpr_id = $request->cpr_id;
            }
            if ($request->has('email')) {
                $clients = $company->clients->filter(function ($c) use ($request, $client) {
                    if ($c->cpr_id) {
                        return (strtolower($c->email) === strtolower($request->email)) && ($c->id != $client->id);
                    }
                    return false;
                });
                if (count($clients) > 0) {
                    return response()->json([
                        'message' => __('strings.Please_enter_unique_email'),
                        'status' => '0',
                    ]);
                }
                $client->email = $request->email;
            }
            if ($request->has('is_personal_id_verified') && $request->is_personal_id_verified) {
                $client->is_personal_id_verified = $request->is_personal_id_verified;
            }
            if ($request->has('phone_number') && $request->phone_number) {
                $client->phone_number = $request->phone_number;
            }
            if ($request->has('country_code') && $request->country_code) {
                $client->country_code = $request->country_code;
            }
            $client->save();
        } else {
            $user = $company->users()->first();
            if ($request->has('personal_id')) {
                $clients = $company->clients->filter(function ($c) use ($request) {
                    if ($c->personal_id) {
                        return strtolower($c->personal_id) === strtolower($request->personal_id);
                    }

                    return false;
                });
                if (count($clients) > 0) {
                    return response()->json([
                        'message' => __('strings.Please_enter_unique_personal_id'),
                        'status' => '0',
                    ]);
                }
            }
            if ($request->has('cpr_id')) {
                $clients = $company->clients->filter(function ($c) use ($request) {
                    if ($c->cpr_id) {
                        return strtolower($c->cpr_id) === strtolower($request->cpr_id);
                    }

                    return false;
                });
                if (count($clients) > 0) {
                    return response()->json([
                        'message' => __('strings.Please_enter_unique_cpr_id'),
                        'status' => '0',
                    ]);
                }
            }
            if ($request->has('email')) {
                $clients = $company->clients->filter(function ($c) use ($request) {
                    if ($c->email) {
                        return strtolower($c->email) === strtolower($request->email);
                    }

                    return false;
                });
                if (count($clients) > 0) {
                    return response()->json([
                        'message' => __('strings.Please_enter_unique_email'),
                        'status' => '0',
                    ]);
                }
            }
            $client = Client::create([
                'user_id' => $request->input('user_id') ?? $user->id,
                'company_id' => $company->id,
                'profile_picture' => '',
                'first_name' => $request->input('first_name'),
                'last_name' => $request->input('last_name'),
                'cpr_id' => $request->input('cpr_id'),
                'personal_id' => $request->input('personal_id'),
                'is_personal_id_verified' => $request->input('is_personal_id_verified'),
                'occupation' => $request->input('occupation'),
                'social_security_number' => $request->input('social_security_number', '') ?? '',
                'email' => $request->input('email'),
                'phone_number' => $request->input('phone_number', '') ?? '',
                'country_code' => $request->input('country_code', '') ?? '',
            ]);
            $client->addresses()->create([
                'client_id' => $client->id,
                'street_address' => $request->address ?? '',
                'zip_code' => $request->zipcode ?? '',
                'city' => $request->city ?? '',
                'state' => $request->state ?? '',
                'country' => $request->country ?? '',
            ]);


            activity()->enableLogging();
            $activity = activity()->performedOn($client);
            $activity = $activity->by($company);
            $activity->log(':subject.first_name :subject.last_name client has been created from booking portal');
            activity()->disableLogging();

            // if ($request->has('extra') && count($request->input('extra', [])) > 0) {
            //     $client->extra_fields()->createMany(collect($request->extra)->map(function ($value, $key) {
            //         return [
            //             'company_client_extra_field_id' => $value['id'],
            //             'value' => $value['value'] ?? '',
            //         ];
            //     })->values()->all());
            // }

            if ($request->has('profile_picture')) {
                $file = $this->saveFile($request->file('profile_picture'), 'clients/' . md5($client->id), $user, true);
                $client->profile_picture = $file->filename;
                $client->save();
            }
            $client_id = $client->id;

            NewClientCreated::dispatch($client, $company);
        }
        if ($request->has('extra') && count($request->input('extra', [])) > 0) {
            foreach ($request->extra as $field) {
                $client->extra_fields()->updateOrCreate([
                    'company_client_extra_field_id' => $field['id'],
                ], [
                    'value' => $field['value'] ?? '',
                ]);
            }
            // $client->extra_fields()->createMany(collect($request->extra)->map(function ($value, $key) {
            //     return [
            //         'company_client_extra_field_id' => $value['id'],
            //         'value' => $value['value'] ?? '',
            //     ];
            // })->values()->all());
        }
        $is_group_booking = false;
        if ($service->category && $service->category->group_booking) {
            $is_group_booking = true;
        }
        $booking_security = Setting::where('company_id', $company->id)->where('key', Setting::BOOKING_SECURITY)->first();
        if (!$booking_security) {
            $booking_security = Setting::create([
                'key' => Setting::BOOKING_SECURITY,
                'value' => 0,
                'company_id' => $company->id,
            ]);
            $booking_security = $booking_security->refresh();
        }
        $should_verify = 0;
        if ($booking_security->value) {
            $should_verify = 1;
        }
        $client_key = null;
        $booking = null;
        if ($is_group_booking) {
            $booking = CompanyBooking::where('service_id', $service->id)
                ->where('start_at', Carbon::parse($request->start_at))
                ->where('end_at', Carbon::parse($request->start_at)->addMinutes($service->duration ?? 0)->addMinutes($service->time_margin ?? 0))
                ->where('is_verified', 1)
                ->where('is_cancelled', 0)
                ->where('user_id', $request->user_id)
                ->first();

            if (!$booking) {
                $booking = CompanyBooking::create([
                    'company_id' => $company->id,
                    'full_name' => null,
                    'first_name' => null,
                    'last_name' =>  null,
                    'email' => null,
                    'country_code' => null,
                    'phone_number' => null,
                    'address' => null,
                    'city' => null,
                    'zipcode' => null,
                    'country' => null,
                    'state' => null,
                    'special_request' => null,
                    'start_at' => Carbon::parse($request->start_at),
                    'end_at' => Carbon::parse($request->start_at)->addMinutes($service->duration ?? 0)->addMinutes($service->time_margin ?? 0),
                    'price' => $service->price ?? 0,
                    'is_cancelled' => 0,
                    'is_verified' => 1,
                    'service_id' => $service->id,
                    'user_id' => $request->user_id,
                    'time_margin' => $service->time_margin ?? 0,
                    'is_bankid_verified' => null,
                ]);
            }
            if (!$service->group_quantity || $service->group_quantity <= 0) {
                return response()->json([
                    'message' => __('strings.problem_with_service'),
                    'status' => '0',
                ]);
            }
            if ($booking->active_clients()->count() >= $service->group_quantity) {
                return response()->json([
                    'message' => __('strings.all_slots_full_for_this_booking'),
                    'status' => '0',
                ]);
            }

            if (
                CompanyBookingClient::where('booking_id', $booking->id)
                ->where('client_id', $client->id)
                ->where('is_cancelled', 0)
                ->where('is_verified', 1)
                ->exists()
            ) {
                return response()->json([
                    'message' => __('strings.you_have_already_book_slot_at_this_time'),
                    'status' => '0',
                ]);
            }
            $company_booking_client = CompanyBookingClient::create([
                'booking_id' => $booking->id,
                'client_id' => $client->id,
                'is_cancelled' => 0,
                'is_verified' => 0,
                'otp' => 0,
                'is_bankid_verified' => $request->is_bankid_verified ?? 0,
                'special_request' => $request->special_request,
            ]);
            $client_key = Crypt::encrypt($company_booking_client->id);
            if ($should_verify) {
                CompanyBooking::sendOtp($booking, $company_booking_client);
            } else {
                CompanyBooking::verifyBooking($booking, $company_booking_client);
                CompanyBooking::sendMail($booking, false, true, $company_booking_client);
                CompanyBooking::sendSMS($booking, $company_booking_client, false);
            }
        } else {
            $booking = CompanyBooking::create([
                'company_id' => $company->id,
                'full_name' => $request->full_name ?? null,
                'first_name' => $request->first_name ?? null,
                'last_name' => $request->last_name ?? null,
                'email' => $request->email,
                'country_code' => $request->country_code,
                'phone_number' => $request->phone_number,
                'address' => $request->address,
                'city' => $request->city,
                'zipcode' => $request->zipcode,
                'country' => $request->country,
                'state' => $request->state,
                'special_request' => $request->special_request,
                'start_at' => Carbon::parse($request->start_at),
                'end_at' => Carbon::parse($request->start_at)->addMinutes($service->duration ?? 0)->addMinutes($service->time_margin ?? 0),
                'price' => $service->price ?? 0,
                'is_cancelled' => 0,
                'is_verified' => 0,
                'client_id' => $client_id,
                'service_id' => $service->id,
                'user_id' => $request->user_id,
                'time_margin' => $service->time_margin ?? 0,
                'is_bankid_verified' => $request->is_bankid_verified ?? 0,
            ]);

            if ($should_verify) {
                CompanyBooking::sendOtp($booking);
            } else {
                CompanyBooking::verifyBooking($booking);
                CompanyBooking::sendMail($booking, false, true);
                CompanyBooking::sendSMS($booking, null, false);
            }
        }

        $booking_key = Crypt::encrypt($booking->id);
        if ($should_verify) {
            return response()->json([
                'should_verify' => $should_verify,
                'client_key' => $client_key,
                'booking_key' => $booking_key,
                'data' => $booking->id,
                'message' => __('strings.booking_created_with_otp'),
                'status' => '1',
            ]);
        } else {

            event(new BookingCreatedEvent($booking));

            return response()->json([
                'should_verify' => $should_verify,
                'client_key' => $client_key,
                'booking_key' => $booking_key,
                'data' => $booking->loadMissing(['company', 'user', 'service', 'clients' => function ($query) use ($client) {
                    $query = $query->where('clients.id', $client->id)
                        ->wherePivot('company_booking_clients.is_cancelled', 0)
                        ->wherePivot('company_booking_clients.is_verified', 1);
                }]),
                'message' => __('strings.booking_created'),
                'status' => '1',
            ]);
        }
    }


    public function createTemp($id, CreateBookingRequest $request)
    {
        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }

        activity()->disableLogging();

        $company = Company::findOrFail($id);
        $service = null;
        if ($request->has('service_id')) {
            $service = CompanyService::findOrFail($request->service_id);
        }
        $start_at = Carbon::parse($request->start_at);
        $end_at = Carbon::parse($request->start_at)->addMinutes($service->duration ?? 0);

        $practitioner = User::findOrFail($request->user_id);
        try {
            CompanyBooking::isBookingAvailable(
                $practitioner,
                $start_at,
                $end_at,
                $service
            );
        } catch (Throwable $th) {
            return response()->json([
                'message' => $th->getMessage(),
                'status' => '0',
            ]);
        }
        $client_id = null;
        $clients = $company->clients->filter(function ($client) use ($request) {
            return strtolower($client->email) === strtolower($request->email);
        });
        if (count($clients) > 0) {
            foreach ($clients as $client) {
                $client_id = $client->id;
            }
            $client = Client::findOrFail($client_id);

            if ($request->has('personal_id')) {
                $clients = $company->clients->filter(function ($c) use ($request, $client) {
                    if ($c->personal_id) {
                        return (strtolower($c->personal_id) === strtolower($request->personal_id)) && ($c->id != $client->id);
                    }

                    return false;
                });
                if (count($clients) > 0) {
                    return response()->json([
                        'message' => __('strings.Please_enter_unique_personal_id'),
                        'status' => '0',
                    ]);
                }
                $client->personal_id = $request->personal_id;
            }
            if ($request->has('cpr_id')) {
                $clients = $company->clients->filter(function ($c) use ($request, $client) {
                    if ($c->cpr_id) {
                        return (strtolower($c->cpr_id) === strtolower($request->cpr_id)) && ($c->id != $client->id);
                    }

                    return false;
                });
                if (count($clients) > 0) {
                    return response()->json([
                        'message' => __('strings.Please_enter_unique_cpr_id'),
                        'status' => '0',
                    ]);
                }
                $client->cpr_id = $request->cpr_id;
            }
            if ($request->has('is_personal_id_verified') && $request->is_personal_id_verified) {
                $client->is_personal_id_verified = $request->is_personal_id_verified;
            }
            if ($request->has('phone_number') && $request->phone_number) {
                $client->phone_number = $request->phone_number;
            }
            if ($request->has('country_code') && $request->country_code) {
                $client->country_code = $request->country_code;
            }
            $client->save();
        } else {
            $user = $company->users()->first();
            if ($request->has('personal_id')) {
                $clients = $company->clients->filter(function ($c) use ($request) {
                    if ($c->personal_id) {
                        return strtolower($c->personal_id) === strtolower($request->personal_id);
                    }

                    return false;
                });
                if (count($clients) > 0) {
                    return response()->json([
                        'message' => __('strings.Please_enter_unique_personal_id'),
                        'status' => '0',
                    ]);
                }
            }
            if ($request->has('cpr_id')) {
                $clients = $company->clients->filter(function ($c) use ($request) {
                    if ($c->cpr_id) {
                        return strtolower($c->cpr_id) === strtolower($request->cpr_id);
                    }

                    return false;
                });
                if (count($clients) > 0) {
                    return response()->json([
                        'message' => __('strings.Please_enter_unique_cpr_id'),
                        'status' => '0',
                    ]);
                }
            }
            $client = Client::create([
                'user_id' => $request->input('user_id') ?? $user->id,
                'company_id' => $company->id,
                'profile_picture' => '',
                'first_name' => $request->input('first_name'),
                'last_name' => $request->input('last_name'),
                'personal_id' => $request->input('personal_id'),
                'cpr_id' => $request->input('cpr_id'),
                'is_personal_id_verified' => $request->input('is_personal_id_verified'),
                'occupation' => $request->input('occupation'),
                'social_security_number' => $request->input('social_security_number', '') ?? '',
                'email' => $request->input('email'),
                'phone_number' => $request->input('phone_number', '') ?? '',
                'country_code' => $request->input('country_code', '') ?? '',
            ]);
            $client->addresses()->create([
                'client_id' => $client->id,
                'street_address' => $request->address ?? '',
                'zip_code' => $request->zipcode ?? '',
                'city' => $request->city ?? '',
                'state' => $request->state ?? '',
                'country' => $request->country ?? '',
            ]);

            activity()->enableLogging();
            $activity = activity()->performedOn($client);
            $activity = $activity->by($company);
            $activity->log(':subject.first_name :subject.last_name client has been created from booking portal');
            activity()->disableLogging();

            // if ($request->has('extra') && count($request->input('extra', [])) > 0) {
            //     $client->extra_fields()->createMany(collect($request->extra)->map(function ($value, $key) {
            //         return [
            //             'company_client_extra_field_id' => $value['id'],
            //             'value' => $value['value'] ?? '',
            //         ];
            //     })->values()->all());
            // }

            if ($request->has('profile_picture')) {
                $file = $this->saveFile($request->file('profile_picture'), 'clients/' . md5($client->id), $user, true);
                $client->profile_picture = $file->filename;
                $client->save();
            }
            $client_id = $client->id;

            NewClientCreated::dispatch($client, $company);
        }
        return "Sdsd";
    }

    public function resendOtp(ResendOtpRequest $request)
    {
        $booking = CompanyBooking::findOrFail($request->booking_id);
        if ($request->has('client_key')) {
            $company_booking_client = CompanyBookingClient::where('id', Crypt::decrypt($request->client_key))->first();
            if ($company_booking_client->is_verified) {
                return response()->json([
                    'message' => __('strings.booking_already_verified'),
                    'status' => '0',
                ]);
            }
            CompanyBooking::sendOtp($booking, $company_booking_client);
        } else {
            if ($booking->is_verified) {
                return response()->json([
                    'message' => __('strings.booking_already_verified'),
                    'status' => '0',
                ]);
            }
            CompanyBooking::sendOtp($booking);
        }

        return response()->json([
            'message' => __('strings.otp_resent'),
            'status' => '1',
        ]);
    }

    public function verifyOtp(VerifyBookingRequest $request)
    {
        $booking = CompanyBooking::findOrFail($request->booking_id);
        if ($request->has('client_key')) {
            $company_booking_client = CompanyBookingClient::where('id', Crypt::decrypt($request->client_key))->first();
            if ($company_booking_client->otp != $request->otp) {
                return response()->json([
                    'message' => __('strings.wrong_otp'),
                    'status' => '0',
                ]);
            }
            if (!$company_booking_client->is_verified) {
                CompanyBooking::sendMail($booking, false, true, $company_booking_client);
            }
            CompanyBooking::verifyBooking($booking, $company_booking_client);
            $booking = $booking->refresh();
            $client_key = $request->client_key;
            return response()->json([
                'client_key' => $client_key,
                'booking_key' => Crypt::encrypt($booking->id),
                'data' => $booking->loadMissing(['company', 'user', 'service', 'clients' => function ($query) use ($request) {
                    $query = $query
                        ->wherePivot('company_booking_clients.id', Crypt::decrypt($request->client_key));
                }]),
                'message' => __('strings.booking_verified'),
                'status' => '1',
            ]);
        } else {
            if ($booking->otp != $request->otp) {
                return response()->json([
                    'message' => __('strings.wrong_otp'),
                    'status' => '0',
                ]);
            }
            if (!$booking->is_verified) {
                CompanyBooking::sendMail($booking, false, true);
            }
            CompanyBooking::verifyBooking($booking);
            $booking = $booking->refresh();
            return response()->json([
                'data' => $booking->loadMissing(['company', 'user', 'service']),
                'message' => __('strings.booking_verified'),
                'status' => '1',
            ]);
        }
    }

    public function createOnExisting(CreateOutsideBookingRequest $request)
    {
        $user = Auth::user();
        $company = $user->company;
        $service = null;

        if ($request->has('service_id')) {
            $service = CompanyService::findOrFail($request->service_id);
        }
        $start_at = Carbon::parse($request->start_at);
        $end_at = $request->has('end_at') ? Carbon::parse($request->end_at) : Carbon::parse($request->start_at)->addMinutes($service->duration ?? 0)->addMinutes($service->time_margin ?? 0);
        $practitioner = User::findOrFail($request->user_id);
        $should_make_slot_unavailable = 0;

        $client = null;
        if ($request->has('client_id')) {
            $client = Client::with(['addresses'])->findOrFail($request->client_id);
        }
        $address = null;
        if ($client) {
            if (count($client->addresses) > 0) {
                $address = $client->addresses[0];
            }
        }

        $booking = CompanyBooking::create([
            'company_id' => $company->id,
            // 'full_name' => $client->first_name . ' ' . $client->last_name,
            'email' => $client->email,
            'country_code' => null,
            'phone_number' => $client->phone_number ?? '',
            'address' => $address ? $address->street_address : null,
            'city' => $address ? $address->city : null,
            'zipcode' => $address ? $address->zipcode : null,
            'country' => $address ? $address->country : null,
            'state' => $address ? $address->state : null,
            'special_request' => $request->special_request,
            'start_at' => Carbon::parse($request->start_at),
            'end_at' => $request->has('end_at') ? Carbon::parse($request->end_at) : Carbon::parse($request->start_at)->addMinutes($service->duration ?? 0)->addMinutes($service->time_margin ?? 0),
            'price' => $service->price ?? 0,
            'is_cancelled' => 0,
            'is_verified' => 0,
            'service_id' => $service->id,
            'user_id' => $request->user_id,
            'client_id' => $client->id,
            'first_name' => $client->first_name ?? null,
            'last_name' => $client->last_name ?? null,
            'time_margin' => $service->time_margin ?? 0,
            'should_make_slot_unavailable' => $should_make_slot_unavailable,
            'is_bankid_verified' => $request->is_bankid_verified ?? 0,
        ]);
        $booking->is_verified = 1;
        $booking->save();
        CompanyBooking::sendMail($booking, false, true);
        if ($request->input('send_sms')) {
            CompanyBooking::sendSMS($booking, null, true, false, true);
        }
        if ($request->input('send_sms_practitioner')) {
            CompanyBooking::sendSMS($booking, null, false, true, true);
        }
        event(new BookingCreatedEvent($booking));
        return response()->json([
            'data' => $booking->refresh()->loadMissing(['company', 'user', 'service']),
            'message' => __('strings.booking_created'),
            'status' => '1',
        ]);
    }
}