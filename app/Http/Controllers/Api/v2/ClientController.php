<?php

namespace App\Http\Controllers\Api\v2;

use App\Activity;
use App\AestheticInterest;
use App\Client;
use App\ClientAddress;
use App\ClientLetterOfConsent;
use App\ClientMedia;
use App\ClientPrescription;
use App\ClientTreatment;
use App\Company;
use App\CompanyBooking;
use App\Contracts\Services\PDF\PDFServiceInterface;
use App\Covid19;
use App\GeneralNote;
use App\HealthQuestionary;
use App\Http\Controllers\Api\v1\ClientConsentController;
use App\Http\Controllers\Controller;
use App\Http\Requests\v2\ClientIndexRequest;
use App\Http\Requests\v2\MergeClientRequest;
use App\Http\Requests\v2\StoreClientRequest;
use App\Http\Requests\v2\StorePublicClientRequest;
use App\Http\Requests\v2\UpdateClientRequest;
use App\Imports\BookingImport;
use App\Jobs\NewClientCreated;
use App\LetterOfConsent;
use App\QuestionaryData;
use App\QuestionaryQuestion;
use App\Traits\ApiResponser;
use App\Traits\SaveFile;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Symfony\Component\HttpKernel\Exception\HttpException;
use VerumConsilium\Browsershot\Facades\PDF;

class ClientController extends Controller
{
    use SaveFile;
    use ApiResponser;

    public function popUpData(Request $request)
    {
        $user = Auth::user();
        $clients = $user->company->clients()->without('general_notes', 'letter_of_consents', 'treatments', 'general_questions', 'health_questionaries', 'aesthetic_insterests', 'covid19s');
        $clients = $clients->get()->where('personal_id', '!=', '');
        $clients = $clients->groupBy('personal_id')->filter(function ($groups) {
            return $groups->count() > 1;
        });
        $temp_clients = [];
        foreach ($clients as $client) {
            $temp_clients = array_merge($temp_clients, $client->toArray());
        }
        $clients = collect($temp_clients);
        if (count($clients) < 2) {
            return response()->json([
                'message' => 'not any duplicate clients',
                'status' => '0',
            ]);
        }
        return response()->json([
            'data' => [$clients[0], $clients[1]],
            'message' => 'duplicate clients returned',
            'status' => '1',
        ]);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(ClientIndexRequest $request, Client $client)
    {
        $user = Auth::user();
        if ($user->user_role == User::ADMIN && $user->company->email == $user->email && $request->input('filter') != 'my') {
            $clients = Client::where('company_id', $user->company_id);
        } else {
            $clients = Client::whereHas('accesses', fn($q) => $q->where('user_id', $user->id));
        }

        $priority_clients = [];
        if ($request->has('priority_ids')) {

            $priority_clients = $clients->clone()->whereIn('id', $request->priority_ids)->get();

            $clients = $clients->whereNotIn("id", $request->priority_ids);
        }

        $isPaginated = false;
        if ($request->has('filter')) {
            switch ($request->filter) {
                case 'same_personal_id':
                    if (!$isPaginated) {
                        $clients = $clients->get()->where('personal_id', '!=', '');
                        $isPaginated = true;
                    }
                    $clients = $clients->groupBy('personal_id')->filter(function ($groups) {
                        return $groups->count() > 1;
                    });
                    $temp_clients = [];
                    foreach ($clients as $client) {
                        $temp_clients = array_merge($temp_clients, $client->toArray());
                    }
                    $clients = collect($temp_clients);
                    break;
                case 'my':
                    # code...
                    break;
                default:
                    $clients = $clients->where($request->filter, $request->input('filter_type', '='), $request->input('filter_value', null));
                    break;
            }
        }
        if ($request->missing('filter')) {
            if ($request->boolean('withTrashed', false)) {
                $clients = $clients;
            } elseif ($request->boolean('onlyTrashed', false)) {
                $clients = $clients->where('deleted_at', '!=', null);
            } else {
                $clients = $clients->where('deleted_at', null);
            }
        }
        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');
            if ($orderBy == 'last_name' || $orderBy == 'phone_number' || $orderBy == 'email') {
                if (!$isPaginated) {
                    $clients = $clients->get();
                    $isPaginated = true;
                }
                $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
                $sortExtra = in_array($orderBy, ['last_name', 'email']) ? (SORT_NATURAL | SORT_FLAG_CASE) : (in_array($orderBy, ['phone_number']) ? SORT_STRING : SORT_FLAG_CASE);
                $clients =  $clients->sortBy($orderBy, $sortExtra, $isDescOrder);
                // $clients =  $clients->sort(function ($a, $b) use ($orderBy) {
                //     return ($a->$orderBy == null || $a->$orderBy == "") ? 1 : (($b->$orderBy == null || $b->$orderBy == "") ? -1 : 0);
                // });
            } elseif ($orderBy == 'pending_sign') {
                $clients = $clients->where(function ($query) {
                    $query->whereHas('treatments', function ($query) {
                        $query->where('signed_at', null);
                    })->orWhereHas('letter_of_consents', function ($query) {
                        $query->where('verified_signed_at', null);
                    })->orWhereHas('general_notes', function ($query) {
                        $query->where('signed_at', null);
                    });
                });
            } elseif ($orderBy == 'needs_prescription') {
                $clients = $clients->where(function ($query) {
                    $query->whereHas('prescriptions', function ($query) {
                        $query->where('sign', null)
                            ->where('is_cancelled', false);
                    });
                });
            } elseif ($orderBy == 'prescribed') {
                $clients = $clients->whereHas('prescriptions', function ($query) {
                    $query->where('signed_at', '!=', null)->where('is_cancelled', false);
                });
                $clients = $clients->select('clients.*', DB::raw('(SELECT client_prescriptions.created_at FROM client_prescriptions WHERE client_prescriptions.client_id = clients.id ORDER BY client_prescriptions.created_at desc LIMIT 1) as prescriptions_created_at'));
                $clients->orderBy('prescriptions_created_at', 'desc');
            } else {
                $clients = $clients->orderBy($orderBy, $orderDirection);
            }
        }
        if ($request->has('search')) {
            if (!$isPaginated) {
                $clients = $clients->with('addresses')->get();
                $isPaginated = true;
            }
            $search = Str::lower($request->input('search'));
            $search = preg_replace('/\s\s+/', ' ', $search);
            if (count(explode(' ', $search)) > 1) {
                $search_array = explode(' ', $search);
                $last_name = end($search_array);
                $first_name = null;
                for ($i = 0; $i < count($search_array) - 1; $i++) {
                    $first_name = $first_name . " " . $search_array[$i];
                }
                $clients = $clients->filter(function ($value) use ($search) {
                    $search_array = explode(' ', $search);
                    $last_name = end($search_array);
                    $first_name = null;
                    for ($i = 0; $i < count($search_array) - 1; $i++) {
                        $first_name = $first_name . " " . $search_array[$i];
                    }
                    $first_name =  trim($first_name);
                    if (strlen($last_name) > 1) {
                        return (
                            (Str::contains(Str::lower($value->first_name), $first_name) &&
                                Str::contains(Str::lower($value->last_name), $last_name)))
                            ||
                            Str::contains(Str::lower($value->full_name), $search) ||
                            Str::contains(Str::lower($value->email), $search) ||
                            Str::contains(Str::lower("+" . $value->country_code . " " . $value->phone_number), $search) ||
                            Str::contains(Str::lower("+" . $value->social_security_number), $search) ||
                            Str::contains(Str::lower("+" . $value->personal_id), $search) ||
                            Str::contains(Str::lower($value->phone_number), $search) ||
                            $value->addresses->filter(function ($address) use ($search) {
                                return Str::contains(Str::lower($address->street_address), $search) ||
                                    Str::contains(Str::lower($address->state), $search) ||
                                    Str::contains(Str::lower($address->zip_code), $search) ||
                                    Str::contains(Str::lower($address->city), $search);
                            })->count() > 0;
                    } else {
                        return (Str::contains(Str::lower($value->first_name), $first_name)
                            &&
                            Str::lower($value->last_name)[0] == $last_name[0]
                        )
                            ||
                            Str::contains(Str::lower($value->full_name), $search) ||
                            Str::contains(Str::lower($value->email), $search) ||
                            Str::contains(Str::lower("+" . $value->country_code . " " . $value->phone_number), $search) ||
                            Str::contains(Str::lower("+" . $value->social_security_number), $search) ||
                            Str::contains(Str::lower("+" . $value->personal_id), $search) ||
                            $value->addresses->filter(function ($address) use ($search) {
                                return Str::contains(Str::lower($address->street_address), $search) ||
                                    Str::contains(Str::lower($address->state), $search) ||
                                    Str::contains(Str::lower($address->zip_code), $search) ||
                                    Str::contains(Str::lower($address->city), $search);
                            })->count() > 0;
                    }
                });
            } else {
                $clients = $clients->filter(function ($value) use ($search) {
                    return Str::contains(Str::lower($value->first_name), $search) ||
                        Str::contains(Str::lower($value->last_name), $search) ||
                        Str::contains(Str::lower($value->full_name), $search) ||
                        Str::contains(Str::lower($value->email), $search) ||
                        Str::contains(Str::lower("+" . $value->country_code . " " . $value->phone_number), $search) ||
                        Str::contains(Str::lower("+" . $value->social_security_number), $search) ||
                        Str::contains(Str::lower("+" . $value->personal_id), $search) ||
                        $value->addresses->filter(function ($address) use ($search) {
                            return Str::contains(Str::lower($address->street_address), $search) ||
                                Str::contains(Str::lower($address->state), $search) ||
                                Str::contains(Str::lower($address->zip_code), $search) ||
                                Str::contains(Str::lower($address->city), $search);
                        })->count() > 0;
                });
            }
            if (
                $request->missing('orderBy')
                ||
                ($request->has('orderBy') && (in_array($request->orderBy, ['created_at'])))
            ) {
                $clients = $clients->sortByDesc(function ($i, $k) use ($search) {
                    $props = [
                        // 'full_name' => 10,
                        'first_name' => 5,
                        'last_name' => 5,
                        'email' => 2,
                        'phone_number' => 1,
                        'country_code' => 1,
                    ];
                    $weight = 0;
                    foreach ($props as $prop => $acquire_weight) {
                        if (strpos(Str::lower($i->{$prop}), $search) !== false) {
                            $weight += $acquire_weight;
                        } // Increase weight if the search term is found
                    }
                    $search_array = explode(' ', $search);
                    if (Str::lower($i->first_name) == Str::lower($search_array[0])) {
                        $weight = $weight + 100;
                    }
                    return $weight;
                });
            }
        }
        if ($request->has('page')) {
            return response()->json(
                collect([
                    'priority_clients' => $priority_clients,
                    'message' => 'Clients return successfully',
                    'status' => '1',
                ])->merge($isPaginated ? $this->paginate($clients) : $clients->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'priority_clients' => $priority_clients,
                'data' => $isPaginated ? $clients : $clients->get(),
                'message' => 'Clients return successfully',
                'status' => '1',
            ]);
        }
    }

    public function pdf(Client $client)
    {
        $this->authorize('view', [Client::class, $client]);

        $user = Auth::user();
        dispatch(function () use ($user, $client) {
            activity()
                ->performedOn($client)
                ->by($user)
                ->log("{$user->first_name} {$user->last_name} exported patient {$client->first_name} {$client->last_name} record.");
        });

        return PDF::loadView('exports.client', ['client' => $client])
            ->waitUntilNetworkIdle()
            ->format('A4')
            ->margins(15, 0, 15, 0)
            ->download();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreClientRequest $request)
    {
        $this->authorize('create', [Client::class]);

        $client = DB::transaction(function () use ($request) {
            $user = Auth::user()->loadMissing(['company.clients', 'company.users']);

            // $count = $user->company->clients
            //     ->where('email', $request->input('email'))
            //     ->count();
            $count = $user->company->clients
                ->filter(function ($client) use ($request) {
                    return strtolower($client->email) === strtolower($request->email);
                })->count();

            if ($count > 0) {
                throw new HttpException(200, __('strings.Please_enter_unique_email'));
            }


            if ($request->has('personal_id')) {
                $count = $user->company->clients
                    ->filter(function ($client) use ($request) {
                        if ($client->personal_id) {
                            return strtolower($client->personal_id) === strtolower($request->personal_id);
                        }
                        return false;
                    })->count();

                if ($count > 0) {
                    throw new HttpException(200, __('strings.Please_enter_unique_personal_id'));
                }
            }

            $file = null;
            if ($request->has('profile_picture')) {
                $file = $this->saveFile($request->file('profile_picture'), 'client', null, true);
            }

            $client = Client::create([
                'user_id' => $user->id,
                'company_id' => $user->company->id,
                'profile_picture' => $file ? $file->filename : '',
                'first_name' => $request->input('first_name'),
                'last_name' => $request->input('last_name'),
                'occupation' => $request->input('occupation'),
                'social_security_number' => $request->input('social_security_number', '') ?? '',
                'email' => $request->input('email'),
                'phone_number' => $request->input('phone_number', '') ?? '',
            ]);

            if ($request->has('extra') && count($request->input('extra', [])) > 0) {
                $client->extra_fields()->createMany(collect($request->extra)->map(function ($value, $key) {
                    return [
                        'company_client_extra_field_id' => $value['id'],
                        'value' => $value['value'] ?? '',
                    ];
                }));
            }

            if ($file) {
                $client->file()->save($file);
            }

            foreach ($request->input('addressess', []) ?? [] as $index => $addressessInput) {
                $client->addresses()->create([
                    'street_address' => array_key_exists('street_address', $addressessInput) ? $addressessInput['street_address'] : '',
                    'zip_code' => array_key_exists('zip_code', $addressessInput) ? $addressessInput['zip_code'] : '',
                    'city' => array_key_exists('city', $addressessInput) ? $addressessInput['city'] : '',
                    'state' => array_key_exists('state', $addressessInput) ? $addressessInput['state'] : '',
                    'country' => array_key_exists('country', $addressessInput) ? $addressessInput['country'] : '',
                ]);
            }

            foreach ($user->company->users as $index => $user_in) {
                $user_in->accesses()->toggle([$this->client->id]);
            }

            NewClientCreated::dispatch($client, $user->company, false);

            return $client;
        });

        return response()->json([
            'data' => $client,
            'message' => __('strings.Client_created_successfully'),
            'status' => '1',
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function createPublic(Request $request)
    {
        $request->merge([
            'company_id' => Company::decryptId($request->company_id),
        ]);

        $validatedData = Validator::make($request->all(), [
            'company_id' => 'required|exists:companies,id',
            'email' => 'required|email',
        ]);

        $company = Company::findOrFail($request->company_id);

        if ($company->is_read_only) {
            throw new HttpException(403, __('strings.This_Account_is_readonly_please_contact_admin'));
        }

        if (!$company->record_plan) {
            throw new AuthorizationException(__('strings.Please_subscribe_to_a_plan'), 403);
        }

        $value = [];

        $count = $company->clients
            ->filter(function ($client) use ($request) {
                return strtolower($client->email) === strtolower($request->email);
            })->count();

        if ($count > 0) {
            $value['email'] = __('strings.Please_enter_unique_email');
        }

        if ($request->has('personal_id')) {
            $count = $company->clients
                ->filter(function ($client) use ($request) {
                    if ($client->personal_id) {
                        return strtolower($client->personal_id) === strtolower($request->personal_id);
                    }
                    return false;
                })->count();

            if ($count > 0) {
                $value['personal_id'] = __('strings.Please_enter_unique_personal_id');
            }
        }

        return response()->json([
            'data' => $value,
            'status' => '1',
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function storePublic(StorePublicClientRequest $request, PDFServiceInterface $pdfService)
    {
        $client = DB::transaction(function () use ($request, $pdfService) {
            activity()->disableLogging();

            // app(Client::class)->disableLogging();
            // app(AestheticInterest::class)->disableLogging();
            // app(HealthQuestionary::class)->disableLogging();
            // app(Covid19::class)->disableLogging();

            $company = Company::findOrFail($request->company_id);

            if ($company->is_read_only) {
                throw new HttpException(403, __('strings.This_Account_is_readonly_please_contact_admin'));
            }

            if (!$company->record_plan) {
                throw new AuthorizationException(__('strings.Please_subscribe_to_a_plan'), 403);
            }

            if ($company->clients()->count() >= $company->activeSubscription()->plan->client) {
                throw new Exception(__('strings.Maximun_clients_limit_has_reached'), 426);
            }
            // if ($request->input('social_security_number')) {
            //     $count = $company->clients
            //         ->where('social_security_number', $request->input('social_security_number'))
            //         ->count();

            //     if ($count > 0) {
            //         return response()->json([
            //             'message' => __('strings.Please_enter_unique_social_security_number'),
            //             'status' => '0'
            //         ]);
            //     }
            // }

            // $count = $company->clients
            //     ->where('email', $request->input('email'))
            //     ->count();

            $count = $company->clients
                ->filter(function ($client) use ($request) {
                    return strtolower($client->email) === strtolower($request->email);
                })->count();

            if ($count > 0) {
                throw new Exception(__('strings.Please_enter_unique_email'), 500);
            }

            if ($request->has('personal_id')) {
                $count = $company->clients
                    ->filter(function ($client) use ($request) {
                        return strtolower($client->personal_id) === strtolower($request->personal_id);
                    })->count();

                if ($count > 0) {
                    throw new Exception(__('strings.Please_enter_unique_personal_id'), 500);
                }
            }

            $file = null;
            $user = $company->users()->first();

            $client = Client::create([
                'user_id' => $request->has('user_id') ? $request->user_id : null,
                'company_id' => $company->id,
                'profile_picture' => $file ? $file->filename : '',
                'first_name' => $request->input('first_name'),
                'last_name' => $request->input('last_name'),
                'occupation' => $request->input('occupation'),
                'social_security_number' => $request->input('social_security_number', '') ?? '',
                'email' => $request->input('email'),
                'phone_number' => $request->input('phone_number', '') ?? '',
            ]);

            if ($request->has('extra') && count($request->input('extra', [])) > 0) {
                $client->extra_fields()->createMany(collect($request->extra)->map(function ($value, $key) {
                    return [
                        'company_client_extra_field_id' => $value['id'],
                        'value' => $value['value'] ?? '',
                    ];
                })->values()->all());
            }

            activity()->enableLogging();
            $activity = activity()
                ->performedOn($client);

            if ($request->has('user_id')) {
                $activity = $activity->by(User::find($request->user_id));
                $activity->log(':subject.first_name :subject.last_name client has been created by :causer.first_name :causer.last_name from registration portal');
            } else {
                $activity = $activity->by($company);
                $activity->log(':subject.first_name :subject.last_name client has been created by :causer.company_name from registration portal');
            }
            activity()->disableLogging();

            if ($request->has('profile_picture')) {
                $file = $this->saveFile($request->file('profile_picture'), 'clients/' . md5($client->id), $user, true);

                $client->profile_picture = $file->filename;
                $client->save();
            }

            if ($file) {
                $client->file()->save($file);
            }

            // foreach ($company->users as $index => $user_in) {
            //     $user_in->accesses()->toggle([$client->id]);
            // }

            NewClientCreated::dispatch($client, $company);

            // $is_client_access_full_access = $company->settings()->where('key', 'CLIENT_ACCESS_FULL_ACCESS') ?? 0;

            // if ($is_client_access_full_access) {
            // } else {
            //     if ($request->has('user_id')) {
            //         $user = User::findOrFail($request->user_id);
            //         $user->accesses()->toggle([$client->id]);
            //     }
            // }

            foreach ($request->input('addressess', []) ?? [] as $index => $addressessInput) {
                $clientAddress = $client->addresses()->create([
                    'client_id' => $client->id,
                    'street_address' => array_key_exists('street_address', $addressessInput) ? $addressessInput['street_address'] : '',
                    'zip_code' => array_key_exists('zip_code', $addressessInput) ? $addressessInput['zip_code'] : '',
                    'city' => array_key_exists('city', $addressessInput) ? $addressessInput['city'] : '',
                    'state' => array_key_exists('state', $addressessInput) ? $addressessInput['state'] : '',
                    'country' => array_key_exists('country', $addressessInput) ? $addressessInput['country'] : '',
                ]);

                activity()->enableLogging();
                $activity = activity()
                    ->performedOn($clientAddress);

                if ($request->has('user_id')) {
                    $activity = $activity->by(User::find($request->user_id));
                    $activity->log("{$client->first_name} {$client->last_name} client 's address has been created by :causer.first_name :causer.last_name from registration portal");
                } else {
                    $activity = $activity->by($company);
                    $activity->log("{$client->first_name} {$client->last_name} client 's address has been created by :causer.company_name from registration portal");
                }
                activity()->disableLogging();
            }

            if ($request->has('aesthetic_interest')) {
                $inputs = $request->all();
                if ($request->hasFile('aesthetic_interest.5.image')) {
                    $file = $this->saveFile($request->file('aesthetic_interest.5.image'), 'aesthetic_interest', $user);
                    $inputs['aesthetic_interest'][5]['image'] = $file->url;
                }

                $aestheticInterest = AestheticInterest::updateOrCreate([
                    'client_id' => $client->id,
                ], [
                    'data_new' => json_encode(['aesthetic_interest' => $inputs['aesthetic_interest']]),
                ]);
                $created_at = now();
                $data = ['datas' => $inputs['aesthetic_interest'], 'client' => $client, 'created_at' => $created_at];


                // $file = $this->generateStoreQuestionary($data, 'exports.client_aesthethic_interest_questionary', 'clients/' . md5($client->id) . '/aesthetic_interest/pdf', $user);
                $filename = $this->generateFilePath('clients/' . md5($client->id) . '/aesthetic_interest/pdf', $user, null, 'pdf');
                $file = $pdfService->client($client)->questionary()->aesthethicInterest($inputs['aesthetic_interest'], $created_at)->saveFile($user, $filename);

                $questionaryData = QuestionaryData::updateOrcreate([
                    'client_id' => $client->id,
                    'modelable_type' => AestheticInterest::class,
                    'modelable_id' => $aestheticInterest->id,
                ], [
                    'pdf' => $file->filename,
                    'response' => collect($data)->values(),
                ]);

                activity()->enableLogging();
                $activity = activity()
                    ->performedOn($aestheticInterest);

                if ($request->has('user_id')) {
                    $activity = $activity->by(User::find($request->user_id));
                    $activity->log($client->first_name . ' ' . $client->last_name . "'s aesthetic interest has been created by :causer.first_name :causer.last_name from registration portal");
                } else {
                    $activity = $activity->by($company);
                    $activity->log($client->first_name . ' ' . $client->last_name . "'s aesthetic interest has been created by :causer.company_name from registration portal");
                }
                activity()->disableLogging();

                if ($request->hasFile('aesthetic_interest.5.image')) {
                    $aestheticInterest->file()->save($file);
                }
            }

            if ($request->has('health_questions')) {
                $healthQuestionary = HealthQuestionary::updateOrCreate([
                    'client_id' => $client->id,
                ], [
                    'data_new' => json_encode($request->input('health_questions')),
                ]);

                $data = ['datas' => $request->input('health_questions'), 'client' => $client, 'created_at' => $created_at];

                // $file = $this->generateStoreQuestionary($data, 'exports.client_health_questionary', 'clients/' . md5($client->id) . '/health_questionary/pdf', $user);
                $filename = $this->generateFilePath('clients/' . md5($client->id) . '/health_questionary/pdf', $user, null, 'pdf');
                $file = $pdfService->client($client)->questionary()->health($request->input('health_questions'), $created_at)->saveFile($user, $filename);

                $questionaryData = QuestionaryData::updateOrcreate([
                    'client_id' => $client->id,
                    'modelable_type' => HealthQuestionary::class,
                    'modelable_id' => $healthQuestionary->id,
                ], [
                    'pdf' => $file->filename,
                    'response' => collect($data)->values(),
                ]);

                activity()->enableLogging();
                $activity = activity()
                    ->performedOn($healthQuestionary);

                if ($request->has('user_id')) {
                    $activity = $activity->by(User::find($request->user_id));
                    $activity->log($client->first_name . ' ' . $client->last_name . "'s health questionary has been created by :causer.first_name :causer.last_name from registration portal");
                } else {
                    $activity = $activity->by($company);
                    $activity->log($client->first_name . ' ' . $client->last_name . "'s health questionary has been created by :causer.company_name from registration portal");
                }
                activity()->disableLogging();
            }

            if ($request->has('covid19')) {
                $covid19 = Covid19::updateOrCreate([
                    'client_id' => $client->id,
                ], [
                    'data' => json_encode($request->input('covid19')),
                ]);

                $data = ['datas' => $request->input('covid19'), 'client' => $client, 'created_at' => $created_at];

                // $file = $this->generateStoreQuestionary($data, 'exports.client_covid19_questionary', 'clients/' . md5($client->id) . '/covid19_questionary/pdf', $user);
                $filename = $this->generateFilePath('clients/' . md5($client->id) . '/covid19_questionary/pdf', $user, null, 'pdf');
                $file = $pdfService->client($client)->questionary()->covid19($request->input('covid19'), $created_at)->saveFile($user, $filename);

                $questionaryData = QuestionaryData::updateOrcreate([
                    'client_id' => $client->id,
                    'modelable_type' => Covid19::class,
                    'modelable_id' => $covid19->id,
                ], [
                    'pdf' => $file->filename,
                    'response' => collect($data)->values(),
                ]);

                activity()->enableLogging();
                $activity = activity()
                    ->performedOn($covid19);

                if ($request->has('user_id')) {
                    $activity = $activity->by(User::find($request->user_id));
                    $activity->log($client->first_name . ' ' . $client->last_name . "'s covid19 questionary has been created by :causer.first_name :causer.last_name from registration portal");
                } else {
                    $activity = $activity->by($company);
                    $activity->log($client->first_name . ' ' . $client->last_name . "'s covid19 questionary has been created by :causer.company_name from registration portal");
                }
                activity()->disableLogging();
            }

            if ($request->has('letter_of_consents')) {
                foreach ($request->input('letter_of_consents', []) as $index => $letter_of_consent) {
                    $file = null;
                    $file1 = null;
                    $letter_of_consent['client_id'] = $client->id;

                    if ($request->hasFile("letter_of_consents.$index.signature")) {
                        $file = $this->saveFile($request->file("letter_of_consents.$index.signature"), 'clients/' . md5($client->id) . '/letter_of_consents', $user);

                        $letter_of_consent['signature'] = $file->filename;
                    }
                    if ($request->has("letter_of_consents.$index.signed_file")) {
                        $file1 = $this->saveFile($request->file("letter_of_consents.$index.signed_file"), 'clients/' . md5($client->id) . '/letter_of_consents', $user);

                        $letter_of_consent['signed_file'] = $file1->filename;
                    }

                    if ($request->input("letter_of_consents.$index.consent_id")) {
                        $consent = LetterOfConsent::find($letter_of_consent['consent_id']);
                        if ($consent) {
                            $letter_of_consent['consent_title'] = $consent->consent_title;
                            $letter_of_consent['letter'] = $consent->letter_html ?? $consent->letter ?? '';
                            $letter_of_consent['version'] = $consent->version;
                        } else {
                            $letter_of_consent['consent_title'] = '';
                            $letter_of_consent['letter'] = '';
                            $letter_of_consent['version'] = '0';
                        }
                    } else {
                        $letter_of_consent['consent_title'] = '';
                        $letter_of_consent['letter'] = '';
                        $letter_of_consent['version'] = '0';
                    }

                    $letter_of_consent['is_bad_allergic_shock'] = 'no';

                    $clientLetterOfConsent = ClientLetterOfConsent::create($letter_of_consent);

                    activity()->enableLogging();
                    $activity = activity()
                        ->performedOn($clientLetterOfConsent);

                    if ($request->has('user_id')) {
                        $activity = $activity->by(User::find($request->user_id));
                        $activity->log($client->first_name . ' ' . $client->last_name . "'s letter of consent has been created by :causer.first_name :causer.last_name from registration portal");
                    } else {
                        $activity = $activity->by($company);
                        $activity->log($client->first_name . ' ' . $client->last_name . "'s letter of consent has been created by :causer.company_name from registration portal");
                    }
                    activity()->disableLogging();

                    if ($file) {
                        $clientLetterOfConsent->files()->save($file);
                    }
                    if ($file1) {
                        $clientLetterOfConsent->files()->save($file1);
                    }
                }
            }

            if ($request->has('questionary')) {
                $questionaries = $company->questionaries()->active()->get();

                foreach ($questionaries as $questionaryIndex => $questionary) {
                    $data = $request->input("questionary.$questionaryIndex.data");
                    foreach ($questionary->questions as $index => $question) {
                        if ($question->type == QuestionaryQuestion::IMAGE) {
                            $file = $this->saveFile($request->file("questionary.$questionaryIndex.data.$index"), 'questionary/uploads', $user);
                            $data[$index] = $file->filename;
                        }
                    }

                    $fileData = ['questionary' => $questionary, 'data' => $data, 'client' => $client, 'created_at' => $created_at];

                    // $file = $this->generateStoreQuestionary($fileData, 'exports.client_questionaries', 'clients/' . md5($client->id) . '/questionary/pdf', $user);
                    $filename = $this->generateFilePath('clients/' . md5($client->id) . '/questionary/pdf', $user, null, 'pdf');
                    $file = $pdfService->client($client)->questionary()->custom($questionary, $data, $created_at)->saveFile($user, $filename);

                    $questionaryData = $questionary->datas()->create([
                        'client_id' => $client->id,
                        'pdf' => $file->filename,
                        'response' => collect($data)->values(),
                    ]);
                }
            }

            if ($request->boolean('verify', false)) {
                $client_fields = ClientConsentController::getClientFields($client);

                $client->consent()->updateOrCreate([
                    'client_id' => $client->id,
                ], [
                    'fields' => json_encode($client_fields),
                    'message' => __('strings.consent_body', [
                        'company_name' => $client->company->company_name,
                        'fields' => implode('', collect($client_fields[app()->getLocale()])->map(function ($data) {
                            return "•    $data<br>";
                        })->values()->all()),
                    ]),
                    'verified_at' => now(),
                ]);


                activity()->enableLogging();
                $activity = activity()
                    ->performedOn($client);

                if ($request->has('user_id')) {
                    $activity = $activity->by(User::find($request->user_id));
                    $activity->log($client->first_name . " " . $client->last_name . "'s consent has been created by :causer.first_name :causer.last_name from registration portal");
                } else {
                    $activity = $activity->by($company);
                    $activity->log($client->first_name . " " . $client->last_name . "'s consent has been created from registration portal");
                }
                activity()->disableLogging();
            }

            return $client;
        });

        // try {
        //     Mail::to($client->email)->send(new NewClientToClient($company->company_name));
        // } catch (\Throwable $th) {
        // }

        return response()->json([
            'data' => [
                'id' => $client->id,
            ],
            'message' => __('strings.Client_created_successfully'),
            'status' => '1',
        ]);
    }

    public function logs(Client $client)
    {
        $this->authorize('logs', [Client::class, $client]);

        $activities = Activity::where(function ($query) use ($client) {
            $query = $query->where(function ($query) use ($client) {
                $query->where('subject_type', Client::class)->where('subject_id', $client->getKey());
            });

            if ($client->aesthetic_insterests->count()) {
                $query = $query->orWhere(function ($query) use ($client) {
                    $query->where('subject_type', AestheticInterest::class)->where('subject_id', $client->aesthetic_insterests[0]->getKey());
                });
            }

            if ($client->covid19s->count()) {
                $query = $query->orWhere(function ($query) use ($client) {
                    $query->where('subject_type', Covid19::class)->where('subject_id', $client->covid19s[0]->getKey());
                });
            }

            if ($client->health_questionaries->count()) {
                $query = $query->orWhere(function ($query) use ($client) {
                    $query->where('subject_type', HealthQuestionary::class)->where('subject_id', $client->health_questionaries[0]->getKey());
                });
            }

            if ($client->treatments->count()) {
                $query = $query->orWhere(function ($query) use ($client) {
                    $query->where('subject_type', ClientTreatment::class)->whereIn('subject_id', $client->treatments->pluck('id')->all());
                });
            }

            if ($client->letter_of_consents->count()) {
                $query = $query->orWhere(function ($query) use ($client) {
                    $query->where('subject_type', ClientLetterOfConsent::class)->whereIn('subject_id', $client->letter_of_consents->pluck('id')->all());
                });
            }

            if ($client->general_notes->count()) {
                $query = $query->orWhere(function ($query) use ($client) {
                    $query->where('subject_type', GeneralNote::class)->whereIn('subject_id', $client->general_notes->pluck('id')->all());
                });
            }

            if ($client->addresses->count()) {
                $query = $query->orWhere(function ($query) use ($client) {
                    $query->where('subject_type', ClientAddress::class)->whereIn('subject_id', $client->addresses->pluck('id')->all());
                });
            }
        })->latest()->paginate();

        return response()->json(collect([
            'message' => 'Client Logs returned successfully.',
            'status' => '1',
        ])->merge($activities));
    }

    public function logsDownload(Client $client)
    {
        $this->authorize('logs', [Client::class, $client]);

        $logs = Activity::where(function ($query) use ($client) {
            $query = $query->where(function ($query) use ($client) {
                $query->where('subject_type', Client::class)->where('subject_id', $client->getKey());
            });

            if ($client->aesthetic_insterests->count()) {
                $query = $query->orWhere(function ($query) use ($client) {
                    $query->where('subject_type', AestheticInterest::class)->where('subject_id', $client->aesthetic_insterests[0]->getKey());
                });
            }

            if ($client->covid19s->count()) {
                $query = $query->orWhere(function ($query) use ($client) {
                    $query->where('subject_type', Covid19::class)->where('subject_id', $client->covid19s[0]->getKey());
                });
            }

            if ($client->health_questionaries->count()) {
                $query = $query->orWhere(function ($query) use ($client) {
                    $query->where('subject_type', HealthQuestionary::class)->where('subject_id', $client->health_questionaries[0]->getKey());
                });
            }

            if ($client->treatments->count()) {
                $query = $query->orWhere(function ($query) use ($client) {
                    $query->where('subject_type', ClientTreatment::class)->whereIn('subject_id', $client->treatments->pluck('id')->all());
                });
            }

            if ($client->letter_of_consents->count()) {
                $query = $query->orWhere(function ($query) use ($client) {
                    $query->where('subject_type', ClientLetterOfConsent::class)->whereIn('subject_id', $client->letter_of_consents->pluck('id')->all());
                });
            }

            if ($client->general_notes->count()) {
                $query = $query->orWhere(function ($query) use ($client) {
                    $query->where('subject_type', GeneralNote::class)->whereIn('subject_id', $client->general_notes->pluck('id')->all());
                });
            }

            if ($client->addresses->count()) {
                $query = $query->orWhere(function ($query) use ($client) {
                    $query->where('subject_type', ClientAddress::class)->whereIn('subject_id', $client->addresses->pluck('id')->all());
                });
            }
        })->latest()->get();

        return PDF::loadView('exports.logs', ['client' => $client, 'logs' => $logs])
            ->waitUntilNetworkIdle()
            ->format('A4')
            ->margins(15, 0, 15, 0)
            ->download();
    }

    public function clientAfterCareLogs(Client $client)
    {
        $this->authorize('logs', [Client::class, $client]);

        $activities = Activity::where(function ($query) use ($client) {
            $query = $query->where(function ($query) use ($client) {
                $query->where('subject_type', Client::class)->where('subject_id', $client->getKey());
            })
                ->inLog('aftercare_treatment_mail');
        })->latest()->paginate();

        return response()->json(collect([
            'message' => 'Client Logs returned successfully.',
            'status' => '1',
        ])->merge($activities));
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $client = Client::where('id', $id)
            ->without('general_notes', 'letter_of_consents', 'treatments', 'general_questions', 'aesthetic_insterests', 'health_questionaries', 'covid19s')
            ->withExists([
                'general_notes as general_notes_unsigned_exists' => function ($query) {
                    $query->where('signed_at', null);
                },
                'treatments as treatments_unsigned_exists' => function ($query) {
                    $query->where('signed_at', null);
                },
                'letter_of_consents as letter_of_consents_unsigned_exists' => function ($query) {
                    $query->where('verified_signed_at', null);
                },
                'prescriptions as prescriptions_unsigned_exists' => function ($query) {
                    $query->where('signed_at', null);
                },
            ])
            ->with([
                'consent',
                'verification',
                'extra_fields'
            ])
            ->firstOrFail();

        $client->timestamps = false;
        $this->authorize('view', [Client::class, $client]);
        $client->viewed_at = Carbon::now();
        $client->save();
        $user = Auth::user();

        dispatch(function () use ($user, $client) {
            activity()
                ->performedOn($client)
                ->by($user)
                ->log("{$user->first_name} {$user->last_name} opened patient {$client->first_name} {$client->last_name} record.");
        });

        return response()->json([
            'data' => $client,
            'message' => 'Client return successfully',
            'status' => '1',
        ]);
    }


    public function mergeClient(MergeClientRequest $request)
    {
        $user = Auth::user();

        $clients = Client::whereIn('id', $request->clients)->get();
        if ($request->has('email')) {
            $search = Str::lower($request->input('email'));
            $search = preg_replace('/\s\s+/', ' ', $search);
            $clients = $clients->filter(function ($value) use ($search) {
                return Str::contains(Str::lower($value->email), $search);
            });
        }
        if (count($clients) <= 0) {
            return response()->json([
                'message' => __('strings.no_client_with_given_email'),
                'status' => '0',
            ]);
        }
        $clients = collect($clients);
        $client_to_keep = Client::findOrFail($clients->first()->id);
        if ($request->has('first_name')) {
            $client_to_keep->first_name = $request->first_name;
        }
        if ($request->has('last_name')) {
            $client_to_keep->last_name = $request->last_name ?? '';
        } else {
            $client_to_keep->last_name =  '';
        }
        if ($request->has('personal_id')) {
            $client_to_keep->personal_id = $request->personal_id;
        }
        if ($request->has('cpr_id')) {
            $client_to_keep->cpr_id = $request->cpr_id;
        }
        if ($request->has('occupation')) {
            $client_to_keep->occupation = $request->occupation;
        }
        if ($request->has('social_security_number')) {
            $client_to_keep->social_security_number = $request->social_security_number;
        }
        if ($request->has('phone_number')) {
            $client_to_keep->phone_number = $request->phone_number;
        }
        if ($request->has('country_code')) {
            $client_to_keep->country_code = $request->country_code;
        }

        if ($request->has('extra') && count($request->input('extra', [])) > 0) {
            $client_to_keep->extra_fields()->createMany(collect($request->extra)->map(function ($value, $key) {
                return [
                    'company_client_extra_field_id' => $value['id'],
                    'value' => $value['value'] ?? '',
                ];
            })->values()->all());
        }
        $client_to_keep->save();
        $clients_to_delete = Client::whereIn('id', $request->clients)->where('id', '!=', $client_to_keep->id)->cursor();

        foreach ($clients_to_delete as $client) {
            if (CompanyBooking::where('is_cancelled', 0)
                ->where('is_verified', 1)
                ->whereHas('service', function ($query) {
                    $query->whereHas('category', function ($query) {
                        $query->where('group_booking', 1);
                    });
                })
                ->whereHas('active_clients', function ($query) use ($client) {
                    $query = $query
                        ->where('clients.id', $client->id);
                })
                ->where('start_at', '>=', Carbon::now())
                ->exists()
            ) {
                return response()->json([
                    'message' => __('strings.can_not_merge_upcoming_booking'),
                    'status' => '0',
                ]);
            }
        }


        $client_ids_to_delete = [];
        foreach ($clients_to_delete as $client_to_delete) {
            //checking and transferring next to kin data
            if (!$client_to_keep->kind) {
                $client_to_delete->kind()->update([
                    'client_id' => $client_to_keep->id,
                ]);
            }

            //checking and transferring verified data
            if (!$client_to_keep->verification) {
                $client_to_delete->verification()->update([
                    'client_id' => $client_to_keep->id,
                ]);
            }

            //MEDIA transfer
            $media_to_delete = ClientMedia::updateOrCreate([
                'client_id' => $client_to_delete->id,
            ]);
            $media_to_keep = ClientMedia::updateOrCreate([
                'client_id' => $client_to_keep->id,
            ]);
            if ($media_to_delete) {
                $media_to_delete->files()->update([
                    'fileable_id' => $media_to_keep->id,
                ]);
            }

            //procedure transfer
            $client_to_delete->treatments()->update([
                'client_id' => $client_to_keep->id
            ]);

            //general notes transfer
            $client_to_delete->general_notes()->update([
                'client_id' => $client_to_keep->id
            ]);

            //letter_of_consents transfer
            $client_to_delete->letter_of_consents()->update([
                'client_id' => $client_to_keep->id
            ]);

            //booking transfer
            $client_to_delete->bookings()->update([
                'client_id' => $client_to_keep->id
            ]);

            //questionary_data transfer
            $client_to_delete->questionary_data()->update([
                'client_id' => $client_to_keep->id
            ]);

            //questionary_data transfer
            $client_to_delete->questionary_data()->update([
                'client_id' => $client_to_keep->id
            ]);

            //prescription transfer
            $client_to_delete->all_prescriptions()->update([
                'client_id' => $client_to_keep->id
            ]);



            $client_ids_to_delete = array_merge($client_ids_to_delete, [$client_to_delete->id]);

            //logs transfer
            Activity::where(function ($query) use ($client_to_delete) {
                $query = $query->where(function ($query) use ($client_to_delete) {
                    $query->where('subject_type', Client::class)->where('subject_id', $client_to_delete->getKey());
                });
                $query = $query->orWhere(function ($query) use ($client_to_delete) {
                    $query->where('subject_type', HealthQuestionary::class)->whereIn('subject_id', $client_to_delete->health_questionaries->pluck('id')->toArray());
                });
                $query = $query->orWhere(function ($query) use ($client_to_delete) {
                    $query->where('subject_type', ClientAddress::class)->whereIn('subject_id', $client_to_delete->addresses->pluck('id')->toArray());
                });
            })->update([
                'subject_id' => $client_to_keep->id
            ]);

            dispatch(function () use ($user, $client_to_keep, $client_to_delete) {
                activity()
                    ->performedOn($client_to_keep)
                    ->by($user)
                    ->log("{$user->first_name} {$user->last_name} merged patient {$client_to_delete->first_name} {$client_to_delete->last_name} into {$client_to_keep->first_name} {$client_to_keep->last_name}.");
            });
        }
        foreach ($client_ids_to_delete as $client_id_to_delete) {
            if ($client_id_to_delete != $client_to_keep->id) {
                $temp_client = Client::where('id', $client_id_to_delete)->first();
                $temp_client->delete();
            }
        }
        return response()->json([
            'message' => 'Client Merged successfully',
            'status' => '1',
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function edit(Client $client)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function update(UpdateClientRequest $request, Client $client)
    {
        $this->authorize('update', [Client::class, $client]);

        $user = Auth::user();

        if ($request->hasFile('profile_picture')) {
            $file = $this->saveFile($request->file('profile_picture'), 'clients/' . md5($client->id), null, true);

            try {
                //code...
                if ($client->file) {
                    $client->file->delete();
                }
            } catch (\Throwable $th) {
                //throw $th;
            }
            $client->file()->save($file);
            $client->profile_picture = $file->filename;
        }

        if ($request->has('first_name')) {
            $client->first_name = $request->input('first_name');
        }

        if ($request->has('last_name')) {
            $client->last_name = $request->input('last_name');
        }

        // if ($request->input('social_security_number', "")) {
        //     $count = $user->company->clients
        //         ->where('social_security_number', $request->input('social_security_number'))
        //         ->where('social_security_number', '!=', $client->social_security_number)
        //         ->count();

        //     if ($count > 0) {
        //         return response()->json([
        //             'message' => 'Please enter unique social security number.',
        //             'status' => '0'
        //         ]);
        //     }
        // }
        $client->social_security_number = $request->input('social_security_number', '') ?? '';

        if ($request->has('email')) {
            // $count = $user->company->clients
            //     ->where('email', $request->input('email'))
            //     ->where('email', '!=', $client->email)
            //     ->count();

            $count = $user->company->clients
                ->filter(function ($filter_client) use ($request, $client) {
                    return strtolower($filter_client->email) === strtolower($request->email) && strtolower($filter_client->email) !== strtolower($client->email);
                })->count();

            if ($count > 0) {
                return response()->json([
                    'message' => __('strings.Please_enter_unique_email'),
                    'status' => '0',
                ]);
            }
            $client->email = $request->input('email');
        }

        if ($request->has('personal_id')) {

            $count = $user->company->clients
                ->filter(function ($filter_client) use ($request, $client) {
                    if ($filter_client->personal_id) {
                        return strtolower($filter_client->personal_id) === strtolower($request->personal_id) && strtolower($filter_client->personal_id) !== strtolower($client->personal_id);
                    }
                    return false;
                })->count();

            if ($count > 0) {
                return response()->json([
                    'message' => __('strings.Please_enter_unique_personal_id'),
                    'status' => '0',
                ]);
            }
            $client->personal_id = $request->input('personal_id');
        }

        if ($request->has('phone_number')) {
            $client->phone_number = $request->input('phone_number', '') ?? '';
        }

        if ($request->has('occupation')) {
            $client->occupation = $request->input('occupation', '') ?? '';
        }

        if ($request->has('extra') && count($request->input('extra', [])) > 0) {
            $extra_fields = $client->extra_fields;
            foreach ($request->extra as $index => $field) {
                if ($data = $extra_fields->where('company_client_extra_field_id', $field['id'])->first()) {
                    $data->value = $field['value'] ?? '';
                    $data->save();
                } else {
                    $data = $client->extra_fields()->create([
                        'value' => $field['value'] ?? '',
                        'company_client_extra_field_id' => $field['id'],
                    ]);
                }
            }
        }

        if ($client->isDirty()) {
            $client->save();
        }

        if ($request->has('addressess')) {
            if (count($request->input('addressess', []) ?? []) < $client->addresses->count()) {
                $client_addresses = $client->addresses
                    ->take($client->addresses->count() - count($request->input('addressess', []) ?? []));

                foreach ($client_addresses as $addresses) {
                    $addresses->delete();
                }
            }

            $client = $client->fresh();

            foreach ($request->input('addressess', []) ?? [] as $index => $addressessInput) {
                if (($index + 1) <= $client->addresses->count()) {
                    $client_addresses = $client->addresses[$index];

                    $client_addresses->street_address = array_key_exists('street_address', $addressessInput) ? $addressessInput['street_address'] : '';
                    $client_addresses->zip_code = array_key_exists('zip_code', $addressessInput) ? $addressessInput['zip_code'] : '';
                    $client_addresses->city = array_key_exists('city', $addressessInput) ? $addressessInput['city'] : '';
                    $client_addresses->state = array_key_exists('state', $addressessInput) ? $addressessInput['state'] : '';
                    $client_addresses->country = array_key_exists('country', $addressessInput) ? $addressessInput['country'] : '';
                    $client_addresses->save();
                } else {
                    $client->addresses()->create([
                        'street_address' => array_key_exists('street_address', $addressessInput) ? $addressessInput['street_address'] : '',
                        'zip_code' => array_key_exists('zip_code', $addressessInput) ? $addressessInput['zip_code'] : '',
                        'city' => array_key_exists('city', $addressessInput) ? $addressessInput['city'] : '',
                        'state' => array_key_exists('state', $addressessInput) ? $addressessInput['state'] : '',
                        'country' => array_key_exists('country', $addressessInput) ? $addressessInput['country'] : '',
                    ]);
                }
            }
        }

        return response()->json([
            'message' => __('strings.Client_updated_successfully'),
            'status' => '1',
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function destroy(Client $client)
    {
        $this->authorize('delete', [$client]);

        $client->deleted_at = now();
        $client->save();
        // $client->delete();

        return response()->json([
            'message' => __('strings.client_inactivated_success'),
            'status' => '1',
        ]);
    }

    public function forceDelete(Client $client)
    {
        $this->authorize('delete', [$client]);

        // $client->save();
        $client->delete();

        return response()->json([
            'message' => __('strings.client_deleted_success'),
            'status' => '1',
        ]);
    }

    /**
     * Restore the specified resource from storage.
     *
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function restore(Client $client)
    {
        $this->authorize('delete', [$client]);

        return response()->json([
            'message' => __('strings.client_activated_success'),
            'status' => '1',
        ]);
    }

    public function recordCounts($id)
    {

        $general_notes_unsigned_exists = GeneralNote::where('client_id', $id)->where('signed_at', null)->exists();

        $treatments_unsigned_exists = ClientTreatment::where('client_id', $id)->where('signed_at', null)->exists();

        $letter_of_consents_unsigned_exists = ClientLetterOfConsent::where('client_id', $id)->where('verified_signed_at', null)->exists();

        $prescriptions_unsigned_exists = ClientPrescription::where('client_id', $id)
            ->select('*', DB::raw('(signed_at IS NULL) as is_inactive'))
            ->having('is_inactive', '=', 0)
            ->where('signed_at', null)->exists();

        $bookings = CompanyBooking::where('client_id', $id)->orWhereHas('clients', fn($q) => $q = $q->where('clients.id', $id))->count();
        $procedures = ClientTreatment::where('client_id', $id)->count();
        $letters = ClientLetterOfConsent::where('client_id', $id)->count();
        $general_notes = GeneralNote::where('client_id', $id)->count();
        $prescriptions = ClientPrescription::where('client_id', $id)->select('*', DB::raw('(signed_at IS NULL) as is_inactive'))
            ->having('is_inactive', '=', 0)->count();

        $total = $bookings + $procedures + $letters + $general_notes + $prescriptions;

        return response()->json([
            'data' => [
                "general_notes_unsigned_exists" => $general_notes_unsigned_exists,
                "treatments_unsigned_exists" => $treatments_unsigned_exists,
                "letter_of_consents_unsigned_exists" => $letter_of_consents_unsigned_exists,
                "prescriptions_unsigned_exists" => $prescriptions_unsigned_exists,
                "bookings_count" => $bookings,
                "procedures_count" => $procedures,
                "letters_count" => $letters,
                "general_notes_count" => $general_notes,
                "prescriptions_count" => $prescriptions,
                "total" => $total,
            ],
            'message' => 'Data returned successfully',
            'status' => '1',
        ]);
    }
}
