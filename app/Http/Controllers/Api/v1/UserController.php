<?php

namespace App\Http\Controllers\Api\v1;

use App\User;
use Exception;
use App\Company;
use App\CompanyBooking;
use App\CompanyPlatform;
use App\CompanyService;
use App\Events\PasswordChangeEvent;
use Carbon\Carbon;
use App\Traits\SaveFile;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Http\Requests\v1\LoginUserRequest;
use App\Http\Requests\v1\StoreUserRequest;
use App\Http\Requests\v1\UpdateUserRequest;
use App\Models\UserOauthToken;
use App\Notifications\EmailVerify2fa;
use App\Notifications\PasswordResetRequest;
use App\Traits\ApiResponser;
use App\UserCompany;
use App\UserPasswordHistory;
use Illuminate\Auth\Events\Registered;
use Illuminate\Foundation\Auth\VerifiesEmails;
use Throwable;

class UserController extends Controller
{
    use VerifiesEmails, SaveFile, ApiResponser;

    /**
     * login into user account.
     *
     * @return \Illuminate\Http\Response
     */
    public function login(LoginUserRequest $request)
    {
        return DB::transaction(function () use ($request) {
            $user = User::where('email', $request->email)->firstOrFail();

            if (CompanyPlatform::where('company_id', $user->company_id)->doesntExist()) {
                CompanyPlatform::create([
                    'platform' => CompanyPlatform::RECORD_SYSTEM,
                    'company_id' => $user->company_id
                ]);
            }

            $company = $user->company;

            if ($request->has('platform')) {
                if (CompanyPlatform::where(['platform' => $request->platform, 'company_id' => $company->id])->doesntExist()) {
                    return response()->json([
                        'message' => __('prescription_strings.register_first'),
                        'status' => '0'
                    ]);
                }
            }

            if ($user->is_account_lock) {
                if ($user->account_unlock_at <= Carbon::now()) {
                    $user->is_account_lock = 0;
                    $user->invalid_password_tries = 0;
                    $user->account_unlock_at = null;
                    $user->save();
                } else {
                    $difference = Carbon::parse($user->account_unlock_at)->diffInMinutes(Carbon::now());
                    $message = __('strings.account_lockout', ['difference' => $difference]);
                    if ($difference <= 0) {
                        $difference = Carbon::parse($user->account_unlock_at)->diffInSeconds(Carbon::now());
                        $message = __('strings.account_lockout_sec', ['difference' => $difference]);
                    }
                    return response()->json([
                        'message' => $message,
                        'status' => '0'
                    ]);
                }
            }

            // Changed status for verify account from 0 to 2
            if (!Hash::check($request->password, $user->password)) {
                $invalid_tries_allowed = 3;
                $user->invalid_password_tries = $user->invalid_password_tries + 1;
                $user->save();
                if ($user->invalid_password_tries < $invalid_tries_allowed) {
                    return response()->json([
                        'message' => __('strings.wrong_password_tries_increased', ['invalid_attempts' => $invalid_tries_allowed - $user->invalid_password_tries]),
                        'status' => '3'
                    ]);
                } else {
                    $user->is_account_lock = 1;
                    $user->account_unlock_at = Carbon::now()->addMinutes(1);
                    $user->save();
                    return response()->json([
                        'message' => __('strings.account_lockout', ['difference' => 1]),
                        'status' => '3'
                    ]);
                }
            }

            $user->is_account_lock = 0;
            $user->invalid_password_tries = 0;
            $user->account_unlock_at = null;
            $user->save();

            if ($user->hasRejectedEmail()) {
                return response()->json([
                    'message' => __('strings.user_rejected_message'),
                    'status' => '0'
                ]);
            }

            if (!$user->is_active) {
                return response()->json([
                    'message' => __('strings.user_inactive_message'),
                    'status' => '3'
                ]);
            }

            if ($user->company->is_blocked) {
                return response()->json([
                    'message' => __('strings.auth_blocked'),
                    'status' => '0'
                ]);
            }

            $setting = $user->company->settings()->where('key', 'SHOW_2FA')->where('value', 1)->exists();

            if ($setting) {
                $device = $user->devices()->where('token', $request->device_token)->first();

                if (!$device || ($device && !$device->isVerified() && !$request->has('otp'))) {
                    $device = $user->createDevice();
                    $user->notify(new EmailVerify2fa($device->otp));

                    return response()->json([
                        'data' => [
                            'device_token' => $device->token,
                        ],
                        'message' => __('strings.otp_send_to_your_email'),
                        'status' => '4',
                    ]);
                }

                if (!$device->isVerified() && $request->otp && !$device->isOtpValid($request->otp)) {
                    return response()->json([
                        'message' => __('strings.invalid_otp'),
                        'status' => '0'
                    ]);
                }

                if (!$device->isVerified() && $request->otp && $device->isOtpValid($request->otp)) {
                    $device->setVerified();
                    $device->save();
                }
            }

            if ($user->company->is_cancelled && $user->email != $user->company->email) {
                return response()->json([
                    'message' => __('strings.Your_company_subscription_has_been_cancelled'),
                    'status' => '3',
                ]);
            }

            $user->last_login_at = now();
            $user->save();

            dispatch(function () use ($user) {
                activity()
                    ->performedOn($user)
                    ->by($user)
                    ->log('User has logged in to system.');
            });

            if (Auth::attempt($request->only(['email', 'password']))) {
                $request->session()->regenerate();

                return response()->json([
                    'status' => '1',
                    'message' => $user->company->is_read_only ? __('strings.account_read_only_message') : __('strings.Welcome_to_MERIDIQ')
                ], 200);
            }

            return response()->json([
                'status' => '0',
                'message' => __('strings.Please_provide_valid_email_and_password'),
            ]);
        });
    }


    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $company = $user->company;
        $users = User::where(function ($query) use ($company) {
            $query
                ->where('company_id', $company->id)
                ->orWhereHas('companies', function ($q) use ($company) {
                    $q->where('companies.id', $company->id)->whereNotIn('invite_status', [UserCompany::STOPPED, UserCompany::DELETED]);
                });
        })
            // ->setEagerLoads([])
            ->with(['company_connections' => function ($query) use ($company) {
                $query->where('user_companies.company_id', $company->id);
            }])->withExists(['oauth_tokens' => function ($query) use ($company) {
                $query->where('type', UserOauthToken::CALENDAR);
            }]);

        $isPaginated = false;
        if ($request->has('withTrashed')) {
            $users = $users->withTrashed();
        }

        if (!$request->has('withUnverified')) {
            $users = $users->verifiedEmail();
        }

        // 'all' | 'active' | 'inactive'
        if ($request->has('filter')) {
            if ($request->filter === 'active') {
                $users = $users->where('is_active', 1);
            }
            if ($request->filter === 'inactive') {
                $users = $users->where('is_active', 0);
            }
        }

        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');


            if ($orderBy == 'first_name') {
                if (!$isPaginated) {
                    $users = $users->get();
                    $isPaginated = true;
                }

                $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
                $sortExtra = $orderBy == 'first_name' ? (SORT_NATURAL | SORT_FLAG_CASE) : SORT_FLAG_CASE;
                $users =  $users->sortBy($orderBy, $sortExtra, $isDescOrder);
                // $users =  $users->sort(function ($a, $b) use ($orderBy) {
                //     return ($a->$orderBy == null || $a->$orderBy == "") ? 1 : (($b->$orderBy == null || $b->$orderBy == "") ? -1 : 0);
                // });
            } else {
                $users = $users->orderBy($orderBy, $orderDirection);
            }
        }


        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => 'Users return successfully',
                    'status' => '1'
                ])->merge($isPaginated ? $this->paginate($users) : $users->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $users->get(),
                'message' => 'Users return successfully',
                'status' => '1'
            ]);
        }
    }


    /**
     * Display a specific of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function single(Request $request, User $user)
    {
        $this->authorize('view', $user);

        return response()->json([
            'data' => $user,
            'message' => 'User return successfully',
            'status' => '1'
        ]);
    }

    public function isUserAvailable(User $user, Request $request)
    {
        $service = CompanyService::findOrFail($request->service_id);
        if ($request->has('date_time')) {
            try {
                CompanyBooking::isBookingAvailable(
                    $user,
                    Carbon::parse($request->date_time),
                    Carbon::parse($request->date_time)->addMinutes($service->duration ?? 0)->addMinutes($service->time_margin ?? 0),
                    $service
                );
            } catch (Throwable $th) {
                return response()->json([
                    'message' => $th->getMessage(),
                    'status' => '0',
                ]);
            }
        }
        return response()->json([
            'message' => 'User is available for selected date-time',
            'status' => '1'
        ]);
    }
    public function makeSuperAdmin(User $user)
    {
        $login_user = Auth::user();
        $this->authorize('makeSuperAdmin', $user);

        DB::transaction(function () use ($login_user, $user) {
            $company = $login_user->company;
            $company->email = $user->email;
            $company->save();
            if ($company->hasStripeId()) {
                $company->updateStripeCustomer([
                    'email' => $user->email,
                ]);
            }
        });


        return response()->json([
            'message' => __('strings.roles_changed'),
            'status' => '1'
        ]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function publicIndex($id)
    {
        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }

        $company = Company::findOrFail($id);

        $users = $company->users()->select(['users.first_name', 'users.last_name', 'users.id', 'users.company_id'])
            ->setEagerLoads([])
            ->with(['company' => function ($query) {
                $query->select('id', 'company_name', 'profile_photo', 'unit');
            }]);

        $users = $users->verifiedEmail()->get();

        $users = $users->map(function ($user) {
            $user->company->setAppends([
                'encrypted_id',
            ]);

            return $user;
        });

        $users = $users->values();

        return response()->json(
            collect(
                [
                    'message' => 'Users return successfully',
                    'status' => '1'
                ]
            )->merge(['data' => $users])
        );
    }

    public function bookingIndex($id)
    {
        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }
        $company = Company::findOrFail($id);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(StoreUserRequest $request)
    {
        $this->authorize('create', [User::class, $request->input('is_record_on', false)]);

        $file  = null;
        $profile_photo = null;

        if ($request->hasFile('profile_photo')) {
            $file = $this->saveFile($request->file('profile_photo'), 'user_photo', null, true);

            $profile_photo = $file->filename;
        }

        $user = User::create([
            "email" => $request->input('email'),
            "title" => $request->input('title'),
            "first_name" => $request->input('first_name'),
            "last_name" => $request->input('last_name'),
            "profile_photo" => $profile_photo,
            "password" => bcrypt($request->input('password')),
            "company_id" => Auth::user()->company_id,
            "street_address" => $request->input('street_address', ''),
            "zip_code" => $request->input('zip_code', ''),
            "city" => $request->input('city', ''),
            "state" => $request->input('state', ''),
            "country" => $request->input('country', ''),
            "mobile_number" => $request->input('mobile_number', ''),
            "is_for_booking" => $request->input('is_for_booking'),
            "country_code" => $request->input('country_code', ''),
            "is_booking_on" => $request->input('is_booking_on', false),
            "is_record_on" => $request->input('is_record_on', false),
            "is_pos_on" => $request->input('is_pos_on', false),
            "user_role" => $request->input('user_role'),
        ]);

        if ($file) {
            $user->file()->save($file);
        }

        foreach ($user->company->clients as $index => $client) {
            $client->accesses()->toggle([$user->id]);
        }

        event(new Registered($user));

        return response()->json([
            'message' => __('strings.User_created_successfully'),
            'status' => '1'
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show()
    {
        $user = Auth::user();

        $user->company->loadMissing(['platforms']);

        $user->company->append([
            'is_subscribed',
            'is_cancelled',
            'active_subscriptions',
            'record_plan',
            'management_plan',
            'pos_plan',
            'booking_plan',
            'has_system',
        ]);

        if ($user->company->is_read_only) {
            return response()->json([
                'data' => $user,
                'meta' => [
                    'message' => __('strings.account_read_only_message', ['message' => "<a href='mailto:<EMAIL>'><EMAIL></a>"]),
                ],
                'message' => 'User returned successfully.',
                'status' => '1'
            ]);
        }

        return response()->json([
            'data' => $user,
            'message' => 'User returned successfully.',
            'status' => '1'
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(User $user)
    {
        return DB::transaction(function () use ($user) {
            $request = app(UpdateUserRequest::class);

            $this->authorize('update', [User::class, $user, $request]);

            $company = $user->company;

            $authUser = Auth::user();

            if ($request->has('profile_photo')) {
                $file = $this->saveFile($request->file('profile_photo'), 'user_photo', null, true);

                if ($user->file) {
                    $user->file->delete();
                }

                $user->file()->save($file);

                $user->profile_photo = $file->filename;
            }

            if ($request->has('user_role')) {
                if (Auth::user()->user_role == User::ADMIN) {
                    if ($user->email != $user->company->email) {
                        $user->user_role = $request->input('user_role');
                    }
                }
            }

            if ($request->input('title')) {
                $user->title = $request->input('title');
            }

            if ($request->input('first_name')) {
                $user->first_name = $request->input('first_name');
            }

            if ($request->input('last_name')) {
                $user->last_name = $request->input('last_name');
            }

            if ($request->input('mobile_number')) {
                $user->mobile_number = $request->input('mobile_number', '');
            }
            if ($request->input('country_code')) {
                $user->country_code = $request->input('country_code', '');
            }

            if ($request->input('street_address')) {
                $user->street_address = $request->input('street_address');
            }

            if ($request->input('zip_code')) {
                $user->zip_code = $request->input('zip_code');
            }

            if ($request->input('city')) {
                $user->city = $request->input('city');
            }

            if ($request->input('state')) {
                $user->state = $request->input('state');
            }

            if ($request->input('country')) {
                $user->country = $request->input('country');
            }

            if ($request->input('booking_description')) {
                $user->booking_description = $request->booking_description;
            }

            if ($request->input('user_count')) {
                $company->user_count = $request->user_count;
            }

            if ($request->input('personal_id')) {
                $user->personal_id = $request->input('personal_id');
            }

            if ($request->has('resend_email_verification') && $user->hasRejectedEmail()) {
                $user->email_verified_at = null;
                $user->save();

                $user->sendEmailVerificationNotification();

                return response()->json([
                    'data' => $user->refresh(),
                    'message' => __('strings.mail_send_to_client'),
                    'status' => '1'
                ]);
            }

            if ($request->has('email')) {
                if ($request->email != $user->email) {
                    $user->email_verified_at = null;
                }

                if ($user->company->email == $user->email) {
                    $company = $user->company;
                    $user->email = $request->input('email');
                    $user->save();
                    $company->email = $request->input('email');
                    $company->save();
                } else {
                    $user->email = $request->input('email');
                }

                if (!$user->email_verified_at) {
                    $user->sendEmailVerificationNotification();
                }

                $user->email = $request->input('email');
            }

            if ($request->has('is_booking_on')) {
                $user->is_booking_on = $request->input('is_booking_on');
            }

            if ($request->has('is_record_on')) {
                $user->is_record_on = $request->input('is_record_on');
            }

            if ($request->has('is_pos_on')) {
                $user->is_pos_on = $request->input('is_pos_on');
            }

            if ($request->has('password')) {
                if (!Hash::check($request->input('old_password'), $user->password) && Auth::user()->user_role != User::MASTER_ADMIN) {
                    return response()->json([
                        'message' => __('strings.Old_password_did_not_match'),
                        'status' => '0'
                    ]);
                }

                if ($request->old_password == $request->password) {
                    return response()->json([
                        'message' => __('strings.old_password_and_new_password_same'),
                        'status' => '0'
                    ]);
                }

                $user_password_history = UserPasswordHistory::where('user_id', $user->id)->get();

                foreach ($user_password_history as $user_password) {
                    if (password_verify($request->input('password'), $user_password->password)) {
                        return response()->json([
                            'message' => __('strings.please_user_different_password'),
                            'status' => '0'
                        ]);
                    }
                }

                event(new PasswordChangeEvent($user, $user['password']));

                $user->password = bcrypt($request->input('password'));
                $user->is_account_lock = 0;
                $user->invalid_password_tries = 0;
                $user->account_unlock_at = null;

                try {
                    $user->tokens()->delete();
                } catch (\Throwable $th) {
                }
            }

            if ($request->has('firebase_token') && $request->firebase_token) {
                $user['firebase_tokens'] = $user->firebase_tokens ? collect($user->firebase_tokens)->push($request->firebase_token)->unique()->values()->all() : [$request->firebase_token];
            }

            if ($user->isDirty()) {
                $user->save();
            }

            if ($company->isDirty()) {
                $company->save();
            }

            $authUser = $authUser->refresh();

            if (!$user->email_verified_at && $user->email == $authUser->email) {
                Auth::guard('web')->logout();

                $request->session()->invalidate();

                $request->session()->regenerateToken();
            }

            return response()->json([
                'data' => $user->refresh(),
                'message' => __('strings.User_updated_successfully'),
                'status' => '1'
            ]);
        });
    }

    public function forgotPassword(Request $request)
    {
        $request->validate(['email' => 'required']);
        $email = $request->input('email');

        $user = User::where('email', $email)->first();
        if (!$user) {
            return response()->json([
                'message' => __('strings.No_user_found_with_this_specific_user'),
                'status' => '0'
            ]);
        }

        $new_pass = Str::random(11);
        $for_int = strpbrk($new_pass, '1234567890');

        while (!$for_int) {
            $new_pass = Str::random(11);
            $for_int = strpbrk($new_pass, '1234567890');
        }

        $user->password = bcrypt($new_pass);
        $user->save();
        if ($user) {
            try {
                $user->notify(new PasswordResetRequest($new_pass));
            } catch (\Throwable $th) {
                return response()->json([
                    'error' => $th->getMessage(),
                    'message' => __('strings.something_wrong'),
                    'status' => '1'
                ]);
            }
        }
        return response()->json([
            'message' => __('strings.password_reset_has_been_send_to_your_email'),
            'status' => '1'
        ]);
    }

    public function delete(User $user)
    {
        $this->authorize('delete', [User::class, $user]);
        if ($user->delete()) {
            return response()->json([
                'message' => __('strings.user_removed_successfully'),
                'status' => '1'
            ]);
        } else {
            return response()->json([
                'message' => __('strings.user_removal_failed'),
                'status' => '0'
            ]);
        }
    }

    public function changeStatus(User $user)
    {
        if ($user->is_active) {
            $this->authorize('delete', [User::class, $user]);
        } else {
            $this->authorize('create', [User::class]);
        }

        $user->is_active = !$user->is_active;
        $user->save();

        return response()->json([
            'message' => __('strings.user_removed_successfully'),
            'status' => '1'
        ]);
    }

    public function restore($id)
    {
        $user = User::withTrashed()->findOrFail($id);
        $this->authorize('restore', [User::class, $user]);

        if ($user->restore()) {
            return response()->json([
                'message' => __('strings.user_restored_successfully'),
                'status' => '1'
            ]);
        } else {
            return response()->json([
                'message' => __('strings.user_restoration_failed'),
                'status' => '0'
            ]);
        }
    }

    public function getTimeZones()
    {
        return response()->json([
            'data' => config('timezones') ?? [],
            'message' => __('strings.timezone_returned'),
            'status' => '1'
        ]);
    }
}
