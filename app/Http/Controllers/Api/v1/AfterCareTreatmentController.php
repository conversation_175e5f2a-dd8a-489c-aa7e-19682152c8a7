<?php

namespace App\Http\Controllers\Api\v1;

use App\Activity;
use App\AestheticInterest;
use App\AfterCareData;
use App\Client;
use App\ClientAfterCare;
use App\ClientLetterOfConsent;
use App\Company;
use App\Contracts\Services\PDF\PDFServiceInterface;
use App\Contracts\Services\VideoCall\VideoCallServiceInterface;
use App\Covid19;
use App\HealthQuestionary;
use App\Traits\SaveFile;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use App\Http\Requests\v1\StoreAfterCareTreatmentRequest;
use App\Http\Requests\v2\CustomerDataRequest;
use App\LetterOfConsent;
use App\Mail\AfterCareMail;
use App\Questionary;
use App\QuestionaryData;
use App\QuestionaryQuestion;
use App\Setting;
use App\Traits\GeneratePDF;
use Exception;
use Illuminate\Support\Facades\Crypt;
use App\Contracts\Services\ZIP\ZipServiceInterface;

class AfterCareTreatmentController extends Controller
{
    use SaveFile, GeneratePDF;

    public function index($id, $after_care_id)
    {

        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }

        $client_after_care = null;
        try {
            if (is_numeric($after_care_id)) {
                $client_after_care = ClientAfterCare::with(['aftercare.aftercarable'])->findorFail($after_care_id);
            } else {
                $client_after_care = ClientAfterCare::with(['aftercare.aftercarable'])->findorFail(Crypt::decrypt($after_care_id));
            }
        } catch (\Throwable $th) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }


        $letter_of_consents = [];
        $questionnaries = [];

        foreach ($client_after_care->aftercare as $aftercare) {
            if ($aftercare->aftercarable_type == LetterOfConsent::class) {
                array_push($letter_of_consents, $aftercare);
            } else {
                if ($aftercare->aftercarable_type == Questionary::class) {
                    $aftercare->aftercarable->loadMissing('questions');
                }
                array_push($questionnaries, $aftercare);
            }
        }
        $client_after_care['letter_of_consents'] = $letter_of_consents;
        $client_after_care['questionnaries'] = $questionnaries;
        unset($client_after_care->aftercare);

        return response()->json([
            'data' => $client_after_care,
            'message' => __('strings.company_information'),
            'status' => '1'
        ]);
    }

    public function store(StoreAfterCareTreatmentRequest $request, Client $client, VideoCallServiceInterface $videoCallService)
    {
        $this->authorize('update', [Client::class, $client]);
        $file = null;
        $videoCallMemberOwner = null;
        $videoCallMemberClient = null;
        $videoCall = null;

        if ($request->boolean('video_call')) {
            $videoCall = $videoCallService->single(Auth::user(), $client);

            $videoCallMemberOwner = $videoCall->owner_member;
            $videoCallMemberClient = $videoCall->other_members()->first();
        }

        if ($request->has('file')) {
            $file = $this->saveFile($request->file('file'), 'after_care_treatment', $client->company->users()->first());
        }

        $client_after_care  =  ClientAfterCare::create([
            'client_id' => $client->id,
            'notes' => $request->notes,
            'file_path' => $file?->filename,
        ]);

        $activity_message = "Email has been sent to $client->email with the following information: ";
        $activity_data = collect();

        if ($request->file('file')) {
            $activity_data->push($request->file('file')->getClientOriginalName());
        }

        if ($request->has('loc_ids')) {
            foreach ($request->loc_ids as $loc_id) {
                AfterCareData::create([
                    'client_after_care_id' => $client_after_care->id,
                    'aftercarable_id' => $loc_id,
                    'aftercarable_type' => LetterOfConsent::class,
                ]);
                $loc = LetterOfConsent::find($loc_id);
                $activity_data->push($loc->consent_title);
            }
        }

        if ($request->has('questionnairies')) {
            foreach ($request->questionnairies as $questionary) {
                if (isset($questionary['id'])) {
                    AfterCareData::create([
                        'client_after_care_id' => $client_after_care->id,
                        'aftercarable_id' => $questionary['id'],
                        'aftercarable_type' => Questionary::class,
                    ]);
                    $ques = Questionary::find($questionary['id']);
                    $activity_data->push($ques->title);
                } else {
                    AfterCareData::create([
                        'client_after_care_id' => $client_after_care->id,
                        'aftercarable_id' => null,
                        'aftercarable_type' => $questionary['type'],
                    ]);
                    if ($questionary['type'] === 'App\AestheticInterest') {
                        $activity_data->push("Aesthetic Interest");
                    }
                    if ($questionary['type'] === 'App\HealthQuestionary') {
                        $activity_data->push("Health Questionnaire");
                    }
                    if ($questionary['type'] === 'App\Covid19') {
                        $activity_data->push("Covid-19 Questionnaire");
                    }
                }
            }
        }

        $activity_message .= $activity_data->join(', ');

        $activity = activity('aftercare_treatment_mail')
            ->performedOn($client);

        $activity = $activity->by(Auth::user());

        $language = Setting::getSetting($client->company, Setting::CUSTOMER_LANGUAGE)?->value;
        Mail::to($client->email)->locale($language ?? app()->getLocale())->send(new AfterCareMail($client, Auth::user(), $client_after_care, $videoCallMemberClient?->url, $file ? $file : null, $language));

        $activity = $activity->log($activity_message);

        if ($videoCall) {
            $videoCall->callable()->associate($activity);
            $videoCall->save();
        }

        return response()->json([
            'data' => [
                'client_after_care' => $client_after_care,
                'video_call' => $videoCall,
            ],
            'message' => __('strings.after_care_treatment_send_successfully', ['email' => $client->email]),
            'status' => '1'
        ]);
    }

    public function customerData(CustomerDataRequest $request, $id, $after_care_id, PDFServiceInterface $pdfService, ZipServiceInterface $zipService)
    {

        try {
            $id = Company::decryptId($id);
        } catch (Exception $e) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }

        $client_after_care = null;
        try {
            if (is_numeric($after_care_id)) {
                $client_after_care = ClientAfterCare::with(['aftercare.aftercarable'])->findorFail($after_care_id);
            } else {
                $client_after_care = ClientAfterCare::with(['aftercare.aftercarable'])->findorFail(Crypt::decrypt($after_care_id));
            }
        } catch (\Throwable $th) {
            return response()->json([
                'message' => __('strings.Please_provide_valid_url'),
                'status' => '0',
            ]);
        }
        $client = $client_after_care->client;
        $user = $client->company->users()->first();


        $created_at = now();
        if ($request->has('aesthetic_interest')) {
            $inputs = $request->all();
            if ($request->hasFile('aesthetic_interest.5.image')) {
                $file = $this->saveFile($request->file('aesthetic_interest.5.image'), 'aesthetic_interest', $user);
                $inputs['aesthetic_interest'][5]['image'] = $file->url;
            }

            $aestheticInterest = AestheticInterest::updateOrCreate([
                'client_id' => $client->id,
                'data_new' => json_encode(['aesthetic_interest' => $inputs['aesthetic_interest']]),
            ]);

            $data = ['datas' => $inputs['aesthetic_interest'], 'client' => $client, 'created_at' => $created_at];
            // $file = $this->generateStoreQuestionary($data, 'exports.client_aesthethic_interest_questionary', 'clients/' . md5($client->id) . '/aesthetic_interest/pdf', $user);
            $filename = $this->generateFilePath('clients/' . md5($client->id) . '/aesthetic_interest/pdf', $user, null, 'pdf');
            $file = $pdfService->client($client)->questionary()->aesthethicInterest($inputs['aesthetic_interest'], $created_at)->saveFile($user, $filename);

            QuestionaryData::updateOrcreate([
                'client_id' => $client->id,
                'modelable_type' => AestheticInterest::class,
                'modelable_id' => $aestheticInterest->id,
            ], [
                'pdf' => $file->filename,
                'response' => collect($data)->values(),
            ]);

            if ($request->hasFile('aesthetic_interest.5.image')) {
                $aestheticInterest->file()->save($file);
            }
        }

        if ($request->has('health_questions')) {
            $healthQuestionary = HealthQuestionary::updateOrCreate([
                'client_id' => $client->id,
                'data_new' => json_encode($request->input('health_questions')),
            ]);

            $data = ['datas' => $request->input('health_questions'), 'client' => $client, 'created_at' => $created_at];

            // $file = $this->generateStoreQuestionary($data, 'exports.client_health_questionary', 'clients/' . md5($client->id) . '/health_questionary/pdf', $user);
            $filename = $this->generateFilePath('clients/' . md5($client->id) . '/health_questionary/pdf', $user, null, 'pdf');
            $file = $pdfService->client($client)->questionary()->health($request->input('health_questions'), $created_at)->saveFile($user, $filename);

            QuestionaryData::updateOrcreate([
                'client_id' => $client->id,
                'modelable_type' => HealthQuestionary::class,
                'modelable_id' => $healthQuestionary->id,
            ], [
                'pdf' => $file->filename,
                'response' => collect($data)->values(),
            ]);
        }

        if ($request->has('covid19')) {
            $covid19 = Covid19::updateOrCreate([
                'client_id' => $client->id,
                'data' => json_encode($request->input('covid19')),
            ]);

            $data = ['datas' => $request->input('covid19'), 'client' => $client, 'created_at' => $created_at];
            // $file = $this->generateStoreQuestionary($data, 'exports.client_covid19_questionary', 'clients/' . md5($client->id) . '/covid19_questionary/pdf', $user);
            $filename = $this->generateFilePath('clients/' . md5($client->id) . '/covid19_questionary/pdf', $user, null, 'pdf');
            $file = $pdfService->client($client)->questionary()->covid19($request->input('covid19'), $created_at)->saveFile($user, $filename);

            $questionaryData = QuestionaryData::updateOrcreate([
                'client_id' => $client->id,
                'modelable_type' => Covid19::class,
                'modelable_id' => $covid19->id,
            ], [
                'pdf' => $file->filename,
                'response' => collect($data)->values(),
            ]);
        }

        if ($request->has('letter_of_consents')) {
            foreach ($request->letter_of_consents as $index => $letter_of_consent) {
                $signature = null;
                $signature_name = null;
                $signed_file = null;
                $signed_file_name = null;
                $consent_id = null;
                $is_bad_allergic_shock = 'no';
                $is_publish_before_after_pictures = null;
                if (isset($letter_of_consent['consent_id'])) {
                    $consent_id = $letter_of_consent['consent_id'];
                };
                if (isset($letter_of_consent['is_bad_allergic_shock'])) {
                    $is_bad_allergic_shock = $letter_of_consent['is_bad_allergic_shock'];
                };
                if (isset($letter_of_consent['is_publish_before_after_pictures'])) {
                    $is_publish_before_after_pictures = $letter_of_consent['is_publish_before_after_pictures'];
                };

                $consent = LetterOfConsent::findOrFail($consent_id);
                if (isset($letter_of_consent['signature'])) {
                    $signature = $this->saveFile($letter_of_consent['signature'], 'clients/' . md5($client->id) . '/letter_of_consents', $user);
                    $signature_name = $signature->filename;
                }
                if (isset($letter_of_consent['signed_file'])) {
                    $signed_file = $this->saveFile($letter_of_consent['signed_file'], 'clients/' . md5($client->id) . '/letter_of_consents', $user);
                    $signed_file_name = $signed_file->filename;
                }

                $clientLetterOfConsent = ClientLetterOfConsent::create([
                    'signed_file' => $signed_file_name,
                    'client_id' => $client_after_care->client_id,
                    'consent_id' => $consent_id,
                    'is_bad_allergic_shock' => $is_bad_allergic_shock,
                    'signature' => $signature_name,
                    'consent_title' => $consent->consent_title ?? '',
                    'letter' => $consent->letter_html ?? $consent->letter ?? '',
                    'is_publish_before_after_pictures' => $is_publish_before_after_pictures,
                    'version' => $consent->version
                ]);
            }
        }

        if ($request->has('questionary')) {

            $questionaries = [];

            foreach ($client_after_care->aftercare as $aftercare) {
                if ($aftercare->aftercarable_type == Questionary::class) {
                    array_push($questionaries, Questionary::with('questions')->findOrFail($aftercare->aftercarable_id));
                }
            }
            foreach ($questionaries as $questionaryIndex => $questionary) {
                $data = $request->input("questionary.$questionaryIndex.data");

                $questions = [];
                $questionaryData = $questionary->datas()->create([
                    'client_id' => $client->id,
                    'pdf' => "",
                    'response' => ""
                ]);
                $zip_data = collect();

                foreach ($questionary->questions as $index => $question) {
                    array_push($questions, (object) ['question' => $question->question, 'type' => $question->type]);

                    if ($question->type == QuestionaryQuestion::IMAGE) {
                        $file = $this->saveFile($request->file("questionary.$questionaryIndex.data.$index"), 'questionary/uploads', $user);
                        $data[$index] = $file->filename;
                    }

                    if ($question->type == QuestionaryQuestion::FILE_UPLOAD) {
                        $uploaded_files = $request->file("questionary.$questionaryIndex.data.$index");
                        if ($uploaded_files) {
                            $files = [];
                            foreach ($uploaded_files['files'] as $i => $uploaded_file) {
                                $file = $this->saveFile($uploaded_file, "questionary_files", $user);
                                $questionaryData->files()->save($file);
                                array_push($files, (object) ['file_id' => $file->id, 'file_name' => $request->input("questionary.$questionaryIndex.data.$index.file_names.$i") ?? '']);

                                $zip_data->push([
                                    'path' => $file->filename,
                                    'zip_path' => "Files/Q" . ($index + 1) . "/" . $request->input("questionary.$questionaryIndex.data.$index.file_names.$i") ?? '',
                                    "delete" => false,
                                ]);
                            }
                            $data[$index] = (object) ['files' => $files];
                        }
                    }
                }

                // $fileData = ['questionary' => $questionary, 'data' => $data, 'client' => $client, 'created_at' => $created_at];
                // $file = $this->generateStoreQuestionary($fileData, 'exports.client_questionaries', 'clients/' . md5($client->id) . '/questionary/pdf', $user);
                $filename = $this->generateFilePath('clients/' . md5($client->id) . '/questionary/pdf', $user, null, 'pdf');
                $file = $pdfService->client($client)->questionary()->custom($questionary, $data, $created_at)->saveFile($user, $filename);

                if ($zip_data->count()) {
                    $zip_path = $this->generateFilePath('clients/' . md5($client->id) . '/questionary/pdf', $user, null, "zip");

                    $zip_data->push([
                        'path' => $file->filename,
                        'zip_path' => "{$questionary->title}.pdf",
                        "delete" => false,
                    ]);

                    // Generate and store zip
                    $file = $zipService->make($zip_data)->saveFile($user, $zip_path);
                }
                $questionaryData->pdf = $file->filename;
                $questionaryData->response = collect($data)->values();
                $questionaryData->questions = collect($questions)->values();
                $questionaryData->save();
            }
        }

        $client_after_care->is_filled = 1;
        $client_after_care->save();
        return response()->json([
            'message' => __('strings.client_extra_data_successfully'),
            'status' => '1'
        ]);
    }

    public static function generatePDF(Client $client, $is_twelve_hours)
    {
        $activities = Activity::where(function ($query) use ($client) {
            $query = $query->where(function ($query) use ($client) {
                $query->where('subject_type', Client::class)->where('subject_id', $client->getKey());
            })->inLog('aftercare_treatment_mail');
        })->get();

        return self::downloadFromView('exports.client.send_info', [
            'client' => $client,
            'logs' => $activities,
            'company' => $client->company,
            'is_twelve_hours' => $is_twelve_hours,
        ]);
    }
}
