<?php

namespace App\Http\Controllers\Api\v1;

use App\Client;
use App\File;
use App\GeneralNote;
use App\Http\Controllers\Controller;
use App\Http\Requests\v1\GeneralNoteDeleteFileRequest;
use App\Http\Requests\v1\GeneralNoteIndexRequest;
use App\Http\Requests\v1\GeneralNoteStoreRequest;
use App\Http\Requests\v1\GeneralNoteUpdateRequest;
use App\Traits\ApiResponser;
use App\Traits\SaveFile;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;


class GeneralNoteController extends Controller
{
    use SaveFile;
    use ApiResponser;
    /**
     * Display a listing of the resource.
     *
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function index(GeneralNoteIndexRequest $request, Client $client)
    {
        $isPaginated = false;

        $generalNotes = $client->general_notes()->with(['files', 'cancelled_by', 'prescriptions' => fn($q) => $q->plucked()]);

        if ($request->has('filter')) {
            $generalNotes = $generalNotes->where($request->filter, $request->input('filter_type', '='), $request->input('filter_value', null));
        }

        if ($request->has('search')) {
            if (!$isPaginated) {
                $generalNotes = $generalNotes->get();
                $isPaginated = true;
            }

            $search = $request->search;
            $generalNotes = $generalNotes->filter(function ($value) use ($search) {
                return Str::contains(Str::lower($value->title), Str::lower($search));
            });
        }

        if ($request->has('orderBy')) {
            $orderBy = $request->orderBy;
            $orderDirection = $request->input('orderDirection', 'asc');

            if (!$isPaginated) {
                $generalNotes = $generalNotes->get();
                $isPaginated = true;
            }

            $isDescOrder = !in_array($orderDirection, ['asc', 'ASC']);
            $generalNotes = $generalNotes->sortBy($orderBy, SORT_NATURAL | SORT_FLAG_CASE, $isDescOrder);
            // $generalNotes =  $generalNotes->sort(function ($a, $b) use ($orderBy) {
            //     return ($a->$orderBy == null || $a->$orderBy == "") ? 1 : (($b->$orderBy == null || $b->$orderBy == "") ? -1 : 0);
            // });
        }
        if ($request->input('page')) {
            return response()->json(
                collect([
                    'message' => 'general notes return successfully',
                    'status' => '1',
                ])->merge($isPaginated ? $this->paginate($generalNotes) : $generalNotes->paginate($request->input('per_page')))
            );
        } else {
            return response()->json([
                'data' => $isPaginated ? $generalNotes : $generalNotes->get(),
                'message' => 'general notes return successfully',
                'status' => '1',
            ]);
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Client  $client
     * @return \Illuminate\Http\Response
     */
    public function store(GeneralNoteStoreRequest $request, Client $client)
    {
        $this->authorize('create', [GeneralNote::class, $client]);

        if ($request->has('file')) {
            return response()->json([
                'message' => 'Please update the app or website',
                'status' => '0',
            ]);
        }

        $generalNote = DB::transaction(function () use ($client, $request) {
            $filenames = [];
            if ($request->has('files')) {
                foreach ($request->files as $index => $file) {
                    foreach ($file as $f) {
                        array_push($filenames, $f->getClientOriginalName());
                    }
                }
            }

            $generalNote = $client->general_notes()->create([
                'title' => $request->title,
                'notes' => $request->notes,
                'notes_html' => $request->notes_html,
                'filename' => $filename ?? null,
                'filenames' => count($filenames) > 0 ? array_values($filenames) : null,
            ]);

            if ($request->is_custom_date_selected == 1) {
                $date = $request->date;
                $time = $request->time;
                $created_at = Carbon::parse("$date $time");
                $generalNote->created_at = $created_at;
                $generalNote->save();
            }

            if ($request->has('files')) {
                foreach ($request->files as $file) {
                    foreach ($file as $f) {
                        $name = $this->saveFile($f, 'clients/' . md5($generalNote->client_id) . '/general_note/media');
                        $generalNote->file()->save($name);
                    }
                }
            }

            if ($request->has('prescriptions')) {
                $generalNote->prescriptions()->sync($request->prescriptions);
            }

            return $generalNote;
        });

        return response()->json([
            'message' => __('strings.general_notes_created_success'),
            'status' => '1',
            'data' => $generalNote->loadMissing('files'),
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Client  $client
     * @param  \App\GeneralNote  $generalNote
     * @return \Illuminate\Http\Response
     */
    public function show(Client $client, GeneralNote $generalNote)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\GeneralNote  $generalNote
     * @return \Illuminate\Http\Response
     */
    public function update(GeneralNoteUpdateRequest $request, GeneralNote $generalNote)
    {
        $this->authorize('update', [GeneralNote::class, $generalNote]);

        if ($request->has('file')) {
            return response()->json([
                'message' => 'Please update the app or website',
                'status' => '0',
            ]);
        }

        // if ($request->has('files')) {
        //     $filenames = $generalNote->filenames ?? [];
        //     if ($generalNote->filename) {
        //         array_push($filenames, $generalNote->filename);
        //         $generalNote->filename = null;
        //     }
        //     foreach ($request->files as $index => $file) {
        //         foreach ($file as $f) {
        //             return $f->getClientOriginalName();
        //             array_push($filenames, $f->getClientOriginalName());
        //             $f = $this->saveFile($f, 'clients/' . md5($generalNote->client_id) . '/general_note/media');
        //             $generalNote->file()->save($f);
        //         }
        //     }
        //     $generalNote->filenames = array_values($filenames);
        // }

        $generalNote = DB::transaction(function () use ($request, $generalNote) {
            if ($request->has('title')) {
                $generalNote->title = $request->title;
            }

            if ($request->has('notes')) {
                $generalNote->notes = $request->notes;
            }

            if ($request->has('notes_html')) {
                $generalNote->notes_html = $request->notes_html;
            }

            if ($request->has('files')) {
                $filenames = $generalNote->filenames ?? [];
                if ($generalNote->filename) {
                    array_push($filenames, $generalNote->filename);
                    $generalNote->filename = null;
                }
                foreach ($request->files as $index => $file) {
                    foreach ($file as $f) {
                        array_push($filenames, $f->getClientOriginalName());
                        $f = $this->saveFile($f, 'clients/' . md5($generalNote->client_id) . '/general_note/media');
                        $generalNote->file()->save($f);
                    }
                }
                $generalNote->filenames = array_values($filenames);
            }

            if ($request->hasFile('sign')) {
                $file = $this->saveFile($request->file('sign'), 'clients/' . md5($generalNote->client_id) . '/general_note/sign');
                $generalNote->sign = $file->filename;
                $generalNote->sign_by_id = Auth::id();
                $generalNote->signed_at = now();
                $generalNote->timestamps = false;

                $activity = activity()
                    ->performedOn($generalNote);

                $activity = $activity->by(Auth::user());
                $activity->log("{$generalNote->client->first_name} {$generalNote->client->last_name} client 's general note (:subject.title) had be signed by :causer.first_name :causer.last_name");
            }

            if (!$request->hasFile('sign')) {
                if ($request->has('prescriptions')) {
                    $generalNote->prescriptions()->sync($request->input('prescriptions', []));
                } else {
                    $generalNote->prescriptions()->detach();
                }
            }
            if ($request->has('is_cancelled') && $request->is_cancelled) {
                $generalNote->is_cancelled = $request->is_cancelled;
                $generalNote->cancel_note = $request->cancel_note;
                $generalNote->cancelled_by_id = Auth::id();
                $generalNote->cancelled_at = Carbon::now();


                $activity = activity()
                    ->performedOn($generalNote);

                $activity = $activity->by(Auth::user());
                $activity->log("{$generalNote->client->first_name} {$generalNote->client->last_name} client 's general note (:subject.title) had be cancelled by :causer.first_name :causer.last_name");
            }
            if ($generalNote->isDirty()) {
                $generalNote->save();
            }
            return $generalNote;
        });

        return response()->json([
            'data' => $generalNote->loadMissing(['files', 'prescriptions' => fn($q) => $q->plucked()]),
            'message' => __('strings.general_notes_updated_success'),
            'status' => '1',
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Client  $client
     * @param  \App\GeneralNote  $generalNote
     * @return \Illuminate\Http\Response
     */
    public function destroy(GeneralNote $generalNote)
    {
        $this->authorize('delete', [GeneralNote::class, $generalNote]);

        $generalNote->delete();
        return response()->json([
            'message' => __('strings.general_notes_deleted_success'),
            'status' => '1',
        ]);
    }

    public function destroyFile(GeneralNote $generalNote, File $file, GeneralNoteDeleteFileRequest $request)
    {
        $this->authorize('delete', [GeneralNote::class, $generalNote]);
        if ($generalNote->filenames) {
            $names = $generalNote->filenames;
            unset($names[$request->index]);
            $generalNote->filenames = array_values($names);
            $generalNote->save();
        }
        if ($generalNote->filename) {
            $generalNote->filename = null;
            $generalNote->save();
        }
        $file = $generalNote->files()->where('files.id', $file->id)->firstOrFail();
        $file->delete();
        $generalNote->refresh();

        return response()->json([
            'data' => $generalNote->loadMissing('files'),
            'message' => 'media file deleted successfully.',
            'status' => '1',
        ]);
    }
}
