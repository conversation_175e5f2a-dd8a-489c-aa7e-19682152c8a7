<?php

namespace App\Http\Controllers\Api\v1;

use App\Client;
use App\Contracts\Services\PDF\PDFServiceInterface;
use App\Contracts\Services\ZIP\ZipServiceInterface;
use App\Questionary;
use App\QuestionaryData;
use App\Traits\SaveFile;
use App\QuestionaryQuestion;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Crypt;
use VerumConsilium\Browsershot\Facades\PDF;
use App\Http\Requests\v1\StoreQuestionaryDataRequest;
use Carbon\Carbon;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Auth;

class QuestionaryDataController extends Controller
{
    use SaveFile;
    /**
     * Display a listing of the resource.
     *
     * @param  \App\Questionary  $questionary
     * @return \Illuminate\Http\Response
     */
    public function index(Questionary $questionary)
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Questionary  $questionary
     * @return \Illuminate\Http\Response
     */
    public function store(StoreQuestionaryDataRequest $request, Questionary $questionary, PDFServiceInterface $pdfService, ZipServiceInterface $zipService)
    {
        $client = Client::findOrFail($request->client_id);

        $this->authorize("create", [QuestionaryData::class, $questionary, $client]);

        $user = Auth::user();
        $msg = "{$client->first_name} {$client->last_name}'s questionary ({$questionary->title}) has been created by {$user->first_name} {$user->last_name}";
        activity()->performedOn($client)->by($user)->log($msg);
        $questionaryData = DB::transaction(function () use ($request, $questionary, $client, $user, $pdfService, $zipService) {
            $data = $request->data;
            $questions = [];
            $questionaryData = $questionary->datas()->create([
                'client_id' => $request->client_id,
                'pdf' => "",
                'response' => ""
            ]);

            $zip_data = collect();

            foreach ($questionary->questions as $index => $question) {
                array_push($questions,(object) ['question'=>$question->question,'type' => $question->type]);
                if ($question->type == QuestionaryQuestion::IMAGE) {
                    $file = $this->saveFile($request->file("data.$index"), "questionary_pdf");
                    $data[$index] = $file->filename;
                }
                if ($question->type == QuestionaryQuestion::FILE_UPLOAD) {
                    $uploaded_files = $request->file("data.$index");
                    if ($uploaded_files) {
                        $files = [];
                        foreach($uploaded_files['files'] as $i => $uploaded_file) {
                            $file = $this->saveFile($uploaded_file, "questionary_files");
                            $questionaryData->files()->save($file);
                            array_push($files, (object) ['file_id' => $file->id, 'file_name' => $request->input("data.$index.file_names.$i") ?? '']);

                            $zip_data->push([
                                'path' => $file->filename,
                                'zip_path' => "Files/Q" . ($index + 1) . "/" . $request->input("data.$index.file_names.$i") ?? '',
                                "delete" => false,
                            ]);
                        }
                        $data[$index] = (object) ['files'=> $files];
                   }
                }
            }
            $created_at = now();
            if ($request->is_custom_date_selected == 1) {
                $date = $request->date;
                $time = $request->time;
                $created_at = Carbon::parse("$date $time");
            }
            // $data = ['questionary' => $questionary, 'data' => $data, 'client' => $client, 'created_at' => $created_at];

            // $file = $this->generateStoreQuestionary($data, 'exports.client_questionaries', 'clients/' . md5($request->client_id) . '/questionary_id');
            $filename = $this->generateFilePath('clients/' . md5($client->id) . '/questionary/pdf', $user, null, 'pdf');
            $file = $pdfService->client($client)->questionary()->custom($questionary, $data, $created_at, $questions)->saveFile($user, $filename);

            if($zip_data->count()) {
                $zip_path = $this->generateFilePath('clients/' . md5($client->id) . '/questionary/pdf', $user, null, "zip");

                $zip_data->push([
                    'path' => $file->filename,
                    'zip_path' => "{$questionary->title}.pdf",
                    "delete" => false,
                ]);

                // Generate and store zip
                $file = $zipService->make($zip_data)->saveFile($user, $zip_path);
            }

            $questionaryData->pdf = $file->filename;
            $questionaryData->response = collect($data)->values();
            $questionaryData->questions = collect($questions)->values();
            $questionaryData->save();

            if ($request->is_custom_date_selected == 1) {
                $date = $request->date;
                $time = $request->time;
                $created_at = Carbon::parse("$date $time");
                $questionaryData->created_at = $created_at;
                $questionaryData->save();
            }
            return $questionaryData;
        });

        return response()->json([
            'data' => $questionaryData,
            'message' => __('strings.questionary_answered'),
            'status' => '1'
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Questionary  $questionary
     * @param  \App\QuestionaryData  $questionaryData
     * @return \Illuminate\Http\Response
     */
    public function show(Questionary $questionary, QuestionaryData $questionaryData)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Questionary  $questionary
     * @param  \App\QuestionaryData  $questionaryData
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Questionary $questionary, QuestionaryData $questionaryData)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Questionary  $questionary
     * @param  \App\QuestionaryData  $questionaryData
     * @return \Illuminate\Http\Response
     */
    public function destroy(Questionary $questionary, QuestionaryData $questionaryData)
    {
        //
    }
}
