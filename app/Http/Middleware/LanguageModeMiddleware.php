<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\App;
use <PERSON>camara\LaravelLocalization\Facades\LaravelLocalization;

class LanguageModeMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        // default to app.locale if no locale header is set on the request
        if($request->has('locale')) {
            LaravelLocalization::setLocale($request->locale ?: config('app.locale'));
            App::setLocale($request->locale ?: config('app.locale'));
        } else {
            LaravelLocalization::setLocale($request->header('X-App-Locale') ?: config('app.locale'));
            App::setLocale($request->header('X-App-Locale') ?: config('app.locale'));
        }
        return $next($request);
    }
}
