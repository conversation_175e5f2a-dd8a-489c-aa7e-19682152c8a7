<?php

namespace App\Http\Middleware;

use Closure;
use App\User;
use Illuminate\Support\Facades\Auth;

class SubscriptionRequired
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next, $plan1 = null, $plan2 = null, $plan3 = null)
    {
        $user = Auth::user();

        if ($user->user_role == User::MASTER_ADMIN) {
            return $next($request);
        }
        $plans = [$plan1, $plan2, $plan3];

        $plans = array_filter($plans, fn($item) => $item !== null);

        if (!empty($plans)) {
            if (in_array('record', $plans) && !$user?->company?->record_plan) {
                return response()->json([
                    'message' => 'Please subscribe to a record plan',
                    'status' => '0'
                ], 402);
            }
            if (in_array('pos', $plans) && !$user?->company?->pos_plan) {
                return response()->json([
                    'message' => 'Please subscribe to a pos plan',
                    'status' => '0'
                ], 402);
            }
            if (in_array('management', $plans) && !$user?->company?->management_plan) {
                return response()->json([
                    'message' => 'Please subscribe to a management plan',
                    'status' => '0'
                ], 402);
            }
            if (in_array('management', $plans) && $user?->company?->management_plan) {
                if ($user->email != $user?->company?->email) {
                    return response()->json([
                        'message' => 'Unauthorized',
                        'status' => '0'
                    ], 402);
                }
            }
            if (in_array('booking', $plans) && !$user?->company?->booking_plan) {
                return response()->json([
                    'message' => 'Please subscribe to a booking plan',
                    'status' => '0'
                ], 402);
            }
            if (in_array('booking', $plans) && $user?->company?->booking_plan) {
                if ($user->is_booking_on == 0 || !$user->is_booking_on) {
                    return response()->json([
                        'message' => 'Unauthorized',
                        'status' => '0'
                    ], 402);
                }
            }
        }

        if (!($user?->company?->record_plan || $user?->company?->pos_plan || $user?->company?->management_plan || $user?->company?->booking_plan)) {
            return response()->json([
                'message' => 'Please subscribe to a plan',
                'status' => '0'
            ], 402);
        }

        return $next($request);
    }
}
