<?php

namespace App\Http\Middleware;

use App\User;
use Closure;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;


class MerchantIDRequired
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();

        if ($user->isMasterAdmin()) {
            return $next($request);
        }

        if (!$user->company->viva_merchant_id) {
            throw new Exception(__('pos_strings.merchant_id_is_required'));
        }

        return $next($request);
    }
}
