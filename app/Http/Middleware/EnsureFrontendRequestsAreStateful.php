<?php

namespace App\Http\Middleware;

use <PERSON><PERSON>\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful as MiddlewareEnsureFrontendRequestsAreStateful;

class EnsureFrontendRequestsAreStateful extends MiddlewareEnsureFrontendRequestsAreStateful
{
    /**
     * Configure secure cookie sessions.
     *
     * @return void
     */
    protected function configureSecureCookieSessions()
    {
        if (config('app.env') === "production") {
            config([
                'session.http_only' => true,
                'session.same_site' => 'lax',
            ]);
        }

        config([
            'session.http_only' => true,
            'session.same_site' => 'none',
        ]);
    }
}
