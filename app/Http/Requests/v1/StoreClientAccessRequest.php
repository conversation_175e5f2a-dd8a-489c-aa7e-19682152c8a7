<?php

namespace App\Http\Requests\v1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreClientAccessRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'select_all' => 'in:1,0',
            'remove_all' => 'in:0,1',
            'client_ids.*' => [
                Rule::exists('clients', 'id')->where(function ($query) {
                    $query->whereIn('id', Auth::user()->company->clients->pluck('id')->toArray());
                })
            ],
            'client_ids' => 'array',
        ];
    }
}
