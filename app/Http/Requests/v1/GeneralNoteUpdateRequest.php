<?php

namespace App\Http\Requests\v1;

use App\Rules\IsValidString;
use Illuminate\Foundation\Http\FormRequest;

class GeneralNoteUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => [new IsValidString],
            'notes' => '',
            'notes_html' => '',
            'important' => 'boolean',
            'file' => '',
            'files.*' => 'required|max:51200',
            'files' => 'array|min:1',
        ];
    }
}