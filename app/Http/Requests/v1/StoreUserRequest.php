<?php

namespace App\Http\Requests\v1;

use App\Exceptions\NoSubscriptionException;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Validator;

class StoreUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'password' => 'required|confirmed|regex:/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9]).{8,}$/',
            'country' => '',
            'state' => '',
            'city' => '',
            'zip_code' => '',
            'street_address' => '',
            'mobile_number' => 'nullable|numeric',
            'email' => 'required|unique:users,email|email',
            'last_name' => 'required',
            'first_name' => 'required',
            'title' => 'filled',
            'user_role' => 'required|in:admin,user',
            'is_booking_on' => [
                'in:1,0',
                'required',
            ],
            'is_record_on' => [
                'in:1,0',
                'required',
            ],
            'is_pos_on' => [
                'in:1,0',
                'required',
            ],
            'profile_photo' => 'image|mimes:jpeg,jpg,png',
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $company = Auth::user()->company;

            if ($this->is_record_on && !$company->record_plan) {
                throw new NoSubscriptionException(__('strings.please_subscribe'), 0);
            }

            if ($this->is_pos_on && !$company->pos_plan) {
                throw new NoSubscriptionException(__('strings.please_subscribe'), 0);
            }

            if ($this->is_booking_on && !$company->booking_plan) {
                throw new NoSubscriptionException(__('strings.please_subscribe'), 0);
            }

            if ($this->is_record_on && !$company->canAddUserInRecordSystem(1, true)) {
                $validator->errors()->add('is_record_on', __('strings.maximum_user_limit_reached'));
            }

            if ($this->is_booking_on && !$company->canAddUserInBookingSystem(1, true)) {
                $validator->errors()->add('is_booking_on', __('strings.maximum_user_limit_reached'));
            }

            if ($this->is_pos_on && !$company->canAddUserInPOSSystem()) {
                $validator->errors()->add('is_pos_on', __('strings.maximum_user_limit_reached'));
            }
        });
    }

    public function messages()
    {
        return [
            'password.regex' => 'Your password must be more than 8 characters long, should contain at-least 1 Uppercase, 1 Lowercase, 1 Numeric.'
        ];
    }
}