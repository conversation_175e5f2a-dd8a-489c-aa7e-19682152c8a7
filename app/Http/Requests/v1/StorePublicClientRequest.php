<?php

namespace App\Http\Requests\v1;

use App\Company;
use Exception;
use Illuminate\Foundation\Http\FormRequest;

class StorePublicClientRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function prepareForValidation()
    {
        try {
            $this->merge([
                'company_id' => Company::decryptId($this->company_id),
            ]);
        } catch (Exception $e) {
            throw new Exception('Please provide valid url');
        }
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $company = Company::findOrFail($this->company_id);
        $questionaries = $company->questionaries()->has('questions')->active()->get();

        $formated_validations = [];
        foreach ($questionaries as $questionaryIndex => $questionary) {
            $questions = $questionary->questions;

            $formated_validations = [];
            foreach ($questions as $index => $question) {
                foreach (config('questionary.type_with_answer_validation') as $type => $validations) {
                    if ($type == $question->type) {
                        if ($question->required) {
                            $validations = $validations['required'];
                        } else {
                            $validations = $validations['normal'];
                        }
                        foreach ($validations as $field => $validation) {
                            $field = str_replace('_index', $index, $field);
                            if ($question->options && count($question->options)) {
                                $field = str_replace('_options', implode(',', $question->options), $field);
                            }

                            $validation = str_replace('_index', $index, $validation);
                            if ($question->options && count($question->options)) {
                                $validation = str_replace('_options', implode(',', $question->options), $validation);
                            }
                            $formated_validations["questionary.$questionaryIndex.$field"] = $validation;
                        }
                    }
                }
            }

            if ($questions->count()) {
                $formated_validations["questionary.$questionaryIndex.data"] = "required|size:{$questions->count()}|array";
            }
        }
        if ($questionaries->count()) {
            $formated_validations['questionary'] = "required|array|size:{$questionaries->count()}";
        }

        $extra_fields = $company->client_extra_fields()->where('view', true)->get();
        $extra_fields_rules = $extra_fields->flatMap(function ($field, $key) {
            return [
                "extra.$key.id" => 'required|in:' . $field->id,
                "extra.$key.value" => $field->required ? 'required' : '',
            ];
        })->all();

        // $verify_required = $company->settings()->where('key','REQUIRED_CONSENT')->where('value', true)->exists();

        return array_merge([
            // recaptcha

            'recaptcha' => 'recaptcha',

            // 'verify' => ($verify_required ? "in:true,1|required": 'in:true,false,0,1'),
            'verify' => "in:true,1|required",
            'addressess.*.country' => '',
            'addressess.*.state' => '',
            'addressess.*.city' => '',
            'addressess.*.zip_code' => '',
            'addressess.*.street_address' => '',
            'addressess' => 'array',
            'phone_number' => 'numeric|nullable',
            'email' => 'nullable|email',
            'personal_id' => '',
            // 'social_security_number' => 'required',
            'last_name' => 'required',
            'first_name' => 'required',
            'profile_picture' => 'sometimes|image|mimes:jpeg,jpg,png',
            'user_id' => 'exists:users,id',
            'company_id' => 'required|exists:companies,id',

            // aesthehic interest

            'aesthetic_interest.10.notes' => 'nullable',
            'aesthetic_interest.10' => 'sometimes|allowed_attributes:notes',

            'aesthetic_interest.9.answer_checkbox.*' => 'sometimes|required|in:1,0',
            'aesthetic_interest.9.answer_checkbox' => 'sometimes|required|size:3|array',
            'aesthetic_interest.9' => 'sometimes|allowed_attributes:answer_checkbox',

            'aesthetic_interest.8.other' => 'nullable',
            'aesthetic_interest.8.answer_checkbox.*' => 'sometimes|required|in:1,0',
            'aesthetic_interest.8.answer_checkbox' => 'sometimes|required|array|size:7',
            'aesthetic_interest.8' => 'sometimes|allowed_attributes:answer_checkbox,other',

            'aesthetic_interest.7.other' => 'nullable',
            'aesthetic_interest.7.answer_checkbox.*' => 'sometimes|required|in:1,0',
            'aesthetic_interest.7.answer_checkbox' => 'sometimes|required|size:10|array',
            'aesthetic_interest.7' => 'sometimes|allowed_attributes:answer_checkbox,other',

            'aesthetic_interest.6.answer_checkbox.*' => 'sometimes|required|in:1,0',
            'aesthetic_interest.6.answer_checkbox' => 'sometimes|required|array|size:4',
            'aesthetic_interest.6' => 'sometimes|allowed_attributes:answer_checkbox',

            'aesthetic_interest.5.image' => 'nullable',
            'aesthetic_interest.5' => 'sometimes|allowed_attributes:image',

            'aesthetic_interest.4.answer_checkbox.*' => 'sometimes|required|in:1,0',
            'aesthetic_interest.4.answer_checkbox' => 'sometimes|required|array|size:4',
            'aesthetic_interest.4' => 'sometimes|allowed_attributes:answer_checkbox',

            'aesthetic_interest.3.answer_checkbox.*' => 'sometimes|required|in:1,0',
            'aesthetic_interest.3.answer_checkbox' => 'sometimes|required|array|size:4',
            'aesthetic_interest.3' => 'sometimes|allowed_attributes:answer_checkbox',

            'aesthetic_interest.2.answer_checkbox.*' => 'sometimes|required|in:1,0',
            'aesthetic_interest.2.answer_checkbox' => 'sometimes|required|size:8|array',
            'aesthetic_interest.2' => 'sometimes|allowed_attributes:answer_checkbox',

            'aesthetic_interest.1.other' => 'nullable',
            'aesthetic_interest.1.answer_checkbox.*' => 'sometimes|required|in:1,0',
            'aesthetic_interest.1.answer_checkbox' => 'sometimes|required|array|size:5',
            'aesthetic_interest.1' => 'sometimes|allowed_attributes:answer_checkbox,other',

            'aesthetic_interest.0.notes' => 'nullable',
            'aesthetic_interest.0' => 'sometimes|allowed_attributes:notes',

            'aesthetic_interest' => 'sometimes|required|array|size:11',

            // health questionaries

            'health_questions.28.more_info' => 'sometimes|present',
            'health_questions.28' => 'sometimes|allowed_attributes:more_info',

            'health_questions.27.answer' => 'sometimes|required|in:yes,no',
            'health_questions.27' => 'sometimes|allowed_attributes:answer',

            'health_questions.26.answer' => 'sometimes|required|in:yes,no',
            'health_questions.26' => 'sometimes|allowed_attributes:answer',

            'health_questions.25.answer' => 'sometimes|required|in:yes,no',
            'health_questions.25' => 'sometimes|allowed_attributes:answer',

            'health_questions.24.answer' => 'sometimes|required|in:yes,no',
            'health_questions.24' => 'sometimes|allowed_attributes:answer',

            'health_questions.23.more_info' => 'sometimes|required_if:health_questions.23.answer,yes',
            'health_questions.23.answer' => 'sometimes|required|in:yes,no',
            'health_questions.23' => 'sometimes|allowed_attributes:answer,more_info',

            'health_questions.22.more_info' => 'sometimes|required_if:health_questions.22.answer,yes',
            'health_questions.22.answer' => 'sometimes|required|in:yes,no',
            'health_questions.22' => 'sometimes|allowed_attributes:answer,more_info',

            'health_questions.21.more_info' => 'sometimes|required_if:health_questions.21.answer,yes',
            'health_questions.21.answer' => 'sometimes|required|in:yes,no',
            'health_questions.21' => 'sometimes|allowed_attributes:answer,more_info',

            'health_questions.20.answer' => 'sometimes|required|in:yes,no',
            'health_questions.20' => 'sometimes|allowed_attributes:answer',

            'health_questions.19.answer' => 'sometimes|required|in:yes,no',
            'health_questions.19' => 'sometimes|allowed_attributes:answer',

            'health_questions.18.answer' => 'sometimes|required|in:yes,no',
            'health_questions.18' => 'sometimes|allowed_attributes:answer',

            'health_questions.17.answer' => 'sometimes|required|in:yes,no',
            'health_questions.17' => 'sometimes|allowed_attributes:answer',

            'health_questions.16.answer' => 'sometimes|required|in:yes,no',
            'health_questions.16' => 'sometimes|allowed_attributes:answer',

            'health_questions.15.answer' => 'sometimes|required|in:yes,no',
            'health_questions.15' => 'sometimes|allowed_attributes:answer',

            'health_questions.14.answer' => 'sometimes|required|in:yes,no',
            'health_questions.14' => 'sometimes|allowed_attributes:answer',

            'health_questions.13.answer' => 'sometimes|required|in:yes,no',
            'health_questions.13' => 'sometimes|allowed_attributes:answer',

            'health_questions.12.answer' => 'sometimes|required|in:yes,no',
            'health_questions.12' => 'sometimes|allowed_attributes:answer',

            'health_questions.11.answer' => 'sometimes|required|in:yes,no',
            'health_questions.11' => 'sometimes|allowed_attributes:answer',

            'health_questions.10.answer' => 'sometimes|required|in:yes,no',
            'health_questions.10' => 'sometimes|allowed_attributes:answer',

            'health_questions.9.more_info' => 'sometimes|required_if:health_questions.9.answer,yes',
            'health_questions.9.answer' => 'sometimes|required|in:yes,no',
            'health_questions.9' => 'sometimes|allowed_attributes:answer,more_info',

            'health_questions.8.more_info' => 'sometimes|required_if:health_questions.8.answer,yes',
            'health_questions.8.answer' => 'sometimes|required|in:yes,no',
            'health_questions.8' => 'sometimes|allowed_attributes:answer,more_info',

            'health_questions.7.more_info' => 'sometimes|required_if:health_questions.7.answer,yes',
            'health_questions.7.answer' => 'sometimes|required|in:yes,no',
            'health_questions.7' => 'sometimes|allowed_attributes:answer,more_info',

            'health_questions.6.more_info' => 'sometimes|required_if:health_questions.6.answer,yes',
            'health_questions.6.answer' => 'sometimes|required|in:yes,no',
            'health_questions.6' => 'sometimes|allowed_attributes:answer,more_info',

            'health_questions.5.answer' => 'sometimes|required|in:yes,no',
            'health_questions.5' => 'sometimes|allowed_attributes:answer',

            'health_questions.4.answer' => 'sometimes|required|in:yes,no',
            'health_questions.4' => 'sometimes|allowed_attributes:answer',

            'health_questions.3.more_info' => 'sometimes|required_if:health_questions.3.answer,yes',
            'health_questions.3.answer' => 'sometimes|required|in:yes,no',
            'health_questions.3' => 'sometimes|allowed_attributes:answer,more_info',

            'health_questions.2.answer' => 'sometimes|required|in:yes,no',
            'health_questions.2' => 'sometimes|allowed_attributes:answer',

            'health_questions.1.answer' => 'sometimes|required|in:yes,no',
            'health_questions.1' => 'sometimes|allowed_attributes:answer',

            'health_questions.0.more_info' => 'sometimes|required_if:health_questions.0.answer,yes',
            'health_questions.0.answer' => 'sometimes|required|in:yes,no',
            'health_questions.0' => 'sometimes|allowed_attributes:answer,more_info',

            'health_questions' => 'sometimes|required|array|size:29',

            // covid19 questionarries

            'covid19.11.more_info' => 'sometimes|required_if:covid19.11.answer,yes',
            'covid19.11.answer' => 'sometimes|required|in:yes,no',
            'covid19.11' => 'sometimes|allowed_attributes:answer,more_info',

            'covid19.10.more_info' => 'sometimes|required_if:covid19.10.answer,yes',
            'covid19.10.answer' => 'sometimes|required|in:yes,no',
            'covid19.10' => 'sometimes|allowed_attributes:answer,more_info',

            'covid19.9.more_info' => 'sometimes|required_if:covid19.9.answer,yes',
            'covid19.9.answer' => 'sometimes|required|in:yes,no',
            'covid19.9' => 'sometimes|allowed_attributes:answer,more_info',

            'covid19.8.more_info' => 'sometimes|required_if:covid19.8.answer,yes',
            'covid19.8.answer' => 'sometimes|required|in:yes,no',
            'covid19.8' => 'sometimes|allowed_attributes:answer,more_info',

            'covid19.7.answer' => 'sometimes|in:yes,no',
            'covid19.7' => 'sometimes|allowed_attributes:answer',

            'covid19.6.more_info' => 'sometimes|required_if:covid19.6.answer,yes',
            'covid19.6.answer' => 'sometimes|in:yes,no',
            'covid19.6' => 'sometimes|allowed_attributes:answer,more_info',

            'covid19.5.more_info' => 'sometimes|required_if:covid19.5.answer,yes',
            'covid19.5.answer' => 'sometimes|in:yes,no',
            'covid19.5' => 'sometimes|allowed_attributes:answer,more_info',

            'covid19.4.more_info' => 'sometimes|required_if:covid19.4.answer,yes',
            'covid19.4.answer' => 'sometimes|in:yes,no',
            'covid19.4' => 'sometimes|allowed_attributes:answer,more_info',

            'covid19.3.more_info' => 'sometimes|required_if:covid19.3.answer,yes',
            'covid19.3.answer' => 'sometimes|in:yes,no',
            'covid19.3' => 'sometimes|allowed_attributes:answer,more_info',

            'covid19.2.more_info' => 'sometimes|required_if:covid19.2.answer,yes',
            'covid19.2.answer' => 'sometimes|in:yes,no',
            'covid19.2' => 'sometimes|allowed_attributes:answer,more_info',

            'covid19.1.more_info' => 'sometimes|required_if:covid19.1.answer,yes',
            'covid19.1.answer' => 'sometimes|in:yes,no',
            'covid19.1' => 'sometimes|allowed_attributes:answer,more_info',

            'covid19.0.more_info' => 'sometimes|required_if:covid19.0.answer,yes',
            'covid19.0.answer' => 'sometimes|in:yes,no',
            'covid19.0' => 'sometimes|allowed_attributes:answer,more_info',

            'covid19' => 'sometimes|array|size:12',

            // client letter of consent
            'letter_of_consents' => 'sometimes|array',
            'letter_of_consents.*.is_publish_before_after_pictures' => 'nullable',
            'letter_of_consents.*.is_bad_allergic_shock' => 'in:yes,no',
            'letter_of_consents.*.consent_id' => 'required_without:letter_of_consents.*.signed_file|exists:letter_of_consents,id',

            'extra' => 'array|size:' . $extra_fields->count(),
        ], $formated_validations, $extra_fields_rules);
    }

    public function messages()
    {
        return [
            'verify.required' => 'please provide your consent',
            'verify.in' => 'please provide your consent',
        ];
    }
}
