<?php

namespace App\Http\Requests\v1;

use App\Rules\OrganizationNumber;
use App\User;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class UpdateMasterCompanyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email' => [
                Rule::unique('users', 'email')->ignore($this->route('company')->email, 'email'),
            ],
            'country' => 'filled',
            'state' => 'filled',
            'city' => 'filled',
            'zip_code' => 'filled',
            'street_address' => 'filled',
            'organization_number' => [
                new OrganizationNumber(),
            ],
            'is_blocked' => 'in:1,0',
            'is_read_only' => 'in:1,0',
            'isv_percentage' => 'numeric|between:0,100',
        ];
    }
}
