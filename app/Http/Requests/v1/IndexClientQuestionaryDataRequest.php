<?php

namespace App\Http\Requests\v1;

use App\AestheticInterest;
use App\Covid19;
use App\HealthQuestionary;
use App\Questionary;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class IndexClientQuestionaryDataRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'questionary_id' => 'required|integer',
            'questionary_type' => [
                'required',
                Rule::in([
                    HealthQuestionary::class,
                    AestheticInterest::class,
                    Covid19::class,
                    Questionary::class,
                ]),
            ],
            'orderBy' => 'in:created_at',
            'orderDirection' => 'in:asc,desc,ASC,DESC',
        ];
    }
}
