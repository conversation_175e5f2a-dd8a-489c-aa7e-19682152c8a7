<?php

namespace App\Http\Requests\v1;

use App\Rules\UniqueCompanyName;
use Illuminate\Foundation\Http\FormRequest;

class StoreCompanyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    // (?=.*?[#?!@$%^&*-]) special character regex
    public function rules()
    {
        return [
            'recaptcha' => 'recaptcha',

            'mobile_number' => 'numeric|nullable',
            'password' => 'required|confirmed|regex:/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9]).{8,}$/',
            'country' => 'required',
            'zip_code' => '',
            'state' => '',
            'city' => '',
            'street_address' => '',
            'email' => 'required|unique:users,email|email|confirmed',
            'last_name' => 'required',
            'first_name' => 'required',
            'company_name' => [
                'required',
                new UniqueCompanyName(),
            ],
            'company_photo' => 'image|mimes:jpeg,jpg,png',
            'profile_photo' => 'image|mimes:jpeg,jpg,png',
        ];
    }

    public function messages()
    {
        return [
            'password.regex' => 'Your password must be more than 8 characters long, should contain at-least 1 Uppercase, 1 Lowercase, 1 Numeric.'
        ];
    }
}
