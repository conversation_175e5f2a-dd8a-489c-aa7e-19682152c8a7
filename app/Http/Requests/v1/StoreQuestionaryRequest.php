<?php

namespace App\Http\Requests\v1;

use App\Questionary;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreQuestionaryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => [
                'required',
                'max:254',
                Rule::unique(Questionary::class, 'title')->where('company_id', Auth::user()->company_id)->where('deleted_at', null),
            ],
        ];
    }
}
