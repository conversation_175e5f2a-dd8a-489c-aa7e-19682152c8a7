<?php

namespace App\Http\Requests\v1;

use Illuminate\Foundation\Http\FormRequest;

class SubscriptionUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'vat_number' => 'sometimes|max:30',
            'paymentMethod' => 'sometimes',
            'address.city' => 'sometimes|max:30',
            'address.country' => 'sometimes|max:30',
            'address.line1' => 'sometimes|max:120',
            'address.postal_code' => 'sometimes|max:30',
            'email' => 'sometimes|email',
            'phone' => 'sometimes|max:20',
            'name' => 'sometimes|max:30',
        ];
    }
}
