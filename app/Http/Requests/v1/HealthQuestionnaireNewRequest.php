<?php

namespace App\Http\Requests\v1;

use Illuminate\Foundation\Http\FormRequest;

class HealthQuestionnaireNewRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

     /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'health_questions.28.more_info' => 'present',
            'health_questions.28' => 'allowed_attributes:more_info',

            'health_questions.27.answer' => 'required|in:yes,no',
            'health_questions.27' => 'allowed_attributes:answer',

            'health_questions.26.answer' => 'required|in:yes,no',
            'health_questions.26' => 'allowed_attributes:answer',

            'health_questions.25.answer' => 'required|in:yes,no',
            'health_questions.25' => 'allowed_attributes:answer',

            'health_questions.24.answer' => 'required|in:yes,no',
            'health_questions.24' => 'allowed_attributes:answer',

            'health_questions.23.more_info' => 'required_if:health_questions.23.answer,yes',
            'health_questions.23.answer' => 'required|in:yes,no',
            'health_questions.23' => 'allowed_attributes:answer,more_info',

            'health_questions.22.more_info' => 'required_if:health_questions.22.answer,yes',
            'health_questions.22.answer' => 'required|in:yes,no',
            'health_questions.22' => 'allowed_attributes:answer,more_info',

            'health_questions.21.more_info' => 'required_if:health_questions.21.answer,yes',
            'health_questions.21.answer' => 'required|in:yes,no',
            'health_questions.21' => 'allowed_attributes:answer,more_info',

            'health_questions.20.answer' => 'required|in:yes,no',
            'health_questions.20' => 'allowed_attributes:answer',

            'health_questions.19.answer' => 'required|in:yes,no',
            'health_questions.19' => 'allowed_attributes:answer',

            'health_questions.18.answer' => 'required|in:yes,no',
            'health_questions.18' => 'allowed_attributes:answer',

            'health_questions.17.answer' => 'required|in:yes,no',
            'health_questions.17' => 'allowed_attributes:answer',

            'health_questions.16.answer' => 'required|in:yes,no',
            'health_questions.16' => 'allowed_attributes:answer',

            'health_questions.15.answer' => 'required|in:yes,no',
            'health_questions.15' => 'allowed_attributes:answer',

            'health_questions.14.answer' => 'required|in:yes,no',
            'health_questions.14' => 'allowed_attributes:answer',

            'health_questions.13.answer' => 'required|in:yes,no',
            'health_questions.13' => 'allowed_attributes:answer',

            'health_questions.12.answer' => 'required|in:yes,no',
            'health_questions.12' => 'allowed_attributes:answer',

            'health_questions.11.answer' => 'required|in:yes,no',
            'health_questions.11' => 'allowed_attributes:answer',

            'health_questions.10.answer' => 'required|in:yes,no',
            'health_questions.10' => 'allowed_attributes:answer',

            'health_questions.9.more_info' => 'required_if:health_questions.9.answer,yes',
            'health_questions.9.answer' => 'required|in:yes,no',
            'health_questions.9' => 'allowed_attributes:answer,more_info',

            'health_questions.8.more_info' => 'required_if:health_questions.8.answer,yes',
            'health_questions.8.answer' => 'required|in:yes,no',
            'health_questions.8' => 'allowed_attributes:answer,more_info',

            'health_questions.7.more_info' => 'required_if:health_questions.7.answer,yes',
            'health_questions.7.answer' => 'required|in:yes,no',
            'health_questions.7' => 'allowed_attributes:answer,more_info',
            
            'health_questions.6.more_info' => 'required_if:health_questions.6.answer,yes',
            'health_questions.6.answer' => 'required|in:yes,no',
            'health_questions.6' => 'allowed_attributes:answer,more_info',

            'health_questions.5.answer' => 'required|in:yes,no',
            'health_questions.5' => 'allowed_attributes:answer',
            
            'health_questions.4.answer' => 'required|in:yes,no',
            'health_questions.4' => 'allowed_attributes:answer',

            'health_questions.3.more_info' => 'required_if:health_questions.3.answer,yes',
            'health_questions.3.answer' => 'required|in:yes,no',
            'health_questions.3' => 'allowed_attributes:answer,more_info',

            'health_questions.2.answer' => 'required|in:yes,no',
            'health_questions.2' => 'allowed_attributes:answer',

            'health_questions.1.answer' => 'required|in:yes,no',
            'health_questions.1' => 'allowed_attributes:answer',

            'health_questions.0.more_info' => 'required_if:health_questions.0.answer,yes',
            'health_questions.0.answer' => 'required|in:yes,no',
            'health_questions.0' => 'allowed_attributes:answer,more_info',


            'health_questions' => 'required|array|size:29'
        ];
    }
}
