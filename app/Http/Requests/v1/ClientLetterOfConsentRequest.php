<?php

namespace App\Http\Requests\v1;

use Illuminate\Foundation\Http\FormRequest;

class ClientLetterOfConsentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'signature' => 'nullable|image|mimes:jpeg,jpg,png',
            'is_publish_before_after_pictures' => 'nullable|in:0,1,2,3,4,5',
            'is_bad_allergic_shock' => 'in:yes,no',
            'consent_id' => 'required_without:signed_file|exists:letter_of_consents,id',
            // 'signed_file' => 'required_without:signature|file|max:5120',
        ];
    }
}
