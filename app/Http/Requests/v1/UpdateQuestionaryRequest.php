<?php

namespace App\Http\Requests\v1;

use App\Questionary;
use App\Rules\IsValidString;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class UpdateQuestionaryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => [
                'sometimes',
                'max:254',
                new IsValidString,
                Rule::unique(Questionary::class, 'title')
                    ->where('company_id', Auth::user()->company_id)
                    ->ignore($this->route('questionary')->id, 'id')
                    ->where('deleted_at', null),
            ],
            'is_active' => [
                'sometimes',
                'in:0,1,yes,no,true,false',
            ],
        ];
    }
}