<?php

namespace App\Http\Requests\v1;

use App\Company;
use App\CompanyLead;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class StoreUpdateCompanyLeadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'company_id' => [
                'required',
                Rule::exists(Company::class,'id'),
            ],
            'status' => [
                'nullable',
                Rule::in(CompanyLead::STATUS)
            ],
            'notes' => '',
            'contacted' => 'nullable|in:1,0,true,false',
        ];
    }
}
