<?php

namespace App\Http\Requests\v1;

use App\Exceptions\NoSubscriptionException;
use App\User;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class UpdateUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'old_password' => Auth::user()->user_role != User::MASTER_ADMIN ? 'required_with:password' : '',
            'password' => 'filled|confirmed|regex:/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9]).{8,}$/',
            'country' => '',
            'state' => '',
            'city' => '',
            'zip_code' => 'nullable',
            'street_address' => '',
            'mobile_number' => 'nullable|numeric',
            'last_name' => 'filled',
            'first_name' => 'filled',
            'title' => 'filled',
            'user_role' => 'filled|in:admin,user',
            'profile_photo' => 'image|mimes:jpeg,jpg,png',
            'booking_description' => '',
            'email' => [
                Rule::unique('users', 'email')->ignore($this->route('user')->id, 'id'),
            ],
            'is_booking_on' => [
                'in:1,0',
            ],
            'is_record_on' => [
                'in:1,0',
            ],
            'is_pos_on' => [
                'in:1,0',
            ],
        ];
    }

    /**
     * Configure the validator instance.
     *
     * @param  \Illuminate\Validation\Validator  $validator
     * @return void
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $user = $this->route('user');
            $company = $user->company;

            if ($this->is_record_on && !$company->record_plan) {
                throw new NoSubscriptionException(__('strings.please_subscribe'), 0);
            }

            if ($this->is_pos_on && !$company->pos_plan) {
                throw new NoSubscriptionException(__('strings.please_subscribe'), 0);
            }

            if ($this->is_booking_on && !$company->booking_plan) {
                throw new NoSubscriptionException(__('strings.please_subscribe'), 0);
            }

            if ($this->has('is_record_on') && $this->is_record_on && !$user->is_record_on && !$company->canAddUserInRecordSystem(1, true)) {
                $validator->errors()->add('is_record_on', __('strings.maximum_user_limit_reached'));
            }

            if ($this->has('is_booking_on') && $this->is_booking_on && !$user->is_booking_on && !$company->canAddUserInBookingSystem(1, true)) {
                $validator->errors()->add('is_booking_on', __('strings.maximum_user_limit_reached'));
            }

            if ($this->has('is_pos_on') && $this->is_pos_on && !$user->is_pos_on && !$company->canAddUserInPOSSystem()) {
                $validator->errors()->add('is_pos_on', __('strings.maximum_user_limit_reached'));
            }
        });
    }

    public function messages()
    {
        return [
            'password.regex' => 'Your password must be more than 8 characters long, should contain at-least 1 Uppercase, 1 Lowercase, 1 Numeric.'
        ];
    }
}
