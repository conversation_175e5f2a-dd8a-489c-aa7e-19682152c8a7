<?php

namespace App\Http\Requests\v1;

use Illuminate\Foundation\Http\FormRequest;

class Covid19StoreUpdateNewRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'covid19.11.more_info' => 'required_if:covid19.11.answer,yes',
            'covid19.11.answer' => 'required|in:yes,no',
            'covid19.11' => 'allowed_attributes:answer,more_info',

            'covid19.10.more_info' => 'required_if:covid19.10.answer,yes',
            'covid19.10.answer' => 'required|in:yes,no',
            'covid19.10' => 'allowed_attributes:answer,more_info',

            'covid19.9.more_info' => 'required_if:covid19.9.answer,yes',
            'covid19.9.answer' => 'required|in:yes,no',
            'covid19.9' => 'allowed_attributes:answer,more_info',

            'covid19.8.more_info' => 'required_if:covid19.8.answer,yes',
            'covid19.8.answer' => 'required|in:yes,no',
            'covid19.8' => 'allowed_attributes:answer,more_info',
            
            'covid19.7.answer' => 'required|in:yes,no',
            'covid19.7' => 'allowed_attributes:answer',

            'covid19.6.more_info' => 'required_if:covid19.6.answer,yes',
            'covid19.6.answer' => 'required|in:yes,no',
            'covid19.6' => 'allowed_attributes:answer,more_info',

            'covid19.5.more_info' => 'required_if:covid19.5.answer,yes',
            'covid19.5.answer' => 'required|in:yes,no',
            'covid19.5' => 'allowed_attributes:answer,more_info',

            'covid19.4.more_info' => 'required_if:covid19.4.answer,yes',
            'covid19.4.answer' => 'required|in:yes,no',
            'covid19.4' => 'allowed_attributes:answer,more_info',

            'covid19.3.more_info' => 'required_if:covid19.3.answer,yes',
            'covid19.3.answer' => 'required|in:yes,no',
            'covid19.3' => 'allowed_attributes:answer,more_info',

            'covid19.2.more_info' => 'required_if:covid19.2.answer,yes',
            'covid19.2.answer' => 'required|in:yes,no',
            'covid19.2' => 'allowed_attributes:answer,more_info',

            'covid19.1.more_info' => 'required_if:covid19.1.answer,yes',
            'covid19.1.answer' => 'required|in:yes,no',
            'covid19.1' => 'allowed_attributes:answer,more_info',

            'covid19.0.more_info' => 'required_if:covid19.0.answer,yes',
            'covid19.0.answer' => 'required|in:yes,no',
            'covid19.0' => 'allowed_attributes:answer,more_info',

            'covid19' => 'required|array|size:12'
        ];
    }
}
