<?php

namespace App\Http\Requests\v1;

use App\Rules\IsValidString;
use Illuminate\Foundation\Http\FormRequest;

class ClientTreatmentUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'images.*' => 'sometimes|image|mimes:jpeg,jpg,png',
            'images' => 'sometimes|required|array',
            'sign' => 'sometimes|required|image|mimes:jpeg,jpg,png',
            'cost' => 'required|numeric',
            'notes' => ['nullable', new IsValidString],
            'date' => 'nullable|date|date_format:Y-m-d',
            'cost' => 'nullable|numeric',
            'name' => ['nullable', new IsValidString],
            'treatment_id' => 'nullable',
            'unit' => 'nullable|in:eur,usd,sek,gbp'
        ];
    }
}