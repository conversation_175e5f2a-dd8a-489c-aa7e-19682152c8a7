<?php

namespace App\Http\Requests\v1;

use App\Client;
use App\QuestionaryQuestion;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Http\FormRequest;

class StoreQuestionaryDataRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $questionary = $this->route('questionary');

        $questions = $questionary->questions;

        $formated_validations = [];
        foreach ($questions as $index => $question) {
            foreach (config('questionary.type_with_answer_validation') as $type => $validations) {
                if ($type == $question->type) {
                    if ($question->required) {
                        $validations = $validations['required'];
                    } else {
                        $validations = $validations['normal'];
                    }
                    foreach ($validations as $field => $validation) {
                        $field = str_replace("_index", $index, $field);
                        if ($question->options && count($question->options)) {
                            $field = str_replace("_options", implode(",", $question->options), $field);
                        }

                        $validation = str_replace("_index", $index, $validation);
                        if ($question->options && count($question->options)) {
                            $validation = str_replace("_options", implode(",", $question->options), $validation);
                        }

                        $formated_validations[$field] = $validation;
                    }
                }
            }
        }

        if ($questions->count()) {
            $formated_validations['data'] = "required|size:{$questions->count()}|array";
        }

        $formated_validations["client_id"] = [
            "required",
            Rule::exists(Client::class, "id")->where('company_id', Auth::user()->company_id),
        ];

        return $formated_validations;
    }
}
