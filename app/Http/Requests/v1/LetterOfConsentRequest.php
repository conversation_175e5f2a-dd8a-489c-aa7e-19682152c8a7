<?php

namespace App\Http\Requests\v1;

use Illuminate\Foundation\Http\FormRequest;

class LetterOfConsentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'is_publish_before_after_pictures' => 'required|integer|in:1,0',
            'letter' => '',
            'letter_html' => '',
            'letter_json' => 'json',
            'consent_title' => 'required'
        ];
    }
}
