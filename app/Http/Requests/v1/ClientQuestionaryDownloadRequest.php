<?php

namespace App\Http\Requests\v1;

use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class ClientQuestionaryDownloadRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'type' => [
                Rule::in([
                    "App\AestheticInterest",
                    "App\HealthQuestionary",
                    "App\Covid19",
                    "App\Custom",
                ])
            ],
            'id' => 'nullable',
        ];
    }
}
