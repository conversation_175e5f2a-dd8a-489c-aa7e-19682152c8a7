<?php

namespace App\Http\Requests\v1;

use Exception;
use App\Company;
use Illuminate\Foundation\Http\FormRequest;

class PublicIndexQuestionaryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function prepareForValidation()
    {
        try {
            $this->merge([
                'company_id' => Company::decryptId($this->company_id)
            ]);
        } catch (Exception $e) {
            throw new Exception('Please provide valid url');
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'company_id' => 'required',
        ];
    }
}
