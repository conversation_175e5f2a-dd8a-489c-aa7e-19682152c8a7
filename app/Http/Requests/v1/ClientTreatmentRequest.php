<?php

namespace App\Http\Requests\v1;

use App\Rules\IsValidString;
use Illuminate\Foundation\Http\FormRequest;

class ClientTreatmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'images.*' => 'image|mimes:jpeg,jpg,png',
            'images' => 'required|array',
            'color' => 'required',
            'date' => 'nullable|date|date_format:Y-m-d',
            'cost' => 'nullable|numeric',
            'treatment_cost' => 'nullable|numeric',
            'name' => ['nullable', new IsValidString],
            'treatment_id' => 'nullable',
            'unit' => 'required|in:eur,usd,sek,gbp'
        ];
    }
}