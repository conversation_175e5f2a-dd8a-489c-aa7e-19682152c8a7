<?php

namespace App\Http\Requests\v1;

use Illuminate\Foundation\Http\FormRequest;

class StoreAfterCareTreatmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'recaptcha' => 'recaptcha',

            'client_id' => 'exists:client,id',
            'note' => '',
            'notes' => '',
            'file' => 'file|max:25000',
            'loc_ids.*' => '',
            'loc_ids' => 'array',
            'questionnairies.*.id' => '',
            'questionnairies.*.type' => '',
            'questionnairies' => 'array',
            'video_call' => 'boolean',
        ];
    }
}
