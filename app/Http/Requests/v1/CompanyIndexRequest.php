<?php

namespace App\Http\Requests\v1;

use App\Company;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CompanyIndexRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'start_date' => 'date|date_format:Y-m-d|required_with:end_date|before_or_equal:today',
            'end_date' => 'date|date_format:Y-m-d|required_with:start_date|before_or_equal:today',
            'orderDirection' => 'in:asc,desc,ASC,DESC',
            'orderBy' => 'in:company_name,mobile_number,contacted,email,subscription,users_count,clients_count,bookings_count,procedures_count,created_at,storage_usage,is_blocked,is_read_only,last_login_at,is_booking_on',
            'excel' => "",
            'filter' => "in:PRESCRIBERS,COMPANY_DELETE",
            'page' => 'integer|gt:0',
            'per_page' => 'integer|gt:0',
            'verification' => [
                'nullable',
                Rule::in([Company::COMPANY_REJECTED, Company::COMPANY_VERIFIED, 'pending'])
            ],
        ];
    }
}
