<?php

namespace App\Http\Requests\v1;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class StoreClientRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $extra_fields = Auth::user()->company->client_extra_fields->where('view', true);
        $extra_fields_rules = $extra_fields->flatMap(function ($field, $key) {
            return [
                "extra.$key.id" => 'required|in:' . $field->id,
                "extra.$key.value" => $field->required ? 'required' : '',
            ];
        })->all();

        return array_merge([
            'recaptcha' => 'recaptcha',

            'addressess.*.country' => '',
            'addressess.*.state' => '',
            'addressess.*.city' => '',
            'addressess.*.zip_code' => '',
            'addressess.*.street_address' => '',
            'addressess' => 'array',
            'phone_number' => 'numeric|nullable',
            'email' => 'nullable|email',
            'occupation' => '',
            // 'social_security_number' => 'required|unique:clients,social_security_number',
            // 'social_security_number' => 'nullable|unique:clients,social_security_number',
            'last_name' => 'required',
            'first_name' => 'required',
            'personal_id' => 'nullable',
            'profile_picture' => 'sometimes|image|mimes:jpeg,jpg,png',
            'extra' => 'array|size:' . $extra_fields->count(),
        ], $extra_fields_rules);
    }
}
