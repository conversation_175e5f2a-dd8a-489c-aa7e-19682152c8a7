<?php

namespace App\Http\Requests\v1;

use App\Treatment;
use Illuminate\Foundation\Http\FormRequest;

class TreatmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            // 'cost' => 'required|numeric|between:0.01,999999.99',
            'cost' => 'required_if:type,'.Treatment::TYPE_TREATMENT.'|nullable|numeric|between:0,999999.99',
            'type'=> 'in:'.Treatment::TYPE_TREATMENT.','.Treatment::TYPE_TEXT,
            'description' => 'nullable',
            'notes' => 'sometimes',
            'name' => 'required',
            'unit' => 'in:eur,usd,sek,gbp',
        ];
    }
}
