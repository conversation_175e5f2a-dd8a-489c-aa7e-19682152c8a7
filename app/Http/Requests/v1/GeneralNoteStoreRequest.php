<?php

namespace App\Http\Requests\v1;

use App\Rules\IsValidString;
use Illuminate\Foundation\Http\FormRequest;

class GeneralNoteStoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'notes_html' => 'notes',
        ];
    }

    public function rules()
    {
        return [
            'title' => ['required', new IsValidString],
            'notes' => ['nullable', 'string', new IsValidString],
            'notes_html' => 'required_without:file|string',
            'files.*' => 'required|max:51200',
            'files' => 'required_without:notes_html|min:1|array',
        ];
    }
}