<?php

namespace App\Http\Requests\v1;

use App\Plan;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Foundation\Http\FormRequest;

class StoreSubscriptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $plans = Cache::rememberForever('plans-key', function () {
            return Plan::get();
        });

        $plan = $plans->where('plan_id', $this->plan)->first();

        if (!$plan) {
            throw app(ModelNotFoundException::class)->setModel(app(Plan::class));
        }

        if ($plan->isFree()) {
            return [
                'plan' => 'required|bail',
                'quantity' => 'required',
            ];
        }

        if (Auth::user()->company->card_last_four) {
            return [
                'payment_method' => '',
                'payment_method.paymentMethod' =>  '',
                'payment_method.billing_details' => '',
                'payment_method.billing_details.address' => '',
                'payment_method.billing_details.address.city' => '',
                'payment_method.billing_details.address.country' => '',
                'payment_method.billing_details.address.line1' => '',
                'payment_method.billing_details.address.postal_code' => '',
                'payment_method.billing_details.email' => '',
                'payment_method.billing_details.phone' => '',
                'payment_method.billing_details.name' => '',
                'payment_method.metadata.company_name' => '',
                'payment_method.metadata.vat_number' => '',
                'plan' => 'required|bail',
                'quantity' => 'required',
            ];
        }

        return [
            'payment_method' => 'required',
            'payment_method.paymentMethod' =>  'required',
            'payment_method.billing_details' => 'required',
            'payment_method.billing_details.address' => 'required',
            'payment_method.billing_details.address.city' => 'required',
            'payment_method.billing_details.address.country' => '',
            'payment_method.billing_details.address.line1' => 'required',
            'payment_method.billing_details.address.postal_code' => 'required',
            'payment_method.billing_details.email' => '',
            'payment_method.billing_details.phone' => 'required',
            'payment_method.billing_details.name' => 'required',
            'payment_method.metadata.company_name' => 'required',
            'payment_method.metadata.vat_number' => '',
            'plan' => 'required|bail',
            'quantity' => 'required',
        ];
    }
}